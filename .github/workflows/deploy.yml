name: Deploy to Cloudflare Pages

on:
  push:
    branches:
      - main
      - nuxt  # 根据你的主分支名称调整

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build project
        run: npm run build
        
      - name: Deploy to Cloudflare Pages
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: ding-frontend  # 替换为你的 Cloudflare Pages 项目名称
          directory: .output/public
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}
