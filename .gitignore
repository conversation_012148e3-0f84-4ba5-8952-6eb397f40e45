# Nuxt dev/build outputs
.output
.data
.nuxt
.nitro
.cache
dist
out

# Build artifacts
*.tar.gz
*.zip

# Cloudflare Pages
.wrangler

# Node dependencies
node_modules

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Misc
.DS_Store
.fleet
.idea
.vscode

# Local env files
.env
.env.*
!.env.example

# TypeScript
*.tsbuildinfo

# Next.js (if used in the future)
.next

# Generated files
public/sw.js
public/workbox-*.js
