# ESLint 和 Prettier 代码格式化指南

本项目已配置 ESLint 和 Prettier 用于代码格式化和质量检查。以下是使用指南：

## 安装依赖

首次设置时，需要安装相关依赖：

```bash
npm install
# 或
yarn
# 或
pnpm install
```

## 命令行格式化

### 检查代码质量

```bash
npm run lint
# 或
yarn lint
# 或
pnpm lint
```

### 自动修复 ESLint 问题

```bash
npm run lint:fix
# 或
yarn lint:fix
# 或
pnpm lint:fix
```

### 使用 Prettier 格式化代码

```bash
npm run format
# 或
yarn format
# 或
pnpm format
```

## VS Code 集成

项目已配置 VS Code 设置，支持以下功能：

1. **保存时自动格式化**：当保存文件时，会自动应用 Prettier 格式化
2. **保存时自动修复 ESLint 问题**：当保存文件时，会自动修复可修复的 ESLint 问题
3. **文件类型特定格式化**：为不同类型的文件（Vue、JavaScript、TypeScript、JSON）配置了特定的格式化规则

### 推荐的 VS Code 扩展

为了获得最佳体验，请安装以下 VS Code 扩展：

- ESLint (`dbaeumer.vscode-eslint`)
- Prettier (`esbenp.prettier-vscode`)
- Vetur (`octref.vetur`) - Vue 工具支持

## 配置说明

### ESLint 配置

ESLint 配置文件位于 `.eslintrc.js`，主要规则包括：

- 使用 Vue 3 推荐的规则集
- TypeScript 支持
- 与 Prettier 集成，避免冲突
- 自定义规则，如关闭多词组件名称要求等

### Prettier 配置

Prettier 配置文件位于 `.prettierrc`，主要设置包括：

- 不使用分号
- 使用单引号
- 缩进使用 2 个空格
- 行宽限制为 100 字符
- 箭头函数参数尽可能省略括号

## 忽略文件

某些文件和目录已被配置为忽略 ESLint 和 Prettier 检查：

- `.eslintignore` 和 `.prettierignore` 文件中列出了这些文件和目录
- 主要包括构建输出、依赖项和锁文件

## 常见问题解决

### ESLint 错误无法自动修复

某些 ESLint 错误需要手动修复，特别是类型相关的错误。请查看错误消息并手动修复。

### 格式化与团队风格不一致

如果团队的代码风格与当前配置不一致，可以调整 `.prettierrc` 文件中的设置，然后运行 `npm run format` 重新格式化所有文件。 