# 自动部署说明

本项目配置了自动构建和部署功能，可以通过以下两种方式实现自动部署：

## 方式一：Cloudflare Pages 直接集成

Cloudflare Pages 可以直接与 GitHub 仓库集成，无需额外配置 GitHub Actions。

### 设置步骤

1. 按照 `cloudflare-pages-setup.md` 文件中的详细说明设置 Cloudflare Pages
2. 确保仓库中包含 `cloudflare-pages.toml` 配置文件
3. 完成设置后，每次推送代码到主分支都会自动触发构建和部署

### 优点

- 设置简单，无需配置 GitHub Secrets
- Cloudflare 提供免费的构建分钟数
- 自动为每个 PR 创建预览环境
- 内置 CDN 和 SSL

## 方式二：GitHub Actions

如果你需要更多自定义选项或想使用其他部署目标，可以使用 GitHub Actions。

### 设置步骤

1. 在 GitHub 仓库设置中添加以下 Secrets：
   - `CLOUDFLARE_API_TOKEN`: Cloudflare API 令牌
   - `CLOUDFLARE_ACCOUNT_ID`: Cloudflare 账户 ID
   
2. 确保仓库中包含 `.github/workflows/deploy.yml` 文件
3. 推送代码到主分支时，GitHub Actions 将自动构建并部署到 Cloudflare Pages

### 获取 Cloudflare API 令牌和账户 ID

1. 登录 Cloudflare Dashboard
2. 在右上角点击你的个人资料，然后选择 "我的个人资料"
3. 在左侧菜单中选择 "API 令牌"
4. 创建一个新的 API 令牌，确保它有 "Cloudflare Pages:Edit" 权限
5. 账户 ID 可以在 Cloudflare Dashboard 的右侧边栏底部找到

### 优点

- 更灵活的构建和部署流程
- 可以添加额外的测试和验证步骤
- 可以轻松切换到其他部署平台

## 本地构建

如果你需要在本地构建项目，可以使用提供的 `build.sh` 脚本：

```bash
# 添加执行权限
chmod +x build.sh

# 运行脚本
./build.sh
```

构建完成后，静态文件将位于 `.output/public` 目录中。

## 注意事项

1. 确保 `nuxt.config.ts` 中的 API 配置正确设置为使用线上 API
2. 确保 `utils/request.ts` 中的认证绕过配置正确
3. 如果部署到子目录，需要在 `nuxt.config.ts` 中设置 `app.baseURL` 属性
