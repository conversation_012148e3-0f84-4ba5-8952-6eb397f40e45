# 静态构建说明文档

## 构建过程

### 1. 配置修改

在构建静态文件之前，需要修改以下配置文件：

1. **nuxt.config.ts**
   - 添加静态构建配置
   - 设置API代理规则
   - 禁用预渲染以避免图片问题

2. **utils/request.ts**
   - 添加固定的userid头部用于API认证绕过
   - 确保所有API请求指向正确的线上域名

3. **src/config/api.ts**
   - 确保开发环境也使用线上API域名

### 2. 构建命令

```bash
# 构建项目
npm run build

# 预览构建结果
cd .output/public && python3 -m http.server 3000
# 然后访问 http://localhost:3000
```

### 3. 部署

将 `.output/public` 目录中的所有文件复制到服务器的网站根目录即可。

## 本次改动内容

### 1. nuxt.config.ts 修改

```javascript
// 添加静态构建配置
nitro: {
  devProxy: {
    '/api': {
      target: 'http://qingke.aihe.space/api',
      changeOrigin: true,
      prependPath: true,
    },
  },
  // 配置静态生成
  preset: 'static',
  // 禁用预渲染以避免图片问题
  prerender: false,
  // 设置生产环境API URL
  routeRules: {
    '/api/**': {
      proxy: 'http://qingke.aihe.space/api/**'
    }
  }
},
```

### 2. utils/request.ts 修改

```typescript
onRequest({ options }) {
  // 添加固定的userid头部用于API认证绕过
  // 使用any类型断言避免TypeScript错误
  const headers = options.headers as any
  headers.userid = 'gAckWxWYazcI5k95n627hRBHB712'
},
```

### 3. src/config/api.ts 修改

```typescript
// 默认配置(开发环境)
const defaultConfig: ApiConfig = {
  baseUrl: 'http://qingke.aihe.space',
  endpoints: {
    stream: '/api/stream',
  }
};
```

## 注意事项

1. 所有API请求都会发送到 `http://qingke.aihe.space/api`
2. 所有请求都会自动添加 `userid: gAckWxWYazcI5k95n627hRBHB712` 头部以绕过认证
3. 静态文件可以部署到任何静态文件服务器上
4. 如果部署到子目录，需要在 `nuxt.config.ts` 中设置 `app.baseURL` 属性
