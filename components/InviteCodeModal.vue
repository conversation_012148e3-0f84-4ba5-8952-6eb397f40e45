<template>
  <div
    class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 px-4"
    style="z-index: 1000"
  >
    <div
      class="bg-white dark:bg-[#1a1a1b] rounded-2xl shadow-xl p-8 md:p-8 p-6 w-full relative text-center max-w-[545px]"
    >
      <button
        class="absolute top-6 right-6 text-2xl text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 bg-white dark:bg-[#1a1a1b]"
        @click="$emit('close')"
      >
        <span aria-label="Close">&times;</span>
      </button>
      <!-- <button
        class="absolute top-5 right-5 text-2xl text-gray-400 hover:text-gray-600 transition-colors"
        @click="$emit('close')"
      >
        <img src="/image/close.png" alt="Close" class="w-6 h-6" />
      </button> -->
      <div class="text-2xl md:text-2xl text-xl mb-4 text-left text-black dark:text-white" style="font-weight: 600; font-size: 18px">
        Enter Invite Code
      </div>
      <div class="text-base md:text-base text-sm text-left mb-6 text-gray-700 dark:text-gray-300" style="font-size: 14px">
        Welcome! Please enter the invite code you received to continue.
      </div>
      <input
        v-model="inviteCode"
        type="text"
        placeholder="Invite Code"
        :class="[
          'w-full border rounded-lg px-4 py-3 text-base md:text-base text-sm mb-2 focus:outline-none placeholder-gray-400 dark:placeholder-gray-500',
          'bg-gray-50 dark:bg-gray-800 text-black dark:text-white',
          error ? 'border-red-500' : 'border-gray-200 dark:border-gray-600 focus:border-black/80 dark:focus:border-white/80',
        ]"
        :disabled="loading"
      />
      <div v-if="error" class="text-red-500 text-sm text-left mb-5">{{ error }}</div>
      <div class="flex gap-3 mt-6 flex-col md:flex-row">
        <button
          class="px-6 py-3 border border-gray-200 dark:border-gray-600 text-gray-600 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors bg-white dark:bg-gray-800 text-base md:text-base text-sm"
          @click="$emit('close')"
          :disabled="loading"
        >
          Cancel
        </button>
        <button
          class="px-6 py-3 bg-black dark:bg-white text-white dark:text-black rounded-lg hover:bg-gray-900 dark:hover:bg-gray-200 transition-colors flex-1 text-base md:text-base text-sm"
          @click="$emit('submit', inviteCode)"
          :disabled="loading || !inviteCode.trim()"
        >
          <span v-if="loading">Submitting...</span>
          <span v-else>Submit</span>
        </button>
      </div>
      <div class="mt-6 text-center">
        <div class="text-gray-500 dark:text-gray-400 text-sm mb-2">Don't have an invite code?</div>
        <button
          v-if="!hasJoinedWaitingList"
          class="text-black dark:text-white underline hover:no-underline transition-all text-sm"
          @click="$emit('waiting-list')"
        >
          Join the waiting list
        </button>
        <div v-else class="text-sm text-gray-500 dark:text-gray-400">
          You're already on the waiting list. We'll notify you when access is available.
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { getWaitingListStatus } from '@/api/index'
  const { currentUser } = useFirebaseAuth()

  const inviteCode = ref('')
  const hasJoinedWaitingList = ref(false)

  onMounted(async () => {
    try {
      const res = await getWaitingListStatus('/api/waiting-list/status', {
        headers: {
          userid: currentUser.value?.uid,
        },
      })
      console.log('getWaitingListStatus', res)
      hasJoinedWaitingList.value = !!(res && res.data.data.entry.status === 'joined')
    } catch (e) {
      hasJoinedWaitingList.value = false
    }
  })

  defineProps<{
    error?: string
    loading?: boolean
  }>()

  defineEmits<{
    (e: 'close'): void
    (e: 'submit', code: string): void
    (e: 'waiting-list'): void
  }>()
</script>

<style scoped>
  input::placeholder {
    color: #bdbdbd;
    opacity: 1;
  }

  button:disabled {
    cursor: not-allowed;
  }

  /* 移动端优化 */
  @media (max-width: 640px) {
    .rounded-2xl {
      border-radius: 1rem;
    }
  }
</style>

