<template>
  <motion.div
    class="mt-16 f-cer w-full"
    :initial="{ opacity: 0, y: 10 }"
    :animate="{ opacity: 1, y: 0 }"
  >
    <div
      class="max-w-172 w-full mx-auto dark:bg-[#141415] bg-[#FBF7F5] px-4 sm:px-8 md:px-11 py-6 rel backdrop-blur profile-card-border"
      style="border-radius: 15px;"
      :data-card-id="'profile-card'"
    >
      <div class="abs left-1/2 -translate-x-1/2 -top-11">
        <Avatar :src="profile.avatar" :size="90" />
      </div>
      <div class="mt-8 flex flex-col items-center gap-3">
        <div class="font-bold text-xl sm:text-2xl text-center text-black dark:text-[#FAF9F5]">
          {{ profile.name }}
        </div>
        <div class="text-sm text-[#7C7C7C] text-center px-2">
          {{ profile.affiliation }}
        </div>
        <div class="text-sm text-[#433D3A] dark:text-[#C6C6C6] text-center min-h-15 px-2">
          {{ profile.description }}
        </div>
        <div class="flex items-center gap-2 sm:gap-2.5 w-full flex-wrap justify-center px-2">
          <Tag :title="tag" v-for="(tag, index) in profile.researchFields" :key="index" />
        </div>
        <div class="mt-5 cursor-pointer" @click="$emit('share-click')">
          <div class="h-[42px] w-[121px] text-4 font-600 text-white bg-[#CB7C5D] dark:bg-[#654D43] hover:bg-[#CB7C5D]/80 dark:hover:bg-[#654D43]/80 transition-colors fx-cer justify-center rounded-full gap-2">
            <div class="i-proicons:x-twitter wh-5 font-600"></div>
            Share
          </div>
        </div>
      </div>
    </div>

    <Teleport to="body">
      <div
        v-show="showShareCard"
        style="position: fixed; left: -9999px; top: -9999px; z-index: -1"
        ref="shareContainerRef"
      >
        <ShareProfileCard :dataBlocks="dataBlocks" ref="shareCardRef" />
      </div>
      <!-- 本地调试弹窗 -->
      <div
        v-if="showDebugDialog"
        class="fixed inset-0 z-50 flex items-center justify-center bg-black/40"
      >
        <div
          class="bg-white rounded-2xl p-8 shadow-xl relative max-w-[90vw] max-h-[90vh] overflow-auto"
        >
          <button class="absolute top-2 right-2 text-xl" @click="showDebugDialog = false">×</button>
          <ShareProfileCard :dataBlocks="dataBlocks" />
        </div>
      </div>
    </Teleport>
  </motion.div>
</template>

<script setup lang="ts">
  import { type ResearcherInfo } from '~/api/types'
  import { motion } from 'motion-v'
  import ShareArea from '../ShareArea/index.vue'
  import ShareProfileCard from '../ShareProfileCard.vue'
  import { computed } from 'vue'

  const props = defineProps<{
    profile: ResearcherInfo
    allInfo: any
  }>()

  const emit = defineEmits<{
    'share-click': []
  }>()

  const showShareCard = ref(false)
  const shareCardRef = ref<any>(null)
  const shareContainerRef = ref<HTMLElement | null>(null)
  const showDebugDialog = ref(false)

  // 获取 dataBlocks 数据
  const dataBlocks = computed(
    () => (props.allInfo as any).dataBlocks || (props.allInfo as any).researcherProfile?.dataBlocks
  )
</script>

<style scoped>
  @media (max-width: 640px) {
    .max-w-172 {
      max-width: calc(100vw - 2rem);
    }
  }

  @media (min-width: 641px) {
    .max-w-172 {
      max-width: 43rem; /* 172/4 = 43rem */
    }
  }

  .profile-card-border {
    position: relative;
  }

  .profile-card-border::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 1px;
    background: linear-gradient(180deg, #FFFFFF 0%, rgba(255, 255, 255, 0.4) 100%);
    border-radius: 15px;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    pointer-events: none;
  }

  .dark .profile-card-border {
    border: 1px solid #27282D;
  }

  .dark .profile-card-border::before {
    display: none;
  }
</style>
