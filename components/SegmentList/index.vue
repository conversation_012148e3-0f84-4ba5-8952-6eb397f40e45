<template>
  <div class="grid grid-cols-3 gap-4">
    <div v-for="(item, index) in items" :key="index"
      class="relative flex flex-col gap-2 pr-4 justify-center">
      <span class="text-black dark:text-white" style="font-family: 'UDC 1.04'; font-weight: 700; font-size: 24px; line-height: 1.5;">{{ formatThousand(item.value) }}</span>
      <span class="text-xs text-gray-1200">{{ item.label }}</span>
      <div v-if="index < items.length - 1 && (index + 1) % 3 !== 0"
        class="absolute right-0 top-1/2 -translate-y-1/2 w-px bg-gray-1300" style="height: 70%;"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  items: {
    label: string;
    value: string | number;
  }[];
}>();
</script>