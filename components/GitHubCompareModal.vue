<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
    <div
      class="relative bg-white rounded-3xl shadow-2xl p-8 pt-7 w-[540px] max-w-full mx-4 dark:bg-[#141415] dark:border dark:border-[#27282D] dark:shadow-[0px_3px_8px_0px_#0000001A]"
      style="box-shadow: 0 16px 64px 0 rgba(0, 0, 0, 0.18)"
    >
      <button
        class="absolute top-6 right-6 text-2xl text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-100 bg-transparent"
        @click="$emit('close')"
      >
        <span aria-label="Close">&times;</span>
      </button>
      <div
        class="text-2xl font-extrabold mb-6 text-black dark:text-white"
      >
        Compare
      </div>
      <div
        class="rounded-2xl flex items-center gap-4 px-5 py-4 mb-4"
        style="background: url('/image/comparecard.png') center/cover no-repeat"
      >
        <div class="relative flex items-center justify-center">
          <NuxtImg
            v-show="!imageError"
            :src="developer.image"
            preload
            :alt="developer.name"
            class="w-14 h-14 rounded-full object-cover"
            @error="handleImageError"
          />
          <img
            v-show="imageError"
            src="/image/avator.png"
            :alt="developer.name"
            class="w-14 h-14 rounded-full object-cover"
          />
        </div>
        <div class="flex flex-col">
          <span class="font-extrabold text-lg text-black mb-1 dark:text-white">{{
            developer.name
          }}</span>
          <span class="text-gray-600 text-sm leading-snug dark:text-[#7A7A7A]">
            {{ developer.Company }}, {{ developer.Job }}
          </span>
        </div>
      </div>
      <div class="mb-6 text-center text-base text-gray-700 dark:text-[#C6C6C6]">
        Wanna compare <span class="text-[#CB7C5D] font-bold">{{ developer.name }}</span> with other GitHub developers?<br />
        Just input their GitHub username
      </div>
      <form
        @submit.prevent="handleCompare"
        class="custom-input border rounded-full bg-white border-black min-h-16 p-1 flex items-center justify-between gap-4 pl-7.5"
      >
        <svg
          class="text-gray-400"
          width="22"
          height="22"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          viewBox="0 0 24 24"
        >
          <circle cx="11" cy="11" r="8" />
          <path d="M21 21l-4.35-4.35" />
        </svg>
        <input
          v-model="input"
          type="text"
          class="flex-1 bg-transparent outline-none px-2 text-base font-bold placeholder-gray-400 placeholder:text-sm placeholder:font-normal text-black"
          placeholder="GitHub username"
        />
        <button
          type="submit"
          class="fx-cer gap-2 py-4 px-7 rounded-full font-bold bg-black-100 text-white hover:bg-black-100/90 transition-colors"
          
        >
          <img src="/image/stars.png" alt="compare" class="w-5 h-5" />
          Compare
        </button>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, defineProps, defineEmits } from 'vue'
  
  interface GitHubDeveloper {
    name: string
    Job: string
    image: string
    Company: string
    github: string
    has_github: boolean
  }
  
  const props = defineProps<{
    developer: GitHubDeveloper
  }>()
  
  const emit = defineEmits<{
    close: []
    compare: [data: { left: GitHubDeveloper; right: string }]
  }>()
  
  const input = ref('')
  const imageError = ref(false)
  
  function handleImageError() {
    imageError.value = true
  }
  
  function handleCompare() {
    if (!input.value) return
    emit('compare', { left: props.developer, right: input.value })
  }
</script>

<style scoped>
  :where(.dark) .rounded-2xl {
    background: url('/image/comparecard-dark.png') center/cover no-repeat !important;
  }

  .custom-input {
    transition: all 0.3s ease;
  }

  .custom-input:focus-within {
    box-shadow: 0 0 0 3px rgba(203, 124, 93, 0.1);
  }

  .fx-cer {
    display: flex;
    align-items: center;
  }

  .bg-black-100 {
    background-color: #000000;
  }

  .bg-black-100:hover {
    background-color: rgba(0, 0, 0, 0.9);
  }
</style>
