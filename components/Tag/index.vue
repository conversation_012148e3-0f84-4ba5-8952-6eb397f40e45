<template>
  <div :class="[
    'inline-flex select-none cursor-pointer px-2.5 text-xs py-4.5px items-center justify-center rounded border transition-colors tag-component',
    variantClass
  ]">
    {{ title }}
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  variant: {
    type: String,
    default: 'default',
  }
})

const variantClass = computed(() => {
  switch (props.variant) {
    case 'primary':
      return 'border-primary-100 text-primary-100 bg-special-lightPink hover:bg-special-lightPink/50 dark:bg-[#323232] dark:text-[#C6C6C6] dark:border-[#3E3E3E]';
    case 'white':
      return 'border-transparent text-white bg-[#6E7DA4]/80';
    case 'brown':
      return 'border-transparent text-white bg-[#8F776D]/50';
    case 'share-blue':
      return 'bg-[#EEF1F9] border-[#7F8EB7] text-[#7F8EB7] dark:bg-[#3C4356] dark:border-[#7F8EB7] dark:text-[#C2C5CE] border-[0.5px] !text-[#7F8EB7] dark:!text-[#C2C5CE]';
    case 'share-orange':
      return 'bg-[#FBEAE3] border-[#CB7C5D] text-[#CB7C5D] dark:bg-[#413834] dark:border-[#71635E] dark:text-[#E1BCAD] border-[0.5px] !text-[#CB7C5D] dark:!text-[#E1BCAD]';
    default:
      return 'border-primary-100 text-primary-100 bg-special-lightPink hover:bg-special-lightPink/50 dark:bg-[#323232] dark:text-[#C6C6C6] dark:border-[#3E3E3E]';
  }
})
</script>

<style scoped>
.dark .tag-component {
  border-width: 0.5px;
}
</style>