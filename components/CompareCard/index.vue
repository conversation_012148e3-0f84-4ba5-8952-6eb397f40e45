<template>
  <div class="flex rel text-white w-full max-w-full overflow-hidden">
    <motion.div class="flex-1 compare1 rel min-w-0" :initial="{ x: -50, opacity: 0 }" :animate="{ x: 0, opacity: 1 }">
      <div class="hidden sm:block abs top-15 right-7.5">
        <img src="~/assets/image/v.png" class="w-12 sm:w-16" />
      </div>
      <div class="px-6 sm:px-8 md:px-12 flex items-center flex-col">
        <div class="-mt-7">
          <Avatar :src="avatar" :size="70" />
        </div>
        <div class="text-xl sm:text-2xl font-bold mt-4 text-center">
          {{ leftName }}
        </div>
        <div class="fx-cer text-sm gap-1.5 mt-2 text-center opacity-80">
          <SvgIcon name="auth-white" />
          <span>{{ leftTitle }}</span>
        </div>
        <div class="fx-cer gap-2 sm:gap-2.5 flex-wrap justify-center mt-4">
          <Tag v-for="tag in leftTags" :key="tag" variant="white" :title="tag" />
        </div>
        <div class="mt-6 text-center">
          <p class="text-sm mb-4">Wanna compare <span class="text-primary-300">{{ leftName }}</span> with other AI researchers?</p>
          <p class="text-xs opacity-80">Just input their name, or Google Scholar link</p>
        </div>
        <div class="flex items-center gap-2 mt-4 w-full">
          <button class="p-2 rounded-lg border border-white/20" @click="resetLeftInput">
            <SvgIcon name="refresh" class="w-5 h-5" />
          </button>
          <div class="flex-1 relative">
            <input 
              v-model="leftInput"
              type="text" 
              placeholder="Twitter handle, or Google Scholar link" 
              class="w-full py-2 px-4 rounded-lg bg-black/10 border border-white/20 text-sm placeholder:text-white/50"
            />
          </div>
          <button 
            class="py-2 px-4 rounded-lg bg-black/20 text-sm font-medium hover:bg-black/30 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            :disabled="!leftInput || isLoading"
            @click="handleLeftCompare"
          >
            {{ isLoading ? 'Comparing...' : 'Compare' }}
          </button>
        </div>
      </div>
    </motion.div>
    <motion.div :initial="{ opacity: 0 }" :animate="{ opacity: 1 }"
      :transition="{ delay: 0.2, duration: 0.8, ease: 'easeInOut' }"
      class="abs top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-2">
      <div class="relative">
        <img src="~/assets/image/flash.png" class="w-16 sm:w-20" />
        <span class="abs top-1/2 left-0 transform -translate-y-1/2 -translate-x-1/2 text-2xl font-bold">V</span>
        <span class="abs top-1/2 right-0 transform -translate-y-1/2 translate-x-1/2 text-2xl font-bold">S</span>
      </div>
    </motion.div>
    <motion.div class="flex-1 compare2 rel flex-col flex items-center min-w-0" :initial="{ x: 50, opacity: 0 }"
      :animate="{ x: 0, opacity: 1 }">
      <div class="hidden sm:block abs bottom-27.5 left-12">
        <img src="~/assets/image/s.png" class="w-12 sm:w-16" />
      </div>
      <div class="px-6 sm:px-8 md:px-12 flex items-center flex-col">
        <div class="-mt-7">
          <Avatar :src="avatar2" :size="70" />
        </div>
        <div class="text-xl sm:text-2xl font-bold mt-4 text-center">
          {{ rightName }}
        </div>
        <div class="fx-cer text-sm gap-1.5 mt-2 text-center opacity-80">
          <SvgIcon name="auth-white" />
          <span>{{ rightTitle }}</span>
        </div>
        <div class="fx-cer gap-2 sm:gap-2.5 flex-wrap justify-center mt-4">
          <Tag v-for="tag in rightTags" :key="tag" variant="brown" :title="tag" />
        </div>
        <div class="mt-6 text-center">
          <p class="text-sm mb-4">Wanna compare <span class="text-primary-400">{{ rightName }}</span> with other AI researchers?</p>
          <p class="text-xs opacity-80">Just input their name, or Google Scholar link</p>
        </div>
        <div class="flex items-center gap-2 mt-4 w-full">
          <button class="p-2 rounded-lg border border-white/20" @click="resetRightInput">
            <SvgIcon name="refresh" class="w-5 h-5" />
          </button>
          <div class="flex-1 relative">
            <input 
              v-model="rightInput"
              type="text" 
              placeholder="Twitter handle, or Google Scholar link" 
              class="w-full py-2 px-4 rounded-lg bg-black/10 border border-white/20 text-sm placeholder:text-white/50"
            />
          </div>
          <button 
            class="py-2 px-4 rounded-lg bg-black/20 text-sm font-medium hover:bg-black/30 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            :disabled="!rightInput || isLoading"
            @click="handleRightCompare"
          >
            {{ isLoading ? 'Comparing...' : 'Compare' }}
          </button>
        </div>
      </div>
    </motion.div>
  </div>
</template>

<script setup lang="ts">
import { motion } from "motion-v"
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const props = defineProps<{
  leftName: string;
  rightName: string;
  leftTitle: string;
  rightTitle: string;
  leftTags: string[];
  rightTags: string[];
  avatar: string;
  avatar2: string;
}>();

const emit = defineEmits<{
  (e: 'compare', data: { source: string; target: string }): void
}>();

const leftInput = ref('')
const rightInput = ref('')
const isLoading = ref(false)

const resetLeftInput = () => {
  leftInput.value = ''
}

const resetRightInput = () => {
  rightInput.value = ''
}

const handleLeftCompare = async () => {
  if (!leftInput.value || isLoading.value) return
  
  try {
    isLoading.value = true
    // 发出比较事件，让父组件处理具体的比较逻辑
    emit('compare', {
      source: props.leftName,
      target: leftInput.value
    })
    
    // 可以直接跳转到新的比较页面
    await router.push({
      path: '/compare',
      query: {
        source: props.leftName,
        target: leftInput.value
      }
    })
  } catch (error) {
    console.error('Compare failed:', error)
  } finally {
    isLoading.value = false
  }
}

const handleRightCompare = async () => {
  if (!rightInput.value || isLoading.value) return
  
  try {
    isLoading.value = true
    // 发出比较事件，让父组件处理具体的比较逻辑
    emit('compare', {
      source: props.rightName,
      target: rightInput.value
    })
    
    // 可以直接跳转到新的比较页面
    await router.push({
      path: '/compare',
      query: {
        researcher1: props.rightName,
        researcher2: rightInput.value
      }
    })
  } catch (error) {
    console.error('Compare failed:', error)
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.compare1 {
  background-image: url('~/assets/image/compare1.png');
  background-size: cover;
  background-repeat: no-repeat;
  min-height: 440px;
  margin-right: -20px;
  border-radius: 24px;
  padding: 24px;
}

.compare2 {
  background-image: url('~/assets/image/compare2.png');
  background-size: cover;
  background-repeat: no-repeat;
  min-height: 440px;
  margin-left: -20px;
  border-radius: 24px;
  padding: 24px;
}

@media (min-width: 640px) {
  .compare1 {
    margin-right: -36px;
  }
  .compare2 {
    margin-left: -36px;
  }
}

@media (max-width: 639px) {
  .compare1, .compare2 {
    background-size: cover;
    background-position: center;
  }
}
</style>