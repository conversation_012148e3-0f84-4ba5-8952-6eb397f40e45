<template>
  <div class="bg-white/60 border border-white rounded-2xl p-7.5 backdrop-blur">
    <div class="flex flex-col items-center">
      <div class="clash-display text-42px text-gray-500 max-w-547px text-center leading-tight">
        Wanna compare
        <span class="text-primary-100">
          {{ researcherName }}
        </span>
        with other AI researchers?
      </div>
      <div class="text-base text-gray-1100 mt-4 font-normal">Just input their name, or Google Scholar link</div>
      <div class="f-cer mt-7.5 mb-7">
        <div class="border rounded-full bg-white border-black min-h-16 w-190 flex items-center justify-between gap-4 pl-7.5 shadow-[0_2px_4px_rgba(0,0,0,0.1)] hover:shadow-[0_4px_8px_rgba(0,0,0,0.15)] transition-shadow">
          <input 
            v-model="searchValue" 
            type="text" 
            class="flex-1 bg-transparent outline-none text-base placeholder:text-gray-400" 
            placeholder="Enter researcher name or link"
            @keyup.enter="handleCompare"
          />
          <button 
            class="h-14 bg-black text-white rounded-full py-2 px-6 flex items-center gap-2.5 transition-all hover:bg-black/90 hover:scale-[1.02] active:scale-[0.98]"
            @click="handleCompare"
          >
            <!-- <img src="/image/stars.png" alt="Compare" class="w-5 h-5" /> -->
            <span class="text-sm font-medium">Compare</span>
          </button>
        </div>
      </div>
      <div class="text-sm text-center text-gray-400">
        By clicking "Compare," you agree to our <a href="/terms" target="_blank" class="text-primary-100 hover:underline">Terms of Service</a>.
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const props = defineProps<{
  researcherName: string;
}>();

const router = useRouter();
const searchValue = ref('');

const handleCompare = () => {
  if (!searchValue.value?.trim()) return;
  
  router.push({
    path: '/compare',
    query: {
      researcher1: props.researcherName,
      researcher2: searchValue.value
    }
  });
};
</script>

<style scoped>
.clash-display {
  font-family: 'Clash Display', sans-serif;
  font-weight: 600;
  letter-spacing: -0.02em;
}

.text-42px {
  font-size: 42px;
}

.text-primary-100 {
  color: #C69279;
}

/* 输入框聚焦时的效果 */
input:focus + button {
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* 响应式调整 */
@media (max-width: 640px) {
  .text-42px {
    font-size: 32px;
  }
  
  .w-190 {
    width: 100%;
    max-width: 190px;
  }
}

/* 确保背景模糊效果在所有浏览器中正常工作 */
.backdrop-blur {
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
}
</style>
