<template>
  <div class="radar-chart" :class="{ 'large-size': props.size === 'large', 'small-size': props.size === 'small' }">
    <Radar 
      ref="chartInstance" 
      :data="chartData" 
      :options="chartOptions" 
              :class="props.size === 'large' ? 'w-[689px] h-[429px]' : props.size === 'small' ? 'w-[600px] h-[350px]' : 'w-full h-[400px]'" 
    />
  </div>
</template>

<script setup lang="ts">
  import {
    Chart as ChartJS,
    RadialLinearScale,
    PointElement,
    LineElement,
    Filler,
    Tooltip,
    Legend,
  } from 'chart.js'
  import { Radar } from 'vue-chartjs'
  import { computed, ref, onMounted, watch } from 'vue'

  ChartJS.register(RadialLinearScale, PointElement, LineElement, Filler, Tooltip, Legend)

  interface GitHubUser {
    name: string
    code_contribution?: {
      total: number
    }
    overview?: {
      active_days: number
      pull_requests: number
    }
    top_projects?: Array<{
      repository: {
        name: string
        stargazerCount?: number
      }
    }>
    feature_project?: {
      stargazerCount: number
    }
  }

  const props = defineProps<{
    user1: GitHubUser
    user2: GitHubUser
    size?: 'default' | 'large' | 'small'
    hideLegend?: boolean
    isShareCard?: boolean
  }>()
  
  // 获取主题颜色
  const isDark = ref(false)

  // 检测深色模式变化 - 仅在客户端执行
  const updateDarkMode = () => {
    if (process.client) {
      isDark.value = document.documentElement.classList.contains('dark')
    }
  }

  const chartInstance = ref<any>(null)

  // 更新图表
  const updateChart = () => {
    if (chartInstance.value && chartInstance.value.chart) {
      // 通过vue-chartjs的chart属性访问底层Chart.js实例
      const chart = chartInstance.value.chart
      chart.options.scales.r.pointLabels.color = isDark.value ? '#C6C6C6' : '#000000'
      
      // 为分享卡片设置特定的网格线颜色和线宽
      if (props.isShareCard) {
        chart.options.scales.r.grid.color = 'rgba(147, 145, 144, 0.5)' // #939190 with 50% opacity
        chart.options.scales.r.grid.lineWidth = 1 // 1px线宽
      } else {
        chart.options.scales.r.grid.color = isDark.value ? '#616161' : '#E8DEDC'
        chart.options.scales.r.grid.lineWidth = 2 // 2px线宽
      }
      
      // 更新图例文字颜色
      if (chart.options.plugins && chart.options.plugins.legend && chart.options.plugins.legend.labels) {
        chart.options.plugins.legend.labels.color = isDark.value ? '#C6C6C6' : '#333333'
      }
      
      chart.update('none') // 使用'none'模式避免动画
    }
  }

  onMounted(() => {
    // 初始化深色模式检测
    updateDarkMode()

    // 监听深色模式变化
    const observer = new MutationObserver(() => {
      updateDarkMode()
      updateChart() // 深色模式变化时更新图表
    })
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    })

    // 延迟一点时间确保图表已经初始化完成
    setTimeout(() => {
      updateChart()
    }, 100)
  })

  // 监听深色模式变化
  watch(isDark, () => {
    updateChart()
  })

  // 计算雷达图数据
  const chartData = computed(() => ({
    labels: [
      'Code Contribution',
      'Active Days',
      'Pull Requests',
      'Contributing Projects',
      'Featured Project Stars',
    ],
    datasets: [
      {
        label: props.user1.name,
        data: [
          normalizeValue(props.user1.code_contribution?.total || 0, dimensionMaxValues.value.codeContribution),
          normalizeValue(props.user1.overview?.active_days || 0, dimensionMaxValues.value.activeDays),
          normalizeValue(props.user1.overview?.pull_requests || 0, dimensionMaxValues.value.pullRequests),
          normalizeValue(getTopProjectsCount(props.user1), dimensionMaxValues.value.topProjects),
          normalizeValue(props.user1.feature_project?.stargazerCount || 0, dimensionMaxValues.value.featuredStars),
        ],
        fill: true,
        backgroundColor: 'rgba(127, 142, 183, 0.2)',
        borderColor: 'rgb(127, 142, 183)',
        pointBackgroundColor: 'rgb(127, 142, 183)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(127, 142, 183)',
      },
      {
        label: props.user2.name,
        data: [
          normalizeValue(props.user2.code_contribution?.total || 0, dimensionMaxValues.value.codeContribution),
          normalizeValue(props.user2.overview?.active_days || 0, dimensionMaxValues.value.activeDays),
          normalizeValue(props.user2.overview?.pull_requests || 0, dimensionMaxValues.value.pullRequests),
          normalizeValue(getTopProjectsCount(props.user2), dimensionMaxValues.value.topProjects),
          normalizeValue(props.user2.feature_project?.stargazerCount || 0, dimensionMaxValues.value.featuredStars),
        ],
        fill: true,
        backgroundColor: 'rgba(203, 124, 93, 0.2)',
        borderColor: 'rgb(203, 124, 93)',
        pointBackgroundColor: 'rgb(203, 124, 93)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(203, 124, 93)',
      },
    ],
  }))

  // 雷达图配置
  const chartOptions = computed(() => ({
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      r: {
        ticks: {
          display: false,
          stepSize: 25,
        },
        angleLines: {
          display: false,
        },
        grid: {
          color: props.isShareCard ? 'rgba(147, 145, 144, 0.5)' : '#E8DEDC', // 分享卡片使用固定颜色，其他由updateChart动态更新
          lineWidth: props.isShareCard ? 1 : 2, // 分享卡片使用1px，其他使用2px
        },
        pointLabels: {
          font: {
            size: 14,
            family: "'Poppins', sans-serif",
            weight: 'bold' as const,
          },
          color: '#000000', // 默认浅色，由updateChart动态更新
        },
        beginAtZero: true,
        max: 100,
      },
    },
    plugins: {
      legend: {
        display: !props.hideLegend,
        position: 'top' as const,
        labels: {
          font: {
            size: 14,
            family: "'Poppins', sans-serif",
            weight: 500,
          },
          padding: 35,
          usePointStyle: true,
          pointStyle: 'rect',
          boxWidth: 12,
          boxHeight: 12,
          borderRadius: 6,
          color: '#333333', // 默认颜色，由updateChart动态更新
          generateLabels: function(chart: any) {
            const original = ChartJS.defaults.plugins.legend.labels.generateLabels;
            const labels = original.call(this, chart);
            
            labels.forEach((label: any, index: number) => {
              if (index === 0) {
                // 第一个用户 - 蓝色系 #7F8EB7
                label.fillStyle = 'rgba(127, 142, 183, 0.8)';
                label.strokeStyle = 'rgb(127, 142, 183)';
              } else {
                // 第二个用户 - 橙色系 #CB7C5D
                label.fillStyle = 'rgba(203, 124, 93, 0.8)';
                label.strokeStyle = 'rgb(203, 124, 93)';
              }
              label.lineWidth = 2;
            });
            
            return labels;
          },
        },
      },
      tooltip: {
        enabled: true,
        mode: 'index' as const,
        bodyFont: {
          family: "'Poppins', sans-serif",
        },
        titleFont: {
          family: "'Poppins', sans-serif",
        },
        callbacks: {
          label: (context: any) => {
            const label = context.dataset.label || ''
            const datasetIndex = context.datasetIndex
            const valueIndex = context.dataIndex
            const originalValue = getOriginalValue(datasetIndex, valueIndex)
            const formattedValue = formatNumber(originalValue)
            return `${label}: ${formattedValue}`
          },
        },
      },
    },
  }))

  // 数据标准化处理函数
  const normalizeValue = (value: number, maxValue: number) => {
    if (!value) return 0
    if (maxValue === 0) return 0
    return Math.round((value / maxValue) * 100)
  }

  // 获取顶级项目数量
  const getTopProjectsCount = (user: GitHubUser) => {
    return user.top_projects?.length || 0
  }

  // 获取每个维度的最大值
  const dimensionMaxValues = computed(() => ({
    codeContribution: Math.max(
      props.user1.code_contribution?.total || 0,
      props.user2.code_contribution?.total || 0
    ) * 1.1,
    activeDays: Math.max(
      props.user1.overview?.active_days || 0,
      props.user2.overview?.active_days || 0
    ) * 1.1,
    pullRequests: Math.max(
      props.user1.overview?.pull_requests || 0,
      props.user2.overview?.pull_requests || 0
    ) * 1.1,
    topProjects: Math.max(
      getTopProjectsCount(props.user1),
      getTopProjectsCount(props.user2)
    ) * 1.1,
    featuredStars: Math.max(
      props.user1.feature_project?.stargazerCount || 0,
      props.user2.feature_project?.stargazerCount || 0
    ) * 1.1,
  }))

  // 获取原始值（用于tooltip显示）
  const getOriginalValue = (datasetIndex: number, valueIndex: number) => {
    const user = datasetIndex === 0 ? props.user1 : props.user2
    switch (valueIndex) {
      case 0:
        return user.code_contribution?.total || 0
      case 1:
        return user.overview?.active_days || 0
      case 2:
        return user.overview?.pull_requests || 0
      case 3:
        return getTopProjectsCount(user)
      case 4:
        return user.feature_project?.stargazerCount || 0
      default:
        return 0
    }
  }

  // 格式化数字显示
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`
    }
    return num.toString()
  }
</script>

<style scoped>
  .radar-chart {
    width: 100%;
    max-width: 900px;
    margin: 0 auto;
  }

  .radar-chart.large-size {
    width: 689px;
    height: 429px;
  }

  .radar-chart.small-size {
    width: 600px;
    height: 350px;
  }
  /* 默认模式下的 label 颜色 */
  .radar-label {
    fill: #2d2d2d; /* 设置雷达图 labels 颜色 */
  }

  /* 暗黑模式下的 label 颜色 */
  .dark .radar-label {
    fill: #eee; /* 暗黑模式下的颜色 */
  }
</style>

<style>
/* 浅色模式下的文字颜色 */
.radar-chart canvas {
  color: #000000 !important;
}

/* 深色模式下的文字颜色 */
:root.dark .radar-chart canvas {
  color: #FAF9F5 !important;
}
</style>
