<template>
  <svg :class="svgClass" v-bind="$attrs" aria-hidden="true">
    <use :xlink:href="iconName" :fill="color" />
  </svg>
</template>

<script setup lang="ts">
const props = defineProps({
  color: {
    type: String,
    default: '',
  },
  className: {
    type: String,
    default: '',
  },
  name: {
    type: String,
    required: true,
  },
});

const iconName = computed(() => {
  if (props.name) return `#icon-${props.name}`;
  return '';
});
const svgClass = computed(() => {
  if (props.className) return `svg-icon ${props.className}`;
  return 'svg-icon';
});
</script>

<style scoped lang="scss">
.svg-icon {
  width: 1em;
  height: 1em;
  min-width: 1em;
  min-height: 1em;
  position: relative;
  fill: currentColor;
  vertical-align: bottom;
}
</style>
