<template>
  <div class="stats-container">
    <div v-for="(item, index) in items" :key="index"
      class="stat-item-block"
      :class="{ 
        'not-last-in-row': (index + 1) % 3 !== 0 && index < items.length - 1,
        'not-last-overall': index < items.length - 1
      }">
      <span class="stat-value">{{ formatThousand(item.value) }}</span>
      <span class="stat-label">{{ item.label }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { formatThousand } from '~/utils'

defineProps<{
  items: {
    label: string;
    value: string | number;
  }[];
}>();
</script>

<style scoped>
.stats-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0;
  width: 100%;
}

.stat-item-block {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 60px;
  padding: 10px 20px;
  position: relative;
}

.stat-value {
  font-family: 'UDC 1.04', sans-serif;
  font-size: 24px;
  font-weight: 700;
  line-height: 1.2;
  color: #000;
  margin-bottom: 4px;
}

.dark .stat-value {
  color: #fff;
}

.stat-label {
  font-size: 12px;
  line-height: 1.2;
  color: #969696;
}

.dark .stat-label {
  color: #7A7A7A;
}

/* 只在每行内部的项目之间添加垂直分割线 */
.stat-item-block.not-last-in-row::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 30px;
  background-color: #E0E0E0;
}

.dark .stat-item-block.not-last-in-row::after {
  background-color: #404040;
}
</style>