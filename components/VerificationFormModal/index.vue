<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-black/10">
    <div class="bg-white rounded-3xl w-[640px] p-8 relative flex flex-col items-center shadow-xl max-h-[90vh] overflow-y-auto">
      <div class="absolute right-6 top-6 text-3xl text-[#BDBDBD] cursor-pointer" @click="$emit('close')">×</div>
      <!-- Stepper -->
      <div class="w-full flex items-center justify-center mb-8 stepper">
        <div class="flex items-center gap-2 cursor-pointer" @click="currentStep = 1">
          <div class="step-circle" :class="{active: currentStep === 1}">1</div>
          <div>
            <div class="step-label" :class="{active: currentStep === 1}">Step 1</div>
            <div class="step-title" :class="{active: currentStep === 1}">Basic Information</div>
          </div>
        </div>
        <div class="step-arrow"></div>
        <div class="flex items-center gap-2 cursor-pointer" @click="currentStep = 2">
          <div class="step-circle" :class="{active: currentStep === 2}">2</div>
          <div>
            <div class="step-label" :class="{active: currentStep === 2}">Step 2</div>
            <div class="step-title" :class="{active: currentStep === 2}">Education</div>
          </div>
        </div>
        <div class="step-arrow"></div>
        <div class="flex items-center gap-2 cursor-pointer" @click="currentStep = 3">
          <div class="step-circle" :class="{active: currentStep === 3}">3</div>
          <div>
            <div class="step-label" :class="{active: currentStep === 3}">Step 3</div>
            <div class="step-title" :class="{active: currentStep === 3}">Professional</div>
          </div>
        </div>
        <div class="step-arrow"></div>
        <div class="flex items-center gap-2 cursor-pointer" @click="currentStep = 4">
          <div class="step-circle" :class="{active: currentStep === 4}">4</div>
          <div>
            <div class="step-label" :class="{active: currentStep === 4}">Step 4</div>
            <div class="step-title" :class="{active: currentStep === 4}">Social Account</div>
          </div>
        </div>
      </div>
      <div v-if="currentStep === 1" class="w-full">
        <!-- Step 1: Basic Information (原有内容) -->
        <form class="w-full flex flex-col gap-7">
          <div>
            <label class="block text-base font-medium mb-2"><span class="text-[#FF5A1F]">*</span> Full Name</label>
            <input type="text" class="form-input" placeholder="Real name or frequently used professional name" v-model="full_name" />
          </div>
          <div class="flex gap-6 items-start">
            <div class="upload-box flex flex-col items-center justify-center cursor-pointer">
              <span class="text-3xl text-[#BDBDBD]">+</span>
              <span class="text-[#C47A5A] font-bold text-base">Upload</span>
              <input type="file" accept="image/*" class="absolute inset-0 opacity-0 cursor-pointer" @change="handlePhotoUpload" />
            </div>
            <div class="text-[#8D8D8D] text-sm leading-relaxed mt-1">
              Professional photo recommended<br />
              Square ratio: 1:1<br />
              Minimum size: 400 × 400 pixels<br />
              Maximum file size: 5MB<br />
              Supported formats: JPG, PNG, WEBP
            </div>
          </div>
          <div>
            <label class="block text-base font-medium mb-2"><span class="text-[#FF5A1F]">*</span> Current Role</label>
            <select class="form-input" v-model="currentRole">
              <option value="">Select Current Role</option>
              <option value="Current Student">Current Student</option>
              <option value="Researcher at an Institution">Researcher at an Institution</option>
              <option value="Engineer/Developer">Engineer/Developer</option>
              <option value="Freelancer/Independent Researcher">Freelancer/Independent Researcher</option>
              <option value="Other">Other (Please specify)</option>
            </select>
            <input v-if="currentRole === 'Other'" v-model="otherRole" type="text" class="form-input mt-2" placeholder="Please specify your role" />
          </div>
          <div>
            <label class="block text-base font-medium mb-2"><span class="text-[#FF5A1F]">*</span> Current/Recent Title</label>
            <input type="text" class="form-input" placeholder="HR Manager, CTO, Partner, etc." v-model="current_title" />
          </div>
          <div>
            <label class="block text-base font-medium mb-2"><span class="text-[#FF5A1F]">*</span> Research Area / Skills</label>
            <div class="flex flex-wrap gap-2 mb-2">
              <span v-for="(tag, idx) in research_area" :key="tag + idx" class="bg-[#F5E7E1] text-[#C47A5A] px-3 py-1 rounded-full text-sm flex items-center">
                {{ tag }}
                <span class="ml-2 cursor-pointer" @click="removeResearchArea(idx)">×</span>
              </span>
            </div>
            <input
              type="text"
              class="form-input"
              placeholder="Enter keywords, e.g. NLP, CV, LLM, MLOps, etc. (press Enter or comma to add)"
              v-model="research_area_input"
              @keydown.enter.prevent="addResearchArea"
              @keydown.",".prevent="addResearchArea"
            />
          </div>
          <div class="flex justify-end gap-4 mt-2">
            <button type="button" class="w-32 bg-black text-white rounded-full py-3 text-lg font-bold flex items-center justify-center" :disabled="loading" @click="handleBasicInfoNext('basic_info')">
              <span v-if="!loading">Next</span>
              <svg v-else class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path></svg>
            </button>
          </div>
        </form>
      </div>
      <div v-else-if="currentStep === 2" class="w-full">
        <!-- Step 2: Education (100%还原视觉稿) -->
        <form class="w-full flex flex-col gap-7">
          <div>
            <label class="block text-base font-medium mb-2"><span class="text-[#FF5A1F]">*</span> University Name</label>
            <input type="text" class="form-input" placeholder="Start typing to search University" v-model="university_name" />
          </div>
          <div>
            <label class="block text-base font-medium mb-2"><span class="text-[#FF5A1F]">*</span> Degree Level</label>
            <select class="form-input" v-model="degree_level">
              <option value="">Choose your highest degree</option>
              <option value="High School Diploma / Secondary School">High School Diploma / Secondary School</option>
              <option value="Associate Degree (e.g., AA, AS)">Associate Degree (e.g., AA, AS)</option>
              <option value="Bachelor's Degree (e.g., BA, BS)">Bachelor's Degree (e.g., BA, BS)</option>
              <option value="Master's Degree (e.g., MA, MS, MEng)">Master's Degree (e.g., MA, MS, MEng)</option>
              <option value="PhD / Doctoral Degree (e.g., PhD, ScD)">PhD / Doctoral Degree (e.g., PhD, ScD)</option>
              <option value="Professional Degree (e.g., JD, MD, MBA)">Professional Degree (e.g., JD, MD, MBA)</option>
              <option value="Postdoctoral">Postdoctoral</option>
              <option value="Currently Enrolled – Undergraduate">Currently Enrolled – Undergraduate</option>
              <option value="Currently Enrolled – Master's">Currently Enrolled – Master's</option>
              <option value="Currently Enrolled – PhD">Currently Enrolled – PhD</option>
              <option value="Other">Other (please specify)</option>
            </select>
            <input v-if="degree_level === 'Other'" v-model="degree_level" type="text" class="form-input mt-2" placeholder="Please specify your degree" />
          </div>
          <div>
            <label class="block text-base font-medium mb-2"><span class="text-[#FF5A1F]">*</span> Department / Major</label>
            <input type="text" class="form-input" placeholder="e.g., Computer Science, Electrical Engineering" v-model="department_major" />
          </div>
          <hr class="my-2 border-[#E5E5E5]" />
          <div>
            <label class="block text-base font-bold mb-1"><span class="text-[#FF5A1F]">*</span> University Verification</label>
            <div class="text-[#222] text-sm mb-2">Please select one of the following authentication methods to complete your profile.<br />You must provide either an email address or upload supporting documents.</div>
            <div class="mb-3 mt-2">
              <div class="font-medium text-base mb-1">1. Email Authentication</div>
              <div class="flex gap-2">
                <input type="email" class="form-input flex-1" placeholder="Enter Email address" v-model="recruiter_company_email" />
                <button type="button" class="border border-black rounded-lg px-6 py-2 font-bold text-base bg-white" @click="openEmailVerifyModal">Verify</button>
              </div>
            </div>
            <div class="text-[#8D8D8D] text-sm mb-2 mt-4">Please upload one of the following documents to complete your university verification:</div>
            <ul class="text-[#8D8D8D] text-sm mb-2 ml-4 list-disc">
              <li>Student ID Card</li>
              <li>University Transcript</li>
              <li>Enrollment Certificate</li>
              <li>Degree Certificate</li>
              <li>University Email Screenshot</li>
              <li>University Website Screenshot</li>
              <li>University Badge or Logo</li>
              <li>University Event Participation Certificate</li>
            </ul>
            <div class="flex gap-4 items-end mt-2">
              <div class="upload-box flex flex-col items-center justify-center cursor-pointer">
                <span class="text-3xl text-[#BDBDBD]">+</span>
                <span class="text-[#C47A5A] font-bold text-base">Upload</span>
                <input type="file" accept="image/*,.pdf" multiple class="absolute inset-0 opacity-0 cursor-pointer" />
              </div>
              <div class="text-[#8D8D8D] text-xs leading-relaxed mt-1">
                Format: JPG, PNG, Or PDF<br />
                Max size: 10MB<br />
                Multiple files allowed (up to 3)
              </div>
            </div>
          </div>
          <div class="flex justify-between gap-4 mt-2">
            <button type="button" class="w-32 border border-black text-black rounded-full py-3 text-lg font-bold bg-white">Previous</button>
            <button type="button" class="w-32 bg-black text-white rounded-full py-3 text-lg font-bold flex items-center justify-center" :disabled="loadingStep2" @click="handleEducationNext">
              <span v-if="!loadingStep2">Next</span>
              <svg v-else class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path></svg>
            </button>
          </div>
        </form>
      </div>
      <div v-else-if="currentStep === 3" class="w-full">
        <!-- Step 3: Professional (100%还原视觉稿) -->
        <form class="w-full flex flex-col gap-7">
          <div>
            <label class="block text-base font-medium mb-2"><span class="text-[#FF5A1F]">*</span> Job Title</label>
            <input type="text" class="form-input" placeholder="Enter your current/recent job title" v-model="job_title" />
          </div>
          <div>
            <label class="block text-base font-medium mb-2"><span class="text-[#FF5A1F]">*</span> Company / Org</label>
            <input type="text" class="form-input" placeholder="Company/ Org Name" v-model="company_org" />
          </div>
          <div>
            <label class="block text-base font-medium mb-2"><span class="text-[#FF5A1F]">*</span> Work/Research Summary</label>
            <textarea class="form-input" rows="3" placeholder="Short project or research intro" v-model="work_research_summary"></textarea>
          </div>
          <hr class="my-2 border-[#E5E5E5]" />
          <div>
            <label class="block text-base font-bold mb-1"><span class="text-[#FF5A1F]">*</span> Company / Org Verification</label>
            <div class="text-[#222] text-sm mb-2">Please select one of the following authentication methods to complete your profile.<br />You must provide either an email address or upload supporting documents.</div>
            <div class="mb-3 mt-2">
              <div class="font-medium text-base mb-1">1. Email Authentication</div>
              <div class="flex gap-2">
                <input type="email" class="form-input flex-1" placeholder="Enter Email address" v-model="recruiter_company_email" />
                <button type="button" class="border border-black rounded-lg px-6 py-2 font-bold text-base bg-white">Verify</button>
              </div>
            </div>
            <div class="text-[#8D8D8D] text-sm mb-2 mt-4">Upload business card, badge, hiring screenshot, business license, or website team page screenshot</div>
            <div class="flex gap-4 items-end mt-2">
              <div class="upload-box flex flex-col items-center justify-center cursor-pointer">
                <span class="text-3xl text-[#BDBDBD]">+</span>
                <span class="text-[#C47A5A] font-bold text-base">Upload</span>
                <input type="file" accept="image/*,.pdf" multiple class="absolute inset-0 opacity-0 cursor-pointer" />
              </div>
              <div class="text-[#8D8D8D] text-xs leading-relaxed mt-1">
                Format: JPG, PNG, Or PDF<br />
                Max size: 10MB<br />
                Multiple files allowed (up to 3)
              </div>
            </div>
          </div>
          <div class="flex justify-between gap-4 mt-2">
            <button type="button" class="w-32 border border-black text-black rounded-full py-3 text-lg font-bold bg-white">Previous</button>
            <button type="button" class="w-32 bg-black text-white rounded-full py-3 text-lg font-bold flex items-center justify-center" :disabled="loadingStep3" @click="handleProfessionalNext">
              <span v-if="!loadingStep3">Next</span>
              <svg v-else class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path></svg>
            </button>
          </div>
        </form>
      </div>
      <div v-else-if="currentStep === 4" class="w-full">
        <!-- Step 4: Social Account (100%还原视觉稿) -->
        <form class="w-full flex flex-col gap-8">
          <div>
            <label class="block text-base font-medium mb-2">GitHub</label>
            <input type="text" class="form-input" placeholder="" v-model="github_username" />
          </div>
          <div>
            <label class="block text-base font-medium mb-2">LinkedIn</label>
            <input type="text" class="form-input" placeholder="" v-model="linkedin_url" />
          </div>
          <div>
            <label class="block text-base font-medium mb-2">Google Scholar</label>
            <input type="text" class="form-input" placeholder="" v-model="google_scholar_url" />
          </div>
          <div>
            <label class="block text-base font-medium mb-2">Twitter (X)</label>
            <input type="text" class="form-input" placeholder="" v-model="twitter_username" />
          </div>
          <div class="flex justify-between gap-4 mt-6">
            <button type="button" class="w-48 border border-black text-black rounded-full py-3 text-lg font-bold bg-white">Previous</button>
            <button type="button" class="w-48 bg-black text-white rounded-full py-3 text-lg font-bold" @click="handleSocialAccountNext">Submit</button>
          </div>
        </form>
      </div>
    </div>
  </div>
  <!-- 邮箱验证码弹窗 -->
  <div v-if="showEmailVerifyModal" class="fixed inset-0 z-60 flex items-center justify-center bg-black/20">
    <div class="bg-white rounded-2xl w-[400px] p-8 relative flex flex-col items-center shadow-xl">
      <div class="absolute right-4 top-4 text-2xl text-[#BDBDBD] cursor-pointer" @click="closeEmailVerifyModal">×</div>
      <div class="text-xl font-bold text-black mb-4 w-full text-left">Verify Your Email Address</div>
      <div class="text-base text-black mb-1 w-full text-left">We've sent a verification email to your email.</div>
      <div class="text-base text-[#C47A5A] mb-2 w-full text-left">[{{ recruiter_company_email || '<EMAIL>' }}]</div>
      <div class="text-base text-black mb-4 w-full text-left">Please check your inbox and enter the code below.</div>
      <div class="flex w-full gap-2 mb-4">
        <input type="text" class="form-input flex-1" placeholder="Enter email verification code" v-model="emailVerifyCode" />
        <button type="button" class="border border-black rounded-lg px-4 py-2 font-bold text-base bg-white" @click="resendEmailCode">Resend Code</button>
      </div>
      <div class="flex w-full gap-2">
        <button type="button" class="w-1/2 border border-black text-black rounded-full py-3 text-lg font-bold bg-white" @click="closeEmailVerifyModal">Cancel</button>
        <button type="button" class="w-1/2 bg-black text-white rounded-full py-3 text-lg font-bold" @click="submitEmailVerifyCode">Verify</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { startCandidateVerification, uploadFile, updateVerificationStatus, verifyEmail, submitVerification, verifyEmailAuth } from '@/api/user'
const currentStep = ref(1)
const currentRole = ref('')
const otherRole = ref('')
const loading = ref(false)
const degreeLevel = ref('')
const otherDegree = ref('')
const loadingStep2 = ref(false)
const loadingStep3 = ref(false)
const { currentUser } = useFirebaseAuth()

const emit = defineEmits(['close'])


// 步骤映射
const stepMap = {
  basic_info: 1,
  education: 2,
  professional: 3,
  social_accounts: 4
}

function setStepByVerification(verification) {
  if (!verification || !verification.current_step) return
  const step = stepMap[verification.current_step]
  if (step) currentStep.value = step
}

// 照片上传
const photoUpload = ref(null)

const handlePhotoUpload = async (event) => {
  const file = event.target.files[0]
  photoUpload.value = file
  const formData = new FormData()
  formData.append('file', file)
  console.log('formData', formData, event.target, file)
  const res = await uploadFile(formData, {Userid: currentUser.value.uid})
  console.log('uploadFile res', res)
}

// 监听 props/外部传入的 verification 数据
const verificationData = ref(null)

// 假设父组件传入 verification 对象
watch(() => verificationData.value, (val) => {
  if (val && val.current_step) {
    setStepByVerification(val)
  }
})

// 如果页面初始化时有 verification 数据
onMounted(() => {
  if (verificationData.value && verificationData.value.current_step) {
    setStepByVerification(verificationData.value)
  }
})

// 基本信息
const basicInfo = ref({
  firstName: '',
  lastName: '',
  role: '',
  otherRole: ''
})

// 教育信息
const educationInfo = ref({
  degree: '',
  otherDegree: '',
  major: '',
  school: '',
  graduationYear: ''
})

// 专业信息
const professionalInfo = ref({
  company: '',
  title: '',
  startDate: '',
  endDate: '',
  description: ''
})

// 社交账号信息
const socialInfo = ref({
  github: '',
  linkedin: '',
  googleScholar: '',
  twitter: ''
})

// 步骤标题
const stepTitles = [
  'Basic Information',
  'Education Background',
  'Professional Experience', 
  'Social Account'
]

const full_name = ref('')
const current_title = ref('')
const research_area = ref([])
const research_area_input = ref('')
const university_name = ref('')
const degree_level = ref('')
const department_major = ref('')
const recruiter_company_email = ref('')
const company_verification_email = ref('')
const company_verification_file = ref([])
const job_title = ref('')
const company_org = ref('')
const work_research_summary = ref('')
const github_username = ref('')
const linkedin_url = ref('')
const google_scholar_url = ref('')
const twitter_username = ref('')
const showEmailVerifyModal = ref(false)
const emailVerifyCode = ref('')


function addResearchArea() {
  const value = research_area_input.value.trim()
  if (value && !research_area.value.includes(value)) {
    research_area.value.push(value)
  }
  research_area_input.value = ''
}
function removeResearchArea(idx) {
  research_area.value.splice(idx, 1)
}

async function verifyEmailFunc() {
  try {
    const res = await verifyEmail({ email: recruiter_company_email.value, email_type: 'edu_email' }, {Userid: currentUser.value.uid})
    console.log('verifyEmail res', res)
  } catch (error) {
    console.error('verifyEmail error', error)
  }
}

async function handleBasicInfoNext(step) {
  loading.value = true
  // 更新基本信息
  try {
    const data = {
      full_name: full_name.value,
      current_role: currentRole.value === 'Other' ? otherRole.value : currentRole.value,
      current_title: current_title.value,
      research_fields: research_area.value
    }
    const params = {
      data,
      step,
      advance_to_next: true
    }
    const res = await updateVerificationStatus(params, {Userid: currentUser.value.uid})
    console.log('updateVerificationStatus res', res)
    // verificationData.value = res.data.data.verification;
    // setStepByVerification(verificationData.value)
  } catch (error) {
    console.error('updateVerificationStatus error', error)
  }
  loading.value = false
  currentStep.value = 2
}

async function handleEducationNext() {
  loadingStep2.value = true
  // mock 异步请求 TODO: 待补充接口请求;
  // await new Promise(resolve => setTimeout(resolve, 1200))
  try {
    const data = {
      university_name: university_name.value,
      degree_level: degree_level.value,
      department_major: department_major.value,
      edu_email: recruiter_company_email.value,
    }
    const params = {
      data,
      step: 'education',
      advance_to_next: true
    }
    const res = await updateVerificationStatus(params, {Userid: currentUser.value.uid})
    console.log('handleEducationNext res', res)
  } catch (error) {
    console.error('handleEducationNext error', error)
  }
  loadingStep2.value = false
  currentStep.value = 3
}

async function handleProfessionalNext() {
  loadingStep3.value = true
  // mock 异步请求 TODO: 待补充接口请求;
  // await new Promise(resolve => setTimeout(resolve, 1200))
  try {
    const data = {
      company_org: company_org.value,
      job_title: job_title.value,
      work_research_summary: work_research_summary.value,
      edu_email: recruiter_company_email.value,
    }
    const params = {
      data,
      step: 'professional',
      advance_to_next: true
    } 
    const res = await updateVerificationStatus(params, {Userid: currentUser.value.uid})
    console.log('handleProfessionalNext res', res)
  } catch (error) {
    console.error('handleProfessionalNext error', error)
  }
  loadingStep3.value = false
  currentStep.value = 4
}   

async function handleSocialAccountNext() {
  loading.value = true
  try {
    const data = {
      github: github_username.value,  
      linkedin: linkedin_url.value,
      google_scholar: google_scholar_url.value,
      twitter: twitter_username.value
    }
    console.log('handleSocialAccountNext data', data)
    const res = await submitVerification(data, {Userid: currentUser.value.uid})
    if (res.data.success) {
      emit('close')
    }
    console.log('submitVerification res', res)
  } catch (error) { 
    console.error('submitVerification error', error)
  }
  loading.value = false
}

function openEmailVerifyModal() {
  verifyEmailFunc().then(() => {
    showEmailVerifyModal.value = true
  }).catch((err) => {
    // 可选：弹出错误提示
    console.error('Failed to send verification email', err)
  })
}
function closeEmailVerifyModal() {
  showEmailVerifyModal.value = false
  emailVerifyCode.value = ''
}
function resendEmailCode() {
  verifyEmailFunc()
}
async function submitEmailVerifyCode() {
  try {
    loading.value = true
    const data = {
      verification_code: emailVerifyCode.value,
      email_type: 'edu_email',
      email: recruiter_company_email.value
    }
    const res = await verifyEmailAuth(data, {Userid: currentUser.value.uid})
    console.log('verifyEmailAuth res', res)
    if (res.data.success) {
      closeEmailVerifyModal()
    }
  } catch (error) {
    console.error('verifyEmailAuth error', error)
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
    try {
        const uid = currentUser.value.uid
        const res = await startCandidateVerification({user_type:'job_seeker'},{Userid: uid})
        console.log('startCandidateVerification res',res)
        verificationData.value = res.data.data.verification;
        setStepByVerification(verificationData.value)
    } catch (error) {
        console.error('startCandidateVerification error', error)
    }
})
</script>

<style scoped>
.form-input {
  width: 100%;
  border: 1px solid #E5E5E5;
  border-radius: 0.75rem;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  background: #FAFAFA;
  color: #222;
  outline: none;
  transition: border 0.2s;
}
.form-input:focus {
  border-color: #C47A5A;
}
.upload-box {
  width: 120px;
  height: 120px;
  border: 2px dashed #E5E5E5;
  border-radius: 1rem;
  background: #FAFAFA;
  position: relative;
  justify-content: center;
  align-items: center;
  gap: 0.25rem;
  font-size: 1.1rem;
  transition: border 0.2s;
}
.upload-box:hover {
  border-color: #C47A5A;
}

.stepper {
  margin-bottom: 2rem;
  width: 100%;
  max-width: 580px;
  padding: 0 16px;
  box-sizing: border-box;
  overflow: hidden;
}

.step-circle {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #EDEDED;
  color: #BDBDBD;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.75rem;
  flex-shrink: 0;
}

.step-circle.active {
  background: #C47A5A;
  color: #fff;
}
.step-label {
  color: #BDBDBD;
  font-size: 0.7rem;
  font-weight: 500;
  line-height: 1;
}
.step-label.active {
  color: #C47A5A;
}
.step-title {
  color: #BDBDBD;
  font-size: 0.75rem;
  font-weight: 600;
  font-family: 'Montserrat', 'PingFang SC', Arial, sans-serif;
  line-height: 1.1;
  white-space: nowrap;
}
.step-title.active {
  color: #C47A5A;
}
.step-arrow {
  width: 30px;
  height: 1.5px;
  border-top: 2px dashed #E0E0E0;
  margin: 0 3px;
  position: relative;
  flex-shrink: 0;
}
.step-arrow::after {
  content: '';
  position: absolute;
  right: -6px;
  top: -5px;
  border-top: 2px solid #E0E0E0;
  border-right: 2px solid #E0E0E0;
  width: 6px;
  height: 6px;
  transform: rotate(45deg);
  background: #fff;
}

/* 自定义滚动条样式 - 去掉上下箭头 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(203, 124, 93, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(203, 124, 93, 0.5);
}

/* 去掉滚动条上下箭头 */
::-webkit-scrollbar-button {
  display: none;
}

/* Firefox 滚动条样式 */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(203, 124, 93, 0.3) transparent;
}
</style> 