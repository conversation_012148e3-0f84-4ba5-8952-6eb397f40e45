<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-black/10">
    <div class="bg-white rounded-3xl w-[540px] relative flex flex-col shadow-xl max-h-[90vh] overflow-hidden">
      <div class="absolute right-6 top-6 text-3xl text-[#BDBDBD] cursor-pointer z-10" @click="$emit('close')">×</div>
      <div class="p-8 pb-0 flex-shrink-0">
      <!-- Stepper -->
      <div class="w-full flex items-center justify-center mb-8 stepper">
        <div class="flex items-center gap-2 cursor-pointer" @click="currentStep = 1">
          <div class="step-circle" :class="{active: currentStep === 1}">1</div>
          <div>
            <div class="step-label" :class="{active: currentStep === 1}">Step 1</div>
            <div class="step-title" :class="{active: currentStep === 1}">Basic Information</div>
          </div>
        </div>
        <div class="step-arrow"></div>
        <div class="flex items-center gap-2 cursor-pointer" @click="currentStep = 2">
          <div class="step-circle" :class="{active: currentStep === 2}">2</div>
          <div>
            <div class="step-label" :class="{active: currentStep === 2}">Step 2</div>
            <div class="step-title" :class="{active: currentStep === 2}">Company / Org</div>
          </div>
        </div>
        <div class="step-arrow"></div>
        <div class="flex items-center gap-2 cursor-pointer" @click="currentStep = 3">
          <div class="step-circle" :class="{active: currentStep === 3}">3</div>
          <div>
            <div class="step-label" :class="{active: currentStep === 3}">Step 3</div>
            <div class="step-title" :class="{active: currentStep === 3}">Social Account</div>
          </div>
        </div>
      </div>
      </div>
      
      <!-- 可滚动内容区域 -->
      <div class="flex-1 overflow-y-auto verification-scroll px-8 pb-8">
      <!-- 原有表单内容 -->
      <form v-if="currentStep === 1" class="w-full flex flex-col gap-7">
        <!-- Step 1: Basic Information -->
        <div>
          <label class="block text-base font-medium mb-2"><span class="text-[#FF5A1F]">*</span> Full Name</label>
          <input type="text" class="form-input" placeholder="Real name or frequently used professional name" v-model="full_name" />
        </div>
        <div class="flex gap-6 items-start">
          <div class="upload-box flex flex-col items-center justify-center cursor-pointer relative overflow-hidden">
            <div v-if="avatar_url" class="w-full h-full relative">
              <img :src="avatar_url" class="w-full h-full object-cover rounded-2xl" alt="avatar" />
              <input type="file" accept="image/*" class="absolute inset-0 opacity-0 cursor-pointer" @change="handleAvatarChange" />
              <div v-if="avatarUploading" class="absolute inset-0 flex flex-col items-center justify-center bg-white/90 rounded-2xl z-10">
                <svg class="animate-spin mb-4" width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="32" cy="32" r="28" stroke="#F5E7E1" stroke-width="8" />
                  <path d="M60 32c0-15.464-12.536-28-28-28" stroke="#C47A5A" stroke-width="8" stroke-linecap="round" />
                </svg>
                <div class="text-xl font-bold text-black">Uploading Avatar</div>
              </div>
            </div>
            <div v-else class="w-full h-full flex flex-col items-center justify-center relative">
              <span class="text-3xl text-[#BDBDBD]">+</span>
              <span class="text-[#C47A5A] font-bold text-base">Upload</span>
              <input type="file" accept="image/*" class="absolute inset-0 opacity-0 cursor-pointer" @change="handleAvatarChange" />
              <div v-if="avatarUploading" class="absolute inset-0 flex flex-col items-center justify-center bg-white/90 rounded-2xl z-10">
                <svg class="animate-spin mb-4" width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="32" cy="32" r="28" stroke="#F5E7E1" stroke-width="8" />
                  <path d="M60 32c0-15.464-12.536-28-28-28" stroke="#C47A5A" stroke-width="8" stroke-linecap="round" />
                </svg>
                <div class="text-xl font-bold text-black">Uploading Avatar</div>
              </div>
            </div>
          </div>
          <div class="text-[#8D8D8D] text-sm leading-relaxed mt-1">
            Professional photo recommended<br />
            Square ratio: 1:1<br />
            Minimum size: 400 × 400 pixels<br />
            Maximum file size: 5MB<br />
            Supported formats: JPG, PNG, WEBP
          </div>
        </div>
        <div>
          <label class="block text-base font-medium mb-2"><span class="text-[#FF5A1F]">*</span> Current Role</label>
          <select class="form-input" v-model="current_role">
            <option value="">Select Current Role</option>
            <option value="Recruiter">Recruiter</option>
            <option value="HRBP">HRBP</option>
            <option value="Hiring Manager">Hiring Manager</option>
            <option value="Executive (CEO/CTO/COO)">Executive (CEO/CTO/COO)</option>
            <option value="Headhunter">Headhunter</option>
            <option value="RPO Recruiter">RPO Recruiter</option>
            <option value="Staffing Recruiter">Staffing Recruiter</option>
            <option value="Career Services">Career Services</option>
            <option value="Other">Others</option>
          </select>
          <input v-if="current_role === 'Other'" type="text" class="form-input mt-2" placeholder="Please specify your role" v-model="current_role" />
        </div>
        <div>
          <label class="block text-base font-medium mb-2"><span class="text-[#FF5A1F]">*</span> Current/Recent Title</label>
          <input type="text" class="form-input" placeholder="HR Manager, CTO, Partner, etc." v-model="current_title" />
        </div>
        <div class="flex justify-end gap-4 mt-2">
          <button
            type="button"
            class="w-32 bg-black text-white rounded-full py-3 text-lg font-bold flex items-center justify-center"
            @click="handleBasicInfoNext('basic_info')"
            :disabled="loading"
          >
            <svg v-if="loading" class="animate-spin mr-2" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="10" stroke="#fff" stroke-width="4" opacity="0.2" />
              <path d="M22 12a10 10 0 0 0-10-10" stroke="#fff" stroke-width="4" stroke-linecap="round" />
            </svg>
            <span>{{ loading ? 'Loading...' : 'Next' }}</span>
          </button>
        </div>
      </form>
      <form v-else-if="currentStep === 2" class="w-full flex flex-col gap-7">
        <!-- Step 2: Company / Org (100%还原视觉稿) -->
        <div>
          <label class="block text-base font-medium mb-2"><span class="text-[#FF5A1F]">*</span> Company / Org Name</label>
          <input type="text" class="form-input" placeholder="Official registered name" v-model="company_name" />
        </div>
        <div>
          <label class="block text-base font-medium mb-2"><span class="text-[#FF5A1F]">*</span> Industry</label>
          <select class="form-input" v-model="industry">
            <option value="">Select industry</option>
            <option value="Information Technology / Internet / Gaming">Information Technology / Internet / Gaming</option>
            <option value="Finance / Investment / Insurance">Finance / Investment / Insurance</option>
            <option value="Healthcare / Pharmaceuticals / Biotech">Healthcare / Pharmaceuticals / Biotech</option>
            <option value="Education / Training / Research">Education / Training / Research</option>
            <option value="Manufacturing / Industrial / Hardware">Manufacturing / Industrial / Hardware</option>
            <option value="Consumer Goods / FMCG / Luxury">Consumer Goods / FMCG / Luxury</option>
            <option value="Retail / E-commerce">Retail / E-commerce</option>
            <option value="Real Estate / Construction / Property">Real Estate / Construction / Property</option>
            <option value="Transportation / Logistics / Supply Chain">Transportation / Logistics / Supply Chain</option>
            <option value="Energy / Chemical / Environmental">Energy / Chemical / Environmental</option>
            <option value="Media / Advertising / Entertainment">Media / Advertising / Entertainment</option>
            <option value="Legal / Accounting / Consulting">Legal / Accounting / Consulting</option>
            <option value="HR / Headhunting / Outsourcing">HR / Headhunting / Outsourcing</option>
            <option value="Tourism / Hospitality / F&B">Tourism / Hospitality / F&B</option>
            <option value="Government / Nonprofit / Public Services">Government / Nonprofit / Public Services</option>
          </select>
        </div>
        <div>
          <label class="block text-base font-medium mb-2">Website (Optional)</label>
          <input type="text" class="form-input" placeholder="Company or Org Website URL" v-model="company_website" />
        </div>
        <div>
          <label class="block text-base font-medium mb-2">Introduction (Optional)</label>
          <input type="text" class="form-input" placeholder="Brief description of company and hiring needs" v-model="introduction" />
        </div>
        <hr class="my-2 border-[#E5E5E5]" />
        <div>
          <label class="block text-base font-bold mb-1"><span class="text-[#FF5A1F]">*</span> Company / Org Verification</label>
          <div class="text-[#222] text-sm mb-2">Please select one of the following authentication methods to complete your profile.<br />You must provide either an email address or upload supporting documents.</div>
          <div class="mb-3 mt-2">
            <div class="font-medium text-base mb-1">1、Email Authentication</div>
            <div class="flex gap-2">
              <input type="email" class="form-input flex-1" placeholder="Enter Email address" v-model="recruiter_company_email" />
              <button type="button" class="border border-black rounded-lg px-6 py-2 font-bold text-base bg-white" @click="openEmailVerifyModal">Verify</button>
            </div>
          </div>
          <div class="font-medium text-base mb-1 mt-4">2、Document Upload</div>
          <div class="text-[#8D8D8D] text-sm mb-2 mt-1">Upload business card, badge, hiring screenshot, business license, or website team page screenshot</div>
          <div class="flex gap-4 items-end mt-2">
            <div class="upload-box flex flex-col items-center justify-center cursor-pointer relative overflow-hidden">
              <span class="text-3xl text-[#BDBDBD]">+</span>
              <span class="text-[#C47A5A] font-bold text-base">Upload</span>
              <input type="file" accept="image/*,.pdf" multiple class="absolute inset-0 opacity-0 cursor-pointer" @change="handleCompanyFileChange" />
              <div v-if="companyFileUploading" class="absolute inset-0 flex flex-col items-center justify-center bg-white/90 rounded-2xl z-10">
                <svg class="animate-spin mb-4" width="48" height="48" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="32" cy="32" r="28" stroke="#F5E7E1" stroke-width="8" />
                  <path d="M60 32c0-15.464-12.536-28-28-28" stroke="#C47A5A" stroke-width="8" stroke-linecap="round" />
                </svg>
                <div class="text-base font-bold text-black">Uploading File...</div>
              </div>
            </div>
            <div class="flex flex-col gap-2 mt-2">
              <template v-for="(doc, idx) in company_documents" :key="doc.url">
                <div v-if="doc.type.startsWith('image/')" class="w-16 h-16 rounded-lg overflow-hidden border">
                  <img :src="doc.url" :alt="doc.name" class="object-cover w-full h-full" />
                </div>
                <div v-else class="flex items-center gap-2 text-xs text-[#C47A5A]">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4"/></svg>
                  <span>{{ doc.name }}</span>
                </div>
              </template>
            </div>
            <div class="text-[#8D8D8D] text-xs leading-relaxed mt-1">
              Format: JPG, PNG, Or PDF<br />
              Max size: 10MB<br />
              Multiple files allowed (up to 3)
            </div>
          </div>
        </div>
        <div class="flex justify-between gap-4 mt-2">
          <button type="button" class="w-32 border border-black text-black rounded-full py-3 text-lg font-bold bg-white" @click="currentStep = 1">Previous</button>
          <button type="button" class="w-32 bg-black text-white rounded-full py-3 text-lg font-bold flex items-center justify-center" @click="handleCompanyNext('company_org')" :disabled="loading">
            <svg v-if="loading" class="animate-spin mr-2" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="10" stroke="#fff" stroke-width="4" opacity="0.2" />
              <path d="M22 12a10 10 0 0 0-10-10" stroke="#fff" stroke-width="4" stroke-linecap="round" />
            </svg>
            <span>{{ loading ? 'Loading...' : 'Next' }}</span>
          </button>
        </div>
      </form>
      <form v-else-if="currentStep === 3" class="w-full flex flex-col gap-8">
        <!-- Step 3: Social Account (100%还原视觉稿) -->
        <div>
          <label class="block text-base font-medium mb-2">GitHub</label>
          <input type="text" class="form-input" placeholder="" v-model="github_username" />
        </div>
        <div>
          <label class="block text-base font-medium mb-2">LinkedIn</label>
          <input type="text" class="form-input" placeholder="" v-model="linkedin_url"/>
        </div>
        <div>
          <label class="block text-base font-medium mb-2">Google Scholar</label>
          <input type="text" class="form-input" placeholder="" v-model="google_scholar_url"/>
        </div>
        <div>
          <label class="block text-base font-medium mb-2">Twitter (X)</label>
          <input type="text" class="form-input" placeholder="" v-model="twitter_username"/>
        </div>
        <div class="flex justify-between gap-4 mt-6">
          <button type="button" class="w-48 border border-black text-black rounded-full py-3 text-lg font-bold bg-white" @click="currentStep = 2">Previous</button>
          <button type="submit" class="w-48 bg-black text-white rounded-full py-3 text-lg font-bold flex items-center justify-center" @click="handleSubmit" :disabled="loading">
            <svg v-if="loading" class="animate-spin mr-2" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="10" stroke="#fff" stroke-width="4" opacity="0.2" />
              <path d="M22 12a10 10 0 0 0-10-10" stroke="#fff" stroke-width="4" stroke-linecap="round" />
            </svg>
            <span>{{ loading ? 'Loading...' : 'Submit' }}</span>
          </button>
        </div>
      </form>
      </div>
      
      <!-- 邮箱验证码弹窗 -->
      <div v-if="showEmailVerifyModal" class="fixed inset-0 z-60 flex items-center justify-center bg-black/20">
        <div class="bg-white rounded-2xl w-[400px] p-8 relative flex flex-col items-center shadow-xl">
          <div class="absolute right-4 top-4 text-2xl text-[#BDBDBD] cursor-pointer" @click="closeEmailVerifyModal">×</div>
          <div class="text-xl font-bold text-black mb-4 w-full text-left">Verify Your Email Address</div>
          <div class="text-base text-black mb-1 w-full text-left">We've sent a verification email to your email.</div>
          <div class="text-base text-[#C47A5A] mb-2 w-full text-left">[{{ recruiter_company_email || '<EMAIL>' }}]</div>
          <div class="text-base text-black mb-4 w-full text-left">Please check your inbox and enter the code below.</div>
          <div class="flex w-full gap-2 mb-4">
            <input type="text" class="form-input flex-1" placeholder="Enter email verification code" v-model="emailVerifyCode" />
            <button type="button" class="border border-black rounded-lg px-4 py-2 font-bold text-base bg-white" @click="resendEmailCode">Resend Code</button>
          </div>
          <div class="flex w-full gap-2">
            <button type="button" class="w-1/2 border border-black text-black rounded-full py-3 text-lg font-bold bg-white" @click="closeEmailVerifyModal">Cancel</button>
            <button type="button" class="w-1/2 bg-black text-white rounded-full py-3 text-lg font-bold" @click="submitEmailVerifyCode">Verify</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { startCandidateVerification, updateVerificationStatus, uploadFile, verifyEmail, submitVerification, verifyEmailAuth } from '@/api/user'
const currentStep = ref(1)
const current_role = ref('')
const current_title = ref('')
const full_name = ref('')
const avatar_url = ref('')
const avatarUploading = ref(false)
const loading = ref(false)
const companyFileUploading = ref(false)
const verificationData = ref({})
const recruiter_company_email = ref('')
const company_documents = ref([])
const company_name = ref('')
const industry = ref('')
const company_website = ref('')
const introduction = ref('')
const github_username = ref('')
const linkedin_url = ref('')
const google_scholar_url = ref('')
const twitter_username = ref('')
const showEmailVerifyModal = ref(false)
const emailVerifyCode = ref('')

const { currentUser } = useFirebaseAuth()

const emit = defineEmits(['close'])

async function handleSubmit() {
    try {
        loading.value = true
        const data = {
        github: github_username.value,  
        linkedin: linkedin_url.value,
        google_scholar: google_scholar_url.value,
        twitter: twitter_username.value
        }
        console.log('handleSocialAccountNext data', data)
        const res = await submitVerification(data, {Userid: currentUser.value.uid})
        console.log('submitVerification res', res)
        if (res.data.success) {
            emit('close')
        }
        console.log('submitVerification res', res)
    } catch (error) { 
        console.error('submitVerification error', error)
    }
    loading.value = false
}

async function handleBasicInfoNext(step) {
  loading.value = true
  // 更新基本信息
  try {
    const data = {
      full_name: full_name.value,
      current_role: current_role.value === 'Other' ? otherRole.value : current_role.value,
      current_title: current_title.value,
    }
    const params = {
      data,
      step,
      advance_to_next: true
    }
    const res = await updateVerificationStatus(params, {Userid: currentUser.value.uid})
    console.log('updateVerificationStatus res', res)
  } catch (error) {
    console.error('updateVerificationStatus error', error)
  }
  loading.value = false
  currentStep.value = 2
}

async function handleCompanyNext(step) {
  loading.value = true
  // 更新公司信息
  try {
    const data = {
      company_name: company_name.value,
      industry: industry.value,
      company_website: company_website.value,
      introduction: introduction.value,
      recruiter_company_email: recruiter_company_email.value,  
      company_documents: company_documents.value
    }
    const params = {
      data,
      step,
      advance_to_next: true
    }
    const res = await updateVerificationStatus(params, {Userid: currentUser.value.uid})
    console.log('updateVerificationStatus res', res)
  } catch (error) {
    console.error('updateVerificationStatus error', error)
  }
  loading.value = false
  currentStep.value = 3
}

async function handleAvatarChange(e) {
  const file = e.target.files[0]
  if (!file) return
  avatarUploading.value = true
  const formData = new FormData()
  formData.append('file', file)
  const res = await uploadFile(formData, { Userid: currentUser.value.uid })
  console.log('uploadFile res', res)
  if (res.code === 200) {
    avatar_url.value = res.data.publicUrl || res.data.url
  }
  avatarUploading.value = false
}

async function verifyEmailFunc() {
  try {
    const res = await verifyEmail({ email: recruiter_company_email.value, email_type: 'recruiter_company_email' }, {Userid: currentUser.value.uid})
    console.log('verifyEmail res', res)
  } catch (error) {
    console.error('verifyEmail error', error)
  }
}

async function handleCompanyFileChange(e) {
  const files = e.target.files
  if (!files || !files.length) return
  companyFileUploading.value = true
  for (let i = 0; i < files.length; i++) {
    const file = files[i]
    const formData = new FormData()
    formData.append('file', file)
    const res = await uploadFile(formData, { Userid: currentUser.value.uid })
    if (res.code === 200) {
      company_documents.value.push({
        url: res.data.publicUrl || res.data.url,
        name: file.name,
        type: file.type
      })
    }
  }
  companyFileUploading.value = false
}

function setStepByVerification(verification) {
  if (!verification || !verification.current_step) return
  // 假设 current_step 可能为 'basic_info', 'company', 'social', 'completed' 等
  switch (verification.current_step) {
    case 'basic_info':
      currentStep.value = 1
      break
    case 'company_org':
    case 'education':
      currentStep.value = 2
      break
    case 'social_accounts':
      currentStep.value = 3
      break
    case 'social_accounts':
      currentStep.value = 3 // 或者根据业务需求定位到最后一步
      break
    default:
      currentStep.value = 1
  }
}

onMounted(async () => {
    try {
        const uid = currentUser.value.uid
        const res = await startCandidateVerification({user_type:'recruiter'},{Userid: uid})
        console.log('startCandidateVerification res',res)
        verificationData.value = res.data.data.verification;
        setStepByVerification(verificationData.value)
    } catch (error) {
        console.error('startCandidateVerification error', error)
    }
})

function openEmailVerifyModal() {
  verifyEmailFunc().then(() => {
    showEmailVerifyModal.value = true
  }).catch((err) => {
    // 可选：弹出错误提示
    console.error('Failed to send verification email', err)
  })
}
function closeEmailVerifyModal() {
  showEmailVerifyModal.value = false
  emailVerifyCode.value = ''
}
function resendEmailCode() {
  // TODO: 调用后端接口重新发送验证码
}
async function submitEmailVerifyCode() {
    try {
      loading.value = true
      const data = {
        verification_code: emailVerifyCode.value,
        email_type: 'recruiter_company_email',
        email: recruiter_company_email.value
      }
      const res = await verifyEmailAuth(data, {Userid: currentUser.value.uid})
      console.log('verifyEmailAuth res', res)
      if (res.data.success) {
        closeEmailVerifyModal()
      }
    } catch (error) {
      console.error('verifyEmailAuth error', error) 
    } finally {
      loading.value = false
    }
  // TODO: 校验验证码逻辑
//   closeEmailVerifyModal()
}
</script>

<style scoped>
.stepper {
  margin-bottom: 2rem;
  width: 100%;
  max-width: 480px;
  padding: 0 16px;
  box-sizing: border-box;
  overflow: hidden;
}
.step-circle {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #EDEDED;
  color: #BDBDBD;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.75rem;
  flex-shrink: 0;
}
.step-circle.active {
  background: #C47A5A;
  color: #fff;
}
.step-label {
  color: #BDBDBD;
  font-size: 0.7rem;
  font-weight: 500;
  line-height: 1;
}
.step-label.active {
  color: #C47A5A;
}
.step-title {
  color: #BDBDBD;
  font-size: 0.75rem;
  font-weight: 600;
  font-family: 'Montserrat', 'PingFang SC', Arial, sans-serif;
  line-height: 1.1;
  white-space: nowrap;
}
.step-title.active {
  color: #C47A5A;
  font-weight: bold;
}
.step-arrow {
  width: 30px;
  height: 1.5px;
  border-top: 2px dashed #E0E0E0;
  margin: 0 3px;
  position: relative;
  flex-shrink: 0;
}
.step-arrow::after {
  content: '';
  position: absolute;
  right: -6px;
  top: -5px;
  border-top: 2px solid #E0E0E0;
  border-right: 2px solid #E0E0E0;
  width: 6px;
  height: 6px;
  transform: rotate(45deg);
  background: #fff;
}

/* 自定义滚动条样式 - 去掉上下箭头 */
.verification-scroll::-webkit-scrollbar {
  width: 6px;
}

.verification-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.verification-scroll::-webkit-scrollbar-thumb {
  background: rgba(203, 124, 93, 0.3);
  border-radius: 3px;
}

.verification-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(203, 124, 93, 0.5);
}

/* 去掉滚动条上下箭头 */
.verification-scroll::-webkit-scrollbar-button {
  display: none;
}

/* Firefox 滚动条样式 */
.verification-scroll {
  scrollbar-width: thin;
  scrollbar-color: rgba(203, 124, 93, 0.3) transparent;
}
.form-input {
  width: 100%;
  border: 1px solid #E5E5E5;
  border-radius: 0.75rem;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  background: #FAFAFA;
  color: #222;
  outline: none;
  transition: border 0.2s;
}
.form-input:focus {
  border-color: #C47A5A;
}
.upload-box {
  width: 120px;
  height: 120px;
  border: 2px dashed #E5E5E5;
  border-radius: 1rem;
  background: #FAFAFA;
  position: relative;
  justify-content: center;
  align-items: center;
  gap: 0.25rem;
  font-size: 1.1rem;
  transition: border 0.2s;
  overflow: hidden;
}
.upload-box:hover {
  border-color: #C47A5A;
}
</style> 