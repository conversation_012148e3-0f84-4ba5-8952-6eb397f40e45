<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 px-4">
    <div
      class="bg-white dark:bg-[#1a1a1b] rounded-2xl shadow-xl p-8 md:p-8 p-6 w-full max-w-2xl relative text-left"
      v-if="!showSuccess"
    >
      <div class="flex justify-between items-center mb-2">
        <div class="font-bold text-black dark:text-white text-lg md:text-lg text-base" style="font-size: 18px">Join The Waiting List</div>
        <button
          class="bg-white dark:bg-[#1a1a1b] text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors text-2xl"
          @click="$emit('close')"
        >
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M15 5L5 15M5 5L15 15"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
            />
          </svg>
        </button>
      </div>
      <div class="text-base md:text-base text-sm text-gray-700 dark:text-gray-300 mb-5">
        Enter your email and we'll notify you when access is available.
      </div>
      <!-- <input
        v-model="name"
        type="text"
        placeholder="Enter your name"
        class="w-full border border-gray-200 rounded-lg px-4 py-3 text-base mb-4 focus:outline-none focus:border-black/80 placeholder-gray-400 bg-gray-50"
      /> -->
      <input
        v-model="email"
        type="email"
        placeholder="Enter your email address"
        class="w-full border border-gray-200 dark:border-gray-600 rounded-lg px-4 py-3 text-base md:text-base text-sm mb-6 focus:outline-none focus:border-black/80 dark:focus:border-white/80 placeholder-gray-400 dark:placeholder-gray-500 bg-gray-50 dark:bg-gray-800 text-black dark:text-white"
      />
      <div class="font-semibold mb-2 text-black dark:text-white text-base md:text-base text-sm">Who are you?</div>
      <div class="grid grid-cols-3 md:grid-cols-3 grid-cols-2 gap-x-4 gap-y-3 mb-4">
        <label class="flex items-center gap-2 text-base md:text-base text-sm font-normal text-black dark:text-white">
          <input type="radio" class="accent-black dark:accent-white" v-model="role" value="developer" /> Developer
        </label>
        <label class="flex items-center gap-2 text-base md:text-base text-sm font-normal text-black dark:text-white">
          <input type="radio" class="accent-black dark:accent-white" v-model="role" value="recruiter" /> Recruiter
        </label>
        <label class="flex items-center gap-2 text-base md:text-base text-sm font-normal text-black dark:text-white">
          <input type="radio" class="accent-black dark:accent-white" v-model="role" value="founder" /> Founder
        </label>
        <label class="flex items-center gap-2 text-base md:text-base text-sm font-normal text-black dark:text-white">
          <input type="radio" class="accent-black dark:accent-white" v-model="role" value="designer" /> Designer
        </label>
        <label class="flex items-center gap-2 text-base md:text-base text-sm font-normal text-black dark:text-white">
          <input type="radio" class="accent-black dark:accent-white" v-model="role" value="headhunter" /> Headhunter
        </label>
        <label class="flex items-center gap-2 text-base md:text-base text-sm font-normal text-black dark:text-white">
          <input type="radio" class="accent-black dark:accent-white" v-model="role" value="productMgr" /> Product Mgr
        </label>
        <label class="flex items-center gap-2 text-base md:text-base text-sm font-normal col-span-3 md:col-span-3 col-span-2 text-black dark:text-white">
          <input type="radio" class="accent-black dark:accent-white" v-model="role" value="other" /> Other
          <input
            v-if="role === 'other'"
            v-model="otherText"
            type="text"
            placeholder="Enter keywords and press Enter"
            class="flex-1 min-w-0 border border-gray-200 dark:border-gray-600 rounded-lg px-3 py-2 text-base md:text-base text-sm focus:outline-none focus:border-black/80 dark:focus:border-white/80 placeholder-gray-400 dark:placeholder-gray-500 bg-gray-50 dark:bg-gray-800 text-black dark:text-white ml-2"
          />
        </label>
      </div>
      <textarea
        v-model="reason"
        placeholder="Why do you want access? (e.g., 'I want to use DINQ to discover top AI developers.')"
        class="w-full border border-gray-200 dark:border-gray-600 rounded-lg px-4 py-3 text-base md:text-base text-sm mb-6 focus:outline-none focus:border-black/80 dark:focus:border-white/80 placeholder-gray-400 dark:placeholder-gray-500 bg-gray-50 dark:bg-gray-800 text-black dark:text-white resize-none"
        rows="2"
      ></textarea>
      <button
        class="w-full h-12 bg-black dark:bg-white text-white dark:text-black rounded-lg font-semibold text-base md:text-base text-sm transition hover:bg-gray-900 dark:hover:bg-gray-200 mb-4"
        @click="handleJoin"
      >
        Join Now
      </button>
      <div
        class="flex items-center justify-center gap-2 text-gray-500 dark:text-gray-400 text-base md:text-base text-sm cursor-pointer hover:underline"
        @click="$emit('back')"
      >
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M10 12L6 8L10 4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        Back to login
      </div>
    </div>
    <div
      class="bg-white dark:bg-[#1a1a1b] rounded-2xl shadow-xl p-8 md:p-8 p-6 w-full max-w-2xl relative text-center"
      v-else
    >
      <div class="text-2xl md:text-2xl text-xl font-bold text-black dark:text-white mb-4">
        Welcome to the waiting list!
      </div>
      <div class="text-base md:text-base text-sm text-gray-700 dark:text-gray-300 mb-8">
        Thank you for joining! We'll notify you as soon as access becomes available. Keep an eye on your inbox.
      </div>
      <button
        class="w-full h-12 bg-black dark:bg-white text-white dark:text-black rounded-lg font-semibold text-base md:text-base text-sm transition hover:bg-gray-900 dark:hover:bg-gray-200 mb-4"
        @click="goHome"
      >
        Got it
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { joinWaitingList } from '@/api/index'
  import { useRouter } from 'vue-router'
  const { currentUser } = useFirebaseAuth()

  const email = ref('')
  const name = ref('')
  const role = ref('')
  const otherText = ref('')
  const reason = ref('')
  const showSuccess = ref(false)
  const router = useRouter()

  defineEmits(['close', 'back'])

  async function handleJoin() {
    const data = {
      name: name.value,
      email: email.value,
      role: role.value,
      job_title: role.value === 'other' ? otherText.value : role.value,
      reason: reason.value,
    }
    const res = await joinWaitingList('/api/waiting-list/join', data, {
      headers: {
        userid: currentUser.value?.uid,
      },
    })
    console.log('join', res)
    if (res && res.data.success) {
      showSuccess.value = true
    } else {
      console.error('Failed to join waiting list:', res?.data?.message || 'Unknown error')
    }
  }

  function goHome() {
    router.replace('/analysis')
  }
</script>

<style scoped>
  /* input:checked {
    accent-color: #000;
  } */
  input[type='radio'] {
    width: 20px;
    height: 20px;
    -webkit-appearance: none;
    -moz-appearance: none;
    border: 1px solid #999;
    border-radius: 2px;
  }

  input[type='radio']:checked {
    background-image: url('~/assets/image/check.png');
    background-size: cover;
    @apply:bg-primary-100;
  }

  /* 移动端优化 */
  @media (max-width: 640px) {
    .rounded-2xl {
      border-radius: 1rem;
    }
    
    .grid-cols-3 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
    
    input[type='radio'] {
      width: 16px;
      height: 16px;
    }
  }
</style>
