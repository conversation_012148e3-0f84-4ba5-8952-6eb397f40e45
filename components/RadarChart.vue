<template>
  <div class="radar-chart" :class="{ 'large-size': props.size === 'large', 'small-size': props.size === 'small' }">
    <Radar 
      ref="chartInstance" 
      :data="chartData" 
      :options="chartOptions" 
              :class="props.size === 'large' ? 'w-[689px] h-[429px]' : props.size === 'small' ? 'w-[600px] h-[350px]' : 'w-full h-[400px]'" 
    />
  </div>
</template>

<script setup lang="ts">
  import {
    Chart as ChartJS,
    RadialLinearScale,
    PointElement,
    LineElement,
    Filler,
    Tooltip,
    Legend,
  } from 'chart.js'
  import { Radar } from 'vue-chartjs'
  import { computed, ref, onMounted, watch } from 'vue'

  ChartJS.register(RadialLinearScale, PointElement, LineElement, Filler, Tooltip, Legend)

  interface Researcher {
    name: string
    total_citations: number
    top_tier_papers: number
    first_author_papers: number
    first_author_citations: number
    most_cited_paper: {
      citations: number
    }
  }

  const props = defineProps<{
    researcher1: Researcher
    researcher2: Researcher
    size?: 'default' | 'large' | 'small'
    hideLegend?: boolean
    isShareCard?: boolean
  }>()
  // 获取主题颜色
  const isDark = ref(false)

  // 检测深色模式变化
  const updateDarkMode = () => {
    isDark.value = document.documentElement.classList.contains('dark')
  }

  const chartInstance = ref<any>(null)

  // 更新图表
  const updateChart = () => {
    if (chartInstance.value && chartInstance.value.chart) {
      // 通过vue-chartjs的chart属性访问底层Chart.js实例
      const chart = chartInstance.value.chart
      chart.options.scales.r.pointLabels.color = isDark.value ? '#C6C6C6' : '#000000'
      
      // 为分享卡片设置特定的网格线颜色和线宽
      if (props.isShareCard) {
        chart.options.scales.r.grid.color = 'rgba(147, 145, 144, 0.5)' // #939190 with 50% opacity
        chart.options.scales.r.grid.lineWidth = 1 // 1px线宽
      } else {
        chart.options.scales.r.grid.color = isDark.value ? '#616161' : '#E8DEDC'
        chart.options.scales.r.grid.lineWidth = 2 // 2px线宽
      }
      
      // 更新图例文字颜色
      if (chart.options.plugins && chart.options.plugins.legend && chart.options.plugins.legend.labels) {
        chart.options.plugins.legend.labels.color = isDark.value ? '#C6C6C6' : '#333333'
      }
      
      chart.update('none') // 使用'none'模式避免动画
    }
  }

  onMounted(() => {
    // 初始化深色模式检测
    updateDarkMode()
    
    // 监听深色模式变化
    const observer = new MutationObserver(() => {
      updateDarkMode()
      updateChart() // 深色模式变化时更新图表
    })
    observer.observe(document.documentElement, { 
      attributes: true, 
      attributeFilter: ['class'] 
    })
    
    // 延迟一点时间确保图表已经初始化完成
    setTimeout(() => {
      updateChart()
    }, 100)
  })

  // 监听深色模式变化
  watch(isDark, () => {
    updateChart()
  })

  // 计算雷达图数据
  const chartData = computed(() => ({
    labels: [
      'Top Tier Papers',
      'First Author Papers',
      'Representative Paper Citations',
      'Total Citations',
      'First Author Citations',
    ],
    datasets: [
      {
        label: props.researcher1.name,
        data: [
          normalizeValue(props.researcher1.top_tier_papers, dimensionMaxValues.value.topTier),
          normalizeValue(
            props.researcher1.first_author_papers,
            dimensionMaxValues.value.firstAuthor
          ),
          normalizeValue(
            props.researcher1.most_cited_paper.citations,
            dimensionMaxValues.value.mostCited
          ),
          normalizeValue(
            props.researcher1.total_citations,
            dimensionMaxValues.value.totalCitations
          ),
          normalizeValue(
            props.researcher1.first_author_citations,
            dimensionMaxValues.value.firstAuthorCitations
          ),
        ],
        fill: true,
        backgroundColor: 'rgba(127, 142, 183, 0.2)',
        borderColor: 'rgb(127, 142, 183)',
        pointBackgroundColor: 'rgb(127, 142, 183)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(127, 142, 183)',
      },
      {
        label: props.researcher2.name,
        data: [
          normalizeValue(props.researcher2.top_tier_papers, dimensionMaxValues.value.topTier),
          normalizeValue(
            props.researcher2.first_author_papers,
            dimensionMaxValues.value.firstAuthor
          ),
          normalizeValue(
            props.researcher2.most_cited_paper.citations,
            dimensionMaxValues.value.mostCited
          ),
          normalizeValue(
            props.researcher2.total_citations,
            dimensionMaxValues.value.totalCitations
          ),
          normalizeValue(
            props.researcher2.first_author_citations,
            dimensionMaxValues.value.firstAuthorCitations
          ),
        ],
        fill: true,
        backgroundColor: 'rgba(203, 124, 93, 0.2)',
        borderColor: 'rgb(203, 124, 93)',
        pointBackgroundColor: 'rgb(203, 124, 93)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(203, 124, 93)',
      },
    ],
  }))

  // 雷达图配置
  const chartOptions = computed(() => ({
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      r: {
        ticks: {
          display: false,
          stepSize: 25,
        },
        angleLines: {
          display: false,
        },
        grid: {
          color: props.isShareCard ? 'rgba(147, 145, 144, 0.5)' : '#E8DEDC', // 分享卡片使用固定颜色，其他由updateChart动态更新
          lineWidth: props.isShareCard ? 1 : 2, // 分享卡片使用1px，其他使用2px
        },
        pointLabels: {
          font: {
            size: 14,
            family: "'Poppins', sans-serif",
            weight: 'bold' as const,
          },
          color: '#000000', // 默认浅色，由updateChart动态更新
        },
        beginAtZero: true,
        max: 100,
      },
    },
    plugins: {
      legend: {
        display: !props.hideLegend,
        position: 'top' as const,
        labels: {
          font: {
            size: 14,
            family: "'Poppins', sans-serif",
            weight: 500,
          },
          padding: 35,
          usePointStyle: true,
          pointStyle: 'rect',
          boxWidth: 12,
          boxHeight: 12,
          borderRadius: 6,
          color: '#333333', // 默认颜色，由updateChart动态更新
          generateLabels: function(chart: any) {
            const original = ChartJS.defaults.plugins.legend.labels.generateLabels;
            const labels = original.call(this, chart);
            
            labels.forEach((label: any, index: number) => {
              if (index === 0) {
                // 第一个研究者 - 蓝色系 #7F8EB7
                label.fillStyle = 'rgba(127, 142, 183, 0.8)';
                label.strokeStyle = 'rgb(127, 142, 183)';
              } else {
                // 第二个研究者 - 橙色系 #CB7C5D
                label.fillStyle = 'rgba(203, 124, 93, 0.8)';
                label.strokeStyle = 'rgb(203, 124, 93)';
              }
              label.lineWidth = 2;
            });
            
            return labels;
          },
        },
      },
      tooltip: {
        enabled: true,
        mode: 'index' as const,
        bodyFont: {
          family: "'Poppins', sans-serif",
        },
        titleFont: {
          family: "'Poppins', sans-serif",
        },
        callbacks: {
          label: (context: any) => {
            const label = context.dataset.label || ''
            const datasetIndex = context.datasetIndex
            const valueIndex = context.dataIndex
            const originalValue = getOriginalValue(datasetIndex, valueIndex)
            const formattedValue = formatNumber(originalValue)
            return `${label}: ${formattedValue}`
          },
        },
      },
    },
  }))

  // 数据标准化处理函数
  const normalizeValue = (value: number, maxValue: number) => {
    if (!value) return 0
    if (maxValue === 0) return 0
    return Math.round((value / maxValue) * 100)
  }

  // 获取每个维度的最大值
  const dimensionMaxValues = computed(() => ({
    topTier: Math.max(props.researcher1.top_tier_papers, props.researcher2.top_tier_papers) * 1.1,
    firstAuthor:
      Math.max(props.researcher1.first_author_papers, props.researcher2.first_author_papers) * 1.1,
    mostCited:
      Math.max(
        props.researcher1.most_cited_paper.citations || 0,
        props.researcher2.most_cited_paper.citations || 0
      ) * 1.1,
    totalCitations:
      Math.max(props.researcher1.total_citations, props.researcher2.total_citations) * 1.1,
    firstAuthorCitations:
      Math.max(props.researcher1.first_author_citations, props.researcher2.first_author_citations) *
      1.1,
  }))

  // 获取原始值（用于tooltip显示）
  const getOriginalValue = (datasetIndex: number, valueIndex: number) => {
    const researcher = datasetIndex === 0 ? props.researcher1 : props.researcher2
    switch (valueIndex) {
      case 0:
        return researcher.top_tier_papers
      case 1:
        return researcher.first_author_papers
      case 2:
        return researcher.most_cited_paper.citations
      case 3:
        return researcher.total_citations
      case 4:
        return researcher.first_author_citations
      default:
        return 0
    }
  }

  // 格式化数字显示
  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`
    }
    return num.toString()
  }

  // const renderChart = () => {
  //   chartInstance.value = new Radar(
  //     document.getElementById('radar-chart'), // 你可能不需要手动获取元素 ID
  //     chartData,
  //     chartOptions
  //   )
  // }

  // chartOptions.scales.r.angleLines = { display: true, color: isDark ? 'rgba(0, 0, 0, 0.1)' : '#ccc' }
  // chartOptions.scales.r.grid = { color: isDark ? 'rgba(0, 0, 0, 0.1)' : '#000' }

  // onMounted(() => {

  //   isDark.value = document.documentElement.classList.contains('dark')
  //   const observer = new MutationObserver(() => {
  //     isDark.value = document.documentElement.classList.contains('dark')
  //   })
  //   observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] })
  // })
</script>

<style scoped>
  .radar-chart {
    width: 100%;
    max-width: 900px;
    margin: 0 auto;
  }
  
  .radar-chart.large-size {
    width: 689px;
    height: 429px;
  }
  
  .radar-chart.small-size {
    width: 600px;
    height: 350px;
  }
  /* 默认模式下的 label 颜色 */
  .radar-label {
    fill: #2d2d2d; /* 设置雷达图 labels 颜色 */
  }

  /* 暗黑模式下的 label 颜色 */
  .dark .radar-label {
    fill: #eee; /* 暗黑模式下的颜色 */
  }
</style>

<style>
/* 浅色模式下的文字颜色 */
.radar-chart canvas {
  color: #000000 !important;
}

/* 深色模式下的文字颜色 */
:root.dark .radar-chart canvas {
  color: #FAF9F5 !important;
}
</style>
