<template>
  <transition name="fade">
    <div
      v-if="visible"
      class="upgrade-modal"
      style="top: 68px;"
      @mouseenter="$emit('modal-enter')"
      @mouseleave="$emit('modal-leave')"
    >
      <!-- Header -->
      <div class="text-left">
        <h2 class="text-black dark:text-[#FAF9F5]" style="font-family: Poppins; font-weight: 600; font-size: 14px; line-height: 14px; letter-spacing: 0%; margin-bottom: 15px;">
          Upgrade subscription plan
        </h2>
        <p class="text-[#6A6B6E] dark:text-[#C6C6C6]" style="font-family: Poppins; font-weight: 400; font-size: 12px; line-height: 18px; letter-spacing: 0%; margin-bottom: 16px;">
          After the upgrade, you will enjoy<br>
          more features.
        </p>
      </div>

      <!-- Upgrade Button -->
      <div class="text-center" style="margin-bottom: 16px;">
        <button
          class="bg-black dark:bg-white text-white dark:text-black rounded-xl hover:opacity-90 transition-opacity"
          style="width: 208px; height: 32px;"
          @click="handleUpgrade"
        >
          Upgrade
        </button>
      </div>

      <!-- Divider -->
      <div class="upgrade-divider" style="margin-bottom: 16px;"></div>

      <!-- Credits Info -->
      <div class="text-left">
        <h3 class="text-black dark:text-[#FAF9F5]" style="font-family: Poppins; font-weight: 500; font-size: 14px; line-height: 14px; letter-spacing: 0%; margin-bottom: 8px;">
          {{ creditsDisplay }}
        </h3>
        <p class="text-[#6A6B6E] dark:text-[#7A7A7A]" style="font-family: Poppins; font-weight: 400; font-size: 12px; line-height: 12px; letter-spacing: 0%;">
          {{ creditsDescription }}
        </p>
      </div>


    </div>
  </transition>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { getCurrentUser } from '@/api/user'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'modal-enter'): void
  (e: 'modal-leave'): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const router = useRouter()
const { currentUser } = useFirebaseAuth()
const userProfile = ref<any>(null)

// 获取用户信息
const fetchUserProfile = async () => {
  if (!currentUser.value?.uid) return
  
  try {
    const userRes = await getCurrentUser({ Userid: currentUser.value.uid }) as any
    if (userRes.success) {
      userProfile.value = userRes.user
    }
  } catch (error) {
    console.error('Error fetching user profile:', error)
  }
}

// 监听用户状态变化
watch(() => currentUser.value?.uid, (newUid) => {
  if (newUid) {
    fetchUserProfile()
  } else {
    userProfile.value = null
  }
}, { immediate: true })

// 计算credits显示文本
const creditsDisplay = computed(() => {
  const remaining = userProfile.value?.remaining
  
  if (remaining === null) {
    return 'Credits : Unlimited'
  } else {
    return `Credits : ${remaining}/5`
  }
})

// 计算credits描述文本
const creditsDescription = computed(() => {
  const remaining = userProfile.value?.remaining
  
  if (remaining === null) {
    return 'Special invited user'
  } else {
    return 'Monthly Credits for free Users'
  }
})

const handleUpgrade = () => {
  // 关闭弹窗
  emit('update:visible', false)
  // 跳转到订阅页面
  router.push('/subscription')
}
</script>

<style scoped>
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.2s ease;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.upgrade-modal {
  position: absolute;
  right: 0;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: 1px solid #f3f4f6;
  z-index: 50;
  width: 240px;
  height: 214px;
  padding: 16px;
}

.dark .upgrade-modal {
  background: #141415;
  border: 1px solid #27282D;
}

.upgrade-divider {
  border-top: 1px solid #F0F0F0;
}

.dark .upgrade-divider {
  border-top: 1px solid #27282D;
}
</style> 