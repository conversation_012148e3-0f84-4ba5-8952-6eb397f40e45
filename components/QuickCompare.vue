<template>
  <div class="flex flex-col items-center justify-center mt-7.5 mt-10 mb-14">
    <motion.div
      :initial="{ opacity: 0, y: 20 }"
      :animate="{ opacity: 1, y: 0 }"
      :transition="{ duration: 0.5, ease: 'easeOut' }"
      class="text-center mb-7.5"
    >
      <div
        class="text-2xl md:text-4xl important:leading-[130%] font-semibold text-gray-900 clash-semibold tracking-wider"
      >
        Or compare the profiles of
      </div>
      <div
        class="text-2xl md:text-4xl important:leading-[130%] font-semibold text-[#CB7C5D] clash-semibold tracking-wider"
      >
        two AI researchers
      </div>
    </motion.div>
    <motion.div
      :initial="{ opacity: 0, y: 30 }"
      :animate="{ opacity: 1, y: 0 }"
      :transition="{ duration: 0.6, ease: 'easeOut', delay: 0.2 }"
      class="flex items-center gap-2 w-full max-w-4xl"
    >
      <input
        v-model="input1"
        type="text"
        class="custom-input flex-1 rounded-full border border-gray-900 px-8 py-4 outline-none focus:border-primary-100 transition-all bg-white placeholder-gray-400 placeholder:text-sm min-w-0 dark:text-black"
        placeholder="Name or Google Scholar link"
      />
      <img src="/image/vs.png" alt="vs" style="width: 40px; height: 22px" />
      <input
        v-model="input2"
        type="text"
        class="custom-input flex-1 rounded-full border border-gray-900 px-8 py-4 outline-none focus:border-primary-100 transition-all bg-white placeholder-gray-400 placeholder:text-sm min-w-0 dark:text-black"
        placeholder="Name or Google Scholar link"
      />
      <button
        class="fx-cer gap-2 py-4 px-7 font-bold rounded-full bg-black-100 text-white dark:hover:bg-black-100/90 transition-colors dark:border dark:border-4 dark:border-white"
        @click="handleCompare"
      >
        <img src="/image/stars.png" alt="compare" class="w-5 h-6" />
        Compare
      </button>
    </motion.div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { motion } from 'motion-v'
  const input1 = ref('')
  const input2 = ref('')
  const router = useRouter ? useRouter() : null
  function handleCompare() {
    if (!input1.value || !input2.value) return
    if (router) {
      router.push(
        `/compare?researcher1=${encodeURIComponent(input1.value)}&researcher2=${encodeURIComponent(input2.value)}`
      )
    }
  }
</script>
