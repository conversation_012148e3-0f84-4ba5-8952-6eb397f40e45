<template>
  <div class="search-input-wrapper">
    <div class="fx-cer flex-1 gap-4">
      <img src="~/assets/image/search.png" alt="" class="wh-6" />
      <div class="search-input-container">
        <input
          v-model="searchValue"
          class="flex-1 outline-none bg-transparent font-bold text-black search-input"
          @keyup.enter="handleEnterSearch"
          @focus="handleFocus"
          @blur="handleBlur"
          @input="handleInput"
          @compositionstart="handleCompositionStart"
          @compositionupdate="handleCompositionUpdate"
          @compositionend="handleCompositionEnd"
        />
        <!-- 动态placeholder显示 (仅在启用轮播时显示) -->
        <div v-if="enableCarousel && !searchValue.trim() && !isComposing" class="animated-placeholder">
          <Transition name="placeholder-fade" mode="out-in">
            <span :key="currentPlaceholderIndex" class="placeholder-text">{{ currentPlaceholder }}</span>
          </Transition>
        </div>
        <!-- 静态placeholder (当不启用轮播时) -->
        <div v-else-if="!enableCarousel && !searchValue.trim() && !isComposing" class="static-placeholder">
          <span class="placeholder-text">{{ staticPlaceholder }}</span>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  placeholder?: string
  type?: string
  enableCarousel?: boolean
}>()

const emit = defineEmits<{
  enterSearch: [query: string]
}>()

const searchValue = ref('')
const isUserFocused = ref(false)
const isComposing = ref(false) // 跟踪IME输入状态
const intervalId = ref<NodeJS.Timeout | null>(null)

// 轮播相关状态
const currentPlaceholderIndex = ref(0)

// Scholar模式的轮播文本（混合人名、URL示例和提示文本）
const scholarCarouselTexts = [
  'Enter Google Scholar URL or Just Name', // 提示文本 - 开始
  'https://scholar.google.com/citations?user=WLN3QrAAAAAJ',
  'Lukasz Kaiser',
  'Paste any Google Scholar link here', // 提示文本 - 中间
  'https://scholar.google.com/citations?user=dOad5HoAAAAJ',
  'Llion Jones'
]

// GitHub模式的轮播文本
const githubCarouselTexts = [
  'Enter Github Username or URL', // 提示文本 - 开始
  'mrkaye97',
  'sooperset',
  'Paste any GitHub profile link here', // 提示文本 - 中间
  'hiyouga',
  'lgrammel'
]

// 判断是否为提示文本
const isHelpText = (text: string) => {
  return text.includes('Enter ') || text.includes('Paste any ')
}

// 根据类型获取轮播文本
const carouselTexts = computed(() => {
  if (props.type === 'github') {
    return githubCarouselTexts
  }
  return scholarCarouselTexts
})

// 当前显示的placeholder
const currentPlaceholder = computed(() => {
  if (props.enableCarousel) {
    return carouselTexts.value[currentPlaceholderIndex.value]
  }
  return staticPlaceholder.value
})

// 静态placeholder
const staticPlaceholder = computed(() => {
  if (props.placeholder) {
    return props.placeholder
  }

  if (props.type === 'github') {
    return 'Name or Github link'
  }

  return 'Name or Google Scholar link'
})



// 开始轮播
const startCarousel = () => {
  if (!props.enableCarousel) return

  if (intervalId.value) {
    clearTimeout(intervalId.value)
  }

  const scheduleNext = () => {
    const currentText = carouselTexts.value[currentPlaceholderIndex.value]
    const delay = isHelpText(currentText) ? 5000 : 3000 // 提示文本5秒，其他3秒

    intervalId.value = setTimeout(() => {
      if (!isUserFocused.value && !searchValue.value.trim()) {
        currentPlaceholderIndex.value = (currentPlaceholderIndex.value + 1) % carouselTexts.value.length
        scheduleNext() // 递归调度下一个
      }
    }, delay)
  }

  scheduleNext()
}

// 停止轮播
const stopCarousel = () => {
  if (intervalId.value) {
    clearTimeout(intervalId.value)
    intervalId.value = null
  }
}

const handleEnterSearch = () => {
  if (searchValue.value.trim()) {
    emit('enterSearch', searchValue.value.trim())
  } else if (props.enableCarousel) {
    // 如果没有输入内容且启用了轮播，搜索当前显示的placeholder
    const placeholderText = currentPlaceholder.value

    // 如果是提示文本，不执行搜索
    if (isHelpText(placeholderText)) {
      return
    }

    // 直接使用完整内容（URL或人名）
    const searchTerm = placeholderText.trim()
    emit('enterSearch', searchTerm)
  }
}

const handleFocus = () => {
  isUserFocused.value = true
  // 焦点时停止轮播
  stopCarousel()
}

const handleBlur = () => {
  isUserFocused.value = false
  // 失去焦点时重新开始轮播
  if (props.enableCarousel) {
    startCarousel()
  }
}

const handleInput = () => {
  // 用户开始输入时的处理逻辑
}

// IME输入法相关事件处理
const handleCompositionStart = () => {
  isComposing.value = true
}

const handleCompositionUpdate = () => {
  isComposing.value = true
}

const handleCompositionEnd = () => {
  isComposing.value = false
}

onMounted(() => {
  if (props.enableCarousel) {
    startCarousel()
  }
})

onUnmounted(() => {
  stopCarousel()
})

defineExpose({
  searchValue,
  currentPlaceholder
})
</script>

<style scoped>
.search-input-wrapper {
  position: relative;
  width: 100%;
}

.search-input-container {
  position: relative;
  display: flex;
  flex: 1;
}

.search-input {
  position: relative;
  z-index: 2;
}

.animated-placeholder,
.static-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  pointer-events: none;
  z-index: 1;
}

.placeholder-text {
  font-weight: normal;
  color: #7C8493;
  font-size: inherit;
}



/* 轮播动画 */
.placeholder-fade-enter-active,
.placeholder-fade-leave-active {
  transition: opacity 0.4s ease;
}

.placeholder-fade-enter-from,
.placeholder-fade-leave-to {
  opacity: 0;
}

.placeholder-fade-enter-to,
.placeholder-fade-leave-from {
  opacity: 1;
}





/* 暗色模式支持 */
.dark .placeholder-text {
  color: #7C8493;
}
</style>
