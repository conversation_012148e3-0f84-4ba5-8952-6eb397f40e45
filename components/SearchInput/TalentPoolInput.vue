<template>
  <div class="fx-cer flex-1 gap-4">
    <img src="~/assets/image/search.png" alt="" class="wh-6" />
    <div class="search-input-container">
      <input
        v-model="searchValue"
        class="flex-1 outline-none bg-transparent font-bold text-black search-input"
        @keyup.enter="handleEnterSearch"
        @focus="handleFocus"
        @blur="handleBlur"
        @input="handleInput"
        @compositionstart="handleCompositionStart"
        @compositionupdate="handleCompositionUpdate"
        @compositionend="handleCompositionEnd"
      />
      <!-- 动态placeholder显示 -->
      <div v-if="!searchValue.trim() && !isComposing" class="animated-placeholder">
        <Transition name="placeholder-fade" mode="out-in">
          <span :key="currentTagIndex" class="placeholder-text">{{ currentPlaceholder }}</span>
        </Transition>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 定义props
const props = defineProps<{
  initialQuery?: string
}>()

// 定义emits
const emit = defineEmits<{
  enterSearch: [query: string]
}>()

const searchValue = ref(props.initialQuery || '')

// 监听 initialQuery 的变化，当父组件传入新的查询时更新搜索值
watch(() => props.initialQuery, (newQuery) => {
  if (newQuery !== undefined) {
    searchValue.value = newQuery
  }
})

// 循环播放的标签
const cyclingTags = [
  'AI Agent',
  'Diffusion Model',
  'LLM',
  'Video Generation',
  'CVPR2025',
  'Reinforcement Learning',
  'Image Generation',
  'AI Safety'
]

const currentTagIndex = ref(0)
const currentPlaceholder = ref(cyclingTags[0])
const isUserFocused = ref(false)
const intervalId = ref<NodeJS.Timeout | null>(null)
const isComposing = ref(false) // 跟踪IME输入状态

// 开始循环播放标签
const startTagCycling = () => {
  if (intervalId.value) {
    clearInterval(intervalId.value)
  }
  
  intervalId.value = setInterval(() => {
    if (!isUserFocused.value && !searchValue.value.trim()) {
      currentTagIndex.value = (currentTagIndex.value + 1) % cyclingTags.length
      currentPlaceholder.value = cyclingTags[currentTagIndex.value]
    }
  }, 3000)
}

// 停止循环播放
const stopTagCycling = () => {
  if (intervalId.value) {
    clearInterval(intervalId.value)
    intervalId.value = null
  }
}

const handleEnterSearch = () => {
  if (searchValue.value.trim()) {
    emit('enterSearch', searchValue.value.trim())
  } else {
    // 如果没有输入内容，搜索当前显示的标签
    emit('enterSearch', currentPlaceholder.value)
  }
}

const handleFocus = (event: Event) => {
  isUserFocused.value = true
  // 焦点时只停止轮播，placeholder保持显示
}

const handleBlur = () => {
  isUserFocused.value = false
  // 失去焦点时重新开始轮播
}

const handleInput = () => {
  // 用户开始输入时的处理逻辑
}

// IME输入法相关事件处理
const handleCompositionStart = () => {
  isComposing.value = true
}

const handleCompositionUpdate = () => {
  isComposing.value = true
}

const handleCompositionEnd = () => {
  isComposing.value = false
}

// 获取当前应该搜索的内容
const getCurrentSearchValue = () => {
  return searchValue.value.trim() || currentPlaceholder.value
}

onMounted(() => {
  startTagCycling()
})

onUnmounted(() => {
  stopTagCycling()
})

defineExpose({
  searchValue,
  getCurrentSearchValue
})
</script>

<style scoped>
.search-input-container {
  position: relative;
  display: flex;
  flex: 1;
}

.search-input {
  position: relative;
  z-index: 2;
}

.animated-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  pointer-events: none;
  z-index: 1;
}

.placeholder-text {
  font-weight: normal;
  color: #7C8493;
  font-size: inherit;
}

/* 简单的淡入淡出动画 */
.placeholder-fade-enter-active,
.placeholder-fade-leave-active {
  transition: opacity 0.4s ease;
}

.placeholder-fade-enter-from,
.placeholder-fade-leave-to {
  opacity: 0;
}

.placeholder-fade-enter-to,
.placeholder-fade-leave-from {
  opacity: 1;
}

/* 暗色模式支持 */
.dark .placeholder-text {
  color: #7C8493;
}
</style> 