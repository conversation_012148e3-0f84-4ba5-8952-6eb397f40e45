<template>
  <div class="min-h-6 rel rounded-full w-full bg-gradient-to-r from-[#F8E9C8] via-[#F8E9C8] to-[#FFE6AE] [background:linear-gradient(90deg,#F8E9C8_0%,#FFE6AE_100%)] dark:from-[#63605A] dark:to-[#423D30] dark:[background:linear-gradient(90deg,#63605A_0%,#423D30_100%)]">
    <motion.div
      :style="{ width: `${percentage}%` }"
      class="bg-gradient-to-r rel from-[#B1C1EC] via-[#B1C1EC] to-[#7F95CE] [background:linear-gradient(90deg,#B1C1EC_0%,#7F95CE_54.04%)] dark:from-[#647193] dark:to-[#3E4967] dark:[background:linear-gradient(90deg,#647193_0%,#3E4967_54.04%)] min-h-6 rounded-tl-full rounded-bl-full"
      :initial="{ width: 0 }"
      :animate="{ width: `${percentage}%` }"
    >
      <div class="abs top-1/2 right-0 h-7.5 rounded-full bg-[#E4E6FF] w-3px [box-shadow:-0.5px_0.5px_1px_0px_#5765F261] -translate-y-1/2"></div>
    </motion.div>
  </div>
</template>

<script setup lang="ts">
import { motion } from "motion-v"
const props = defineProps({
  percentage: {
    type: Number,
    default: 0
  }
})
</script>