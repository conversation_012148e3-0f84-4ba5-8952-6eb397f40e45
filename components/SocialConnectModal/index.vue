<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-black/10 px-4">
    <div class="bg-white dark:bg-black-70 rounded-3xl w-[400px] max-w-full p-6 md:p-6 p-4 relative">
      <div class="absolute right-4 top-4 text-2xl cursor-pointer" @click="$emit('close')">×</div>
      
      <div class="mb-6">
        <h2 class="text-xl md:text-xl text-lg font-semibold mb-2 text-black dark:text-white">
          {{ isEdit ? `Edit ${platformName}` : `Connect ${platformName}` }}
        </h2>
        <p class="text-gray-500 dark:text-gray-400 text-sm">
          {{ getDescription() }}
        </p>
      </div>

      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {{ platformType === 'twitter' ? 'X (Twitter) Username or URL' : 'GitHub Username or URL' }}
        </label>
        <input
          v-model="inputValue"
          type="text"
          :placeholder="getPlaceholder()"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white text-base md:text-base text-sm"
          @keyup.enter="handleSave"
        />
      </div>

      <div class="flex gap-3 flex-col md:flex-row">
        <button
          @click="$emit('close')"
          class="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-base md:text-base text-sm"
        >
          Cancel
        </button>
        <button
          v-if="isEdit"
          @click="handleUnbind"
          class="px-4 py-2 border border-red-300 rounded-lg text-red-600 hover:bg-red-50 transition-colors text-base md:text-base text-sm"
        >
          Unbind
        </button>
        <button
          @click="handleSave"
          class="flex-1 px-4 py-2 bg-black dark:bg-white text-white dark:text-black rounded-lg hover:opacity-90 transition-opacity text-base md:text-base text-sm"
        >
          {{ isEdit ? 'Update' : 'Connect' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

const props = defineProps({
  platformType: {
    type: String,
    required: true,
    validator: (value) => ['twitter', 'github'].includes(value)
  },
  currentValue: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['close', 'save', 'unbind'])

const inputValue = ref('')
const isEdit = computed(() => props.currentValue && props.currentValue !== '--')

const platformName = computed(() => {
  return props.platformType === 'twitter' ? 'X (Twitter)' : 'GitHub'
})

onMounted(() => {
  if (isEdit.value) {
    // 提取用户名作为默认值
    if (props.platformType === 'twitter') {
      inputValue.value = props.currentValue.replace('https://x.com/', '').replace('https://twitter.com/', '')
    } else {
      inputValue.value = props.currentValue.replace('https://github.com/', '')
    }
  }
})

function getDescription() {
  if (props.platformType === 'twitter') {
    return isEdit.value 
      ? 'Update your X (Twitter) profile link or leave empty to unbind.'
      : 'Connect your X (Twitter) profile to showcase your social presence.'
  } else {
    return isEdit.value
      ? 'Update your GitHub profile link or leave empty to unbind.'
      : 'Connect your GitHub profile to showcase your development work.'
  }
}

function getPlaceholder() {
  if (props.platformType === 'twitter') {
    return '@username or https://x.com/username'
  } else {
    return 'username or https://github.com/username'
  }
}

function handleSave() {
  const value = inputValue.value.trim()
  
  if (value === '') {
    if (isEdit.value) {
      handleUnbind()
    }
    return
  }

  let processedValue = value
  
  if (props.platformType === 'twitter') {
    // 处理 Twitter/X URL
    if (processedValue.includes('twitter.com/') || processedValue.includes('x.com/')) {
      const match = processedValue.match(/(?:twitter\.com\/|x\.com\/)([^\/\?]+)/)
      if (match) {
        processedValue = match[1]
      }
    }
    processedValue = processedValue.replace('@', '')
    processedValue = `https://x.com/${processedValue}`
  } else {
    // 处理 GitHub URL
    if (processedValue.includes('github.com/')) {
      const match = processedValue.match(/github\.com\/([^\/\?]+)/)
      if (match) {
        processedValue = match[1]
      }
    }
    processedValue = `https://github.com/${processedValue}`
  }

  emit('save', processedValue)
}

function handleUnbind() {
  emit('unbind')
}
</script>

<style scoped>
  /* 移动端优化 */
  @media (max-width: 640px) {
    .rounded-3xl {
      border-radius: 1.5rem;
    }
  }
</style> 