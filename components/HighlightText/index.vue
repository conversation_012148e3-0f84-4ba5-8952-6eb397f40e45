<template>
  <div class="highlight-text" v-html="highlightedText"></div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps({
  content: {
    type: String,
    required: true
  },
  keywords: {
    type: [String, Array],
    default: () => []
  },
  color: {
    type: String,
    default: '#000'
  },
  bold: {
    type: Boolean,
    default: true
  },
  caseSensitive: {
    type: Boolean,
    default: false
  }
})

const highlightedText = computed(() => {
  let text = props.content
  if (!text || !props.keywords) return text

  const keywords = Array.isArray(props.keywords)
    ? props.keywords
    : [props.keywords]

  const validKeywords = keywords.filter(k => k && k.trim())

  if (validKeywords.length === 0) return text

  validKeywords.forEach(keyword => {
    const flags = props.caseSensitive ? 'g' : 'gi'
    const regex = new RegExp(escapeRegExp(keyword), flags)

    text = text.replace(regex, match => {
      const style = `
        color: ${props.color};
        ${props.bold ? 'font-weight: bold;' : ''}
      `
      return `<span style="${style}">${match}</span>`
    })
  })

  return text
})

function escapeRegExp(string: string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}
</script>

<style scoped>
.highlight-text {
  line-height: 1.5;
}
</style>