<template>
  <transition name="fade">
    <div
      v-if="visible"
      class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
      @click.self
    >
      <transition name="scale">
        <div class="bg-white dark:bg-[#141415] rounded-5 shadow-xl p-5 w-136 rel" v-show="visible">
          <div class="mt-5 font-bold text-7 text-black dark:text-white">
            Hey, you've reached today's limit!
          </div>
          <div class="mt-4 mb-5 text-sm text-#696A70 dark:text-[#C6C6C6]">
            Try again tomorrow.
          </div>
          <button
            class="bg-black dark:bg-white rounded-2.5 uppercase transition-colors hover:bg-black/80 dark:hover:bg-gray-100 py-4 w-full text-white dark:text-black"
            @click="close"
          >
            OK
          </button>
          <div class="abs top-5 right-5">
            <div
              class="wh-6 text-#333 i-material-symbols:close cursor-pointer opacity-32 dark:text-white"
              @click="close"
            ></div>
          </div>
        </div>
      </transition>
    </div>
  </transition>
</template>

<script setup lang="ts">
  const props = defineProps<{
    visible: boolean
  }>()
  
  const emit = defineEmits<{
    (e: 'update:visible', val: boolean): void
  }>()
  
  const close = () => {
    emit('update:visible', false)
  }
</script>

<style scoped>
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease;
  }
  
  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
  
  .scale-enter-active,
  .scale-leave-active {
    transition: transform 0.3s ease, opacity 0.3s ease;
  }
  
  .scale-enter-from,
  .scale-leave-to {
    transform: scale(0.9);
    opacity: 0;
  }
</style> 