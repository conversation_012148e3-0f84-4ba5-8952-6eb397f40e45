<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-black/20 px-4">
    <div class="edit-name-modal bg-white dark:bg-[#1a1a1b] rounded-2xl p-8 md:p-8 p-6 w-[360px] max-w-full relative flex flex-col items-center">
      <div class="absolute right-4 top-4 text-2xl text-gray-400 dark:text-gray-500 cursor-pointer" @click="$emit('close')">×</div>
      <div class="text-xl md:text-xl text-lg mb-6 text-black dark:text-white">Change Username</div>
      <input v-model="editName" class="edit-name-input w-full border border-gray-200 dark:border-gray-600 rounded-lg px-4 py-2 text-lg md:text-lg text-base mb-2 focus:outline-none focus:border-primary-500 bg-white dark:bg-gray-800 text-black dark:text-white" />
      <div v-if="errorMsg" class="w-full text-left text-red-500 text-sm mb-4">{{ errorMsg }}</div>
      <div class="flex w-full justify-between mt-2 gap-4 flex-col md:flex-row">
        <button class="border border-gray-300 dark:border-gray-600 text-[#3B4659] dark:text-gray-300 bg-white dark:bg-gray-800 px-8 py-2 text-lg md:text-lg text-base transition hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg md:rounded-none" @click="$emit('close')">Cancel</button>
        <button class="rounded-full bg-[#000000] dark:bg-white text-white dark:text-black px-8 py-2 text-lg md:text-lg text-base transition hover:bg-[#000001] dark:hover:bg-gray-200" @click="onSave">Confirm</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits } from 'vue'
import { updateUserInfo } from '@/api/user'
const props = defineProps({
  currentName: { type: String, default: '' }
})

const emit = defineEmits(['close', 'save'])
const editName = ref(props.currentName)
const { currentUser } = useFirebaseAuth()
const errorMsg = ref('')

watch(() => props.currentName, (val) => { editName.value = val })

function onSave() {
  errorMsg.value = ''
  if (!editName.value.trim()) {
    errorMsg.value = 'Username cannot be empty.'
    return
  }
  updateUserInfo({
    display_name: editName.value,
    // email: '<EMAIL>',
    // profile_picture: 'https://example.com/new-profile-picture.jpg',
    // preferences: {
    //   theme: 'dark',
    //   language: 'zh-CN'
    // }
  }, {
      Userid: currentUser.value.uid
  }).then((res) => {
    if (res.success) {
      console.log('更新用户信息成功', res);
      emit('save', editName.value)
    } else {
      errorMsg.value = res.message || 'Failed to update username.'
    }
  }).catch((err) => {
    errorMsg.value = err?.message || 'Failed to update username.'
  })
}
</script>

<style scoped>
.edit-name-modal {
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.1);
}
.edit-name-input {
  font-size: 1.1rem;
}
.edit-name-modal button {
  min-width: 110px;
  border-radius: 12px;
}

/* 移动端优化 */
@media (max-width: 640px) {
  .edit-name-modal {
    margin: 1rem;
  }
  
  .rounded-2xl {
    border-radius: 1rem;
  }
}
</style> 