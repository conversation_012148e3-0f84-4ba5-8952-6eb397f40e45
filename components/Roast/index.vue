<template>
  <motion.div class="rel px-30" :initial="{ opacity: 0 }" :in-view="{ opacity: 1 }" :transition="{ duration: 1, delay: 0.5 }"
    :in-view-options="{ once: true }">
    <div class="w-full bg-white/65 rounded-2xl py-8 rel z-1">
      <div class="flex items-center justify-center flex-col">
        <div class="font-italic text-14 leading-67px text-primary-100 mb-4 font-black">Roast</div>
        <div class="max-w-156.5 text-center text-lg leading-8 text-#555658"><PERSON><PERSON><PERSON>'s face-swapping framework has more
          citations than <PERSON><PERSON>'s entire career, making <PERSON> the face of Al research.</div>
      </div>
    </div>
    <div class="ellipse"></div>
  </motion.div>
</template>

<script setup lang="ts">
import { motion } from 'motion-v'

</script>

<style scoped>
.ellipse {
  @apply abs top-0 right-0 left-0 bottom-0;
  background-image: url('~/assets/image/ellipse.png');
  background-size: 50% 100%;
  background-repeat: no-repeat;
  background-position: center;
  z-index: 0;
}
</style>