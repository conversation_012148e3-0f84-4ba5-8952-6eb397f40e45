<template>
  <div class="mt-7.5 footer-container">
    <div class="footer-main-content">
      <div class="footer-top">
        <div class="footer-logo">
          <nuxt-link to="/">
            <img
              :src="isDark ? '/image/darklogo2.png' : '/image/newlogo1.png'"
              width="95"
              height="42"
              :alt="isDark ? 'DINQ dark logo' : 'DINQ logo'"
            />
          </nuxt-link>
        </div>
        
        <div class="footer-right">
          <div class="footer-links">
            <a href="/terms" class="footer-link">
              <span>Terms & Conditions</span>
            </a>
            <a href="/privacy" class="footer-link">
              <span>Privacy Policy</span>
            </a>
          </div>
          <div class="footer-social">
            <a href="https://x.com/dinq_io" target="_blank">
              <button class="media-btn border-none !bg-twitter">
                <div class="i-proicons:x-twitter wh-6"></div>
              </button>
            </a>
            <a href="https://discord.gg/JyQwmYUTM6" target="_blank">
              <button class="discord-btn">
                <div class="text-base"></div>
              </button>
            </a>
          </div>
        </div>
      </div>
      
      <div class="footer-divider"></div>
      
      <div class="footer-copyright">
        Copyright© 2025 DINQ Inc. All Rights Reserved
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  const isDark = ref(false)
  const currentTheme = ref('light')
  onMounted(() => {
    isDark.value = document.documentElement.classList.contains('dark')
    const observer = new MutationObserver(() => {
      isDark.value = document.documentElement.classList.contains('dark')
    })
    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] })
  })

  // Apply theme to document
  const applyTheme = (theme: string) => {
    const html = document.documentElement
    if (theme === 'dark') {
      html.classList.add('dark')
      html.setAttribute('data-theme', 'dark')
    } else {
      html.classList.remove('dark')
      html.setAttribute('data-theme', 'light')
    }
  }

  // Toggle theme
  const toggleTheme = () => {
    console.log('toggleTheme clicked')
    const newTheme = currentTheme.value === 'light' ? 'dark' : 'light'
    currentTheme.value = newTheme
    localStorage.setItem('theme', newTheme)
    applyTheme(newTheme)
  }
</script>

<style scoped>
  .footer-container {
    position: relative;
    z-index: 25; /* 确保footer在talent-pool页面的白色背景覆盖层之上 */
    padding: 30px 0; /* 与首页保持一致 */
  }

  .footer-main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px; /* 与首页保持一致的左右边距 */
  }

  .footer-divider {
    width: 100%;
    border-width: 1px;
    border: 1px solid #E0E6EE;
    margin-top: 30px;
  }

  .dark .footer-divider {
    border: 1px solid #404040;
  }

  .footer-copyright {
    text-align: center;
    font-family: Poppins;
    font-weight: 400;
    font-size: 14px;
    line-height: 150%;
    letter-spacing: 0%;
    text-transform: capitalize;
    color: #283646;
    padding: 20px 0;
  }

  .dark .footer-copyright {
    color: #A8A8A8;
  }

  .footer-link {
    font-family: Poppins;
    font-weight: 400;
    font-size: 16px;
    line-height: 150%;
    letter-spacing: 0%;
    text-transform: capitalize;
    color: #283646;
    transition: colors;
  }

  .dark .footer-link {
    color: #A8A8A8;
  }

  .footer-link:hover {
    color: #2563eb;
  }

  .media-btn {
    @apply rounded-full wh-42px f-cer transition-all text-white;
  }

  .discord-btn {
    @apply rounded-full wh-42px transition-colors bg-white text-gray-500 hover:bg-gray-100 hover:text-primary-100 dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-gray-400;
    display: flex;
    align-items: center;
  }

  .text-base {
    background-image: url('~/assets/image/footthemedark.png');
    background-size: 100%;
    width: 42px;
    height: 42px;
    display: inline-block; /* 确保 div 能显示 */
  }

  /* 桌面端布局 */
  .footer-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
  }

  .footer-right {
    display: flex;
    align-items: center;
    gap: 80px; /* gap-20 = 5rem = 80px */
  }

  .footer-links {
    display: flex;
    align-items: center;
    gap: 46px; /* gap-[2.88rem] = 46px */
  }

  .footer-social {
    display: flex;
    align-items: center;
    gap: 22px; /* gap-5.5 = 1.375rem = 22px */
  }

  /* 移动端页脚重新布局 */
  @media (max-width: 768px) {
    .footer-main-content {
      position: relative;
    }
    
    /* 顶部：logo左，社交媒体右 */
    .footer-top {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 60px; /* 增加间距为链接区域留出空间 */
    }
    
    .footer-logo {
      flex-shrink: 0;
    }
    
    .footer-right {
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      gap: 16px;
    }
    
    /* 将links从footer-right中提取出来，绝对定位到中间 */
    .footer-right .footer-links {
      position: absolute;
      top: 72px; /* footer-top的高度 + margin */
      left: 0;
      right: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      margin-bottom: 0;
      gap: 0;
      padding: 0 25px; /* 添加左右边距，与页面边距对齐 */
      box-sizing: border-box;
    }
    
    /* 调整分割线样式 */
    .footer-divider {
      margin-top: 80px; /* 增加上方间距为链接区域留出空间 */
      border-color: rgba(0, 0, 0, 0.1);
    }
    
    .dark .footer-divider {
      border-color: rgba(255, 255, 255, 0.2);
    }
    
    /* 版权信息样式调整 */
    .footer-copyright {
      opacity: 0.7;
      color: #666;
      margin-top: 0; /* 移除原有margin，由分割线控制间距 */
    }
    
    .dark .footer-copyright {
      color: #999;
      opacity: 0.8;
    }
  }
  
  /* 更小屏幕的进一步优化 */
  @media (max-width: 480px) {
    .footer-container {
      padding: 0 16px;
    }
    
    .footer-right .footer-links {
      padding: 0 20px; /* 减少左右边距 */
    }
    
    .footer-social {
      gap: 16px; /* 减少社交媒体按钮间距 */
    }
    
    .media-btn,
    .discord-btn {
      width: 36px;
      height: 36px;
    }
    
    .text-base {
      width: 36px;
      height: 36px;
    }
  }
</style>
