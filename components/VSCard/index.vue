<template>
  <div v-if="show" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
    <div class="relative">
      <div
        class="rounded-2xl shadow-xl p-6 w-[985px] h-[700px] bg-gradient-to-b from-[#FFFFFF] to-[#F4F2F1] dark:from-[#42516a] dark:to-[#000]"
      >
        <div>
          <div class="relative rounded-4">
            <div
              class="absolute top-0 left-0 w-full h-full z-0 bg-[url(@/assets/image/compare1.png)] bg-no-repeat bg-cover bg-left rounded-4"
              style="clip-path: polygon(0 0, 55% 0, 45% 100%, 0% 100%)"
            ></div>
            <!-- 左图遮罩：从左到右白 → 透明 -->
            <div
              class="absolute top-0 left-0 w-full h-full pointer-events-none z-10 rounded-4"
              style="
                clip-path: polygon(0 0, 55% 0, 45% 100%, 0% 100%);
                background: linear-gradient(
                  to right,
                  rgba(255, 255, 255, 0.8),
                  rgba(255, 255, 255, 0)
                );
              "
            ></div>
            <div
              class="absolute top-0 right-0 w-full z-0 h-full bg-[url(@/assets/image/compare2.png)] bg-no-repeat bg-cover bg-right rounded-4"
              style="clip-path: polygon(55% 0, 100% 0, 100% 100%, 45% 100%)"
            ></div>
            <!-- 右图遮罩：从左到右透明 → 白 -->
            <div
              class="absolute top-0 right-0 w-full h-full pointer-events-none z-10 rounded-4"
              style="
                clip-path: polygon(55% 0, 100% 0, 100% 100%, 45% 100%);
                background: linear-gradient(
                  to right,
                  rgba(255, 255, 255, 0),
                  rgba(255, 255, 255, 0.8)
                );
              "
            ></div>
            <!-- 顶部用户信息 -->
            <div class="fx-cer justify-right gap-24 z-10">
              <div class="w-[42%] z-10">
                <div class="flex items-center justify-end h-[80px]">
                  <div class="text-right">
                    <h2 class="text-xl font-bold leading-8">{{ pkData.researcher1.name }}</h2>
                    <p class="fx-cer gap-1 text-sm text-gray-500 leading-8">
                      <SvgIcon name="verified" />{{ pkData.researcher1.affiliation }}
                    </p>
                  </div>
                  <img :src="pkData.researcher1.avatar" class="w-17.5 h-17.5 rounded-full mr-4" />
                </div>
                <div class="flex flex-wrap gap-2 mt-4 justify-center h-[64px]">
                  <div
                    v-for="(field, index) in pkData.researcher1.research_fields"
                    :key="index"
                    class="px-3 py-1 bg-black/10 rounded-md text-white text-xs leading-[28px] h-[32px]"
                  >
                    {{ field }}
                  </div>
                </div>
              </div>

              <img
                src="@/assets/svg/VS.svg"
                class="absolute top-10 w-30 h-20 left-109.8 mr-4 z-10"
              />

              <div class="w-[42%] z-10">
                <div class="flex items-center justify-start h-[80px]">
                  <img :src="pkData.researcher2.avatar" class="w-17.5 h-17.5 rounded-full mr-4" />
                  <div class="flex flex-col justify-around">
                    <h2 class="text-xl font-bold leading-8">{{ pkData.researcher2.name }}</h2>
                    <p class="fx-cer gap-1 text-sm text-gray-500 leading-8">
                      <SvgIcon name="verified" />{{ pkData.researcher2.affiliation }}
                    </p>
                  </div>
                </div>
                <div class="flex flex-wrap gap-2 mt-4 justify-center h-[64px]">
                  <div
                    v-for="(field, index) in pkData.researcher2.research_fields"
                    :key="index"
                    class="px-3 py-1 bg-black/10 rounded-md text-white text-xs leading-[28px] h-[32px]"
                  >
                    {{ field }}
                  </div>
                </div>
              </div>
            </div>
            <!-- 雷达图部分 -->
            <div class="sticky py-2 z-10">
              <RadarChart
                :researcher1="pkData.researcher1"
                :researcher2="pkData.researcher2"
                size="small"
                class="z-10"
              />
            </div>
          </div>
        </div>
        <div class="fx-cer justify-between border-t h-[70px] mt-7.5">
          <img
            :src="isDark ? '/image/darklogo2.png' : '/image/newlogo1.png'"
            width="95"
            height="42"
            :alt="isDark ? 'DINQ dark logo' : 'DINQ logo'"
          />
          <div class="text-3.5 font-400">Copyright @ 2025 DINQ Inc. All rights reserved</div>
        </div>
      </div>

      <button
        class="absolute -bottom-12 left-1/2 -translate-x-1/2 p-1 hover:bg-white/10 rounded-full transition-colors bg-neutral-800"
        @click="$emit('close')"
      >
        <div class="i-carbon:close text-2xl text-white"></div>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { defineProps } from 'vue'
  import SvgIcon from '../SvgIcon'

  const props = defineProps({
    show: Boolean,
    user: Object,
    isDark: Boolean,
    pkData: Object,
  })
</script>
