# LogoMarquee 组件

这是一个基于 Vue3 和 vue3-marquee 的横向跑马灯组件，用于展示合作伙伴 logo。

## 功能特性

- ✅ 支持无限循环滚动 (clone=true)
- ✅ 支持 hover 暂停
- ✅ 响应式设计 (桌面端、平板、移动端)
- ✅ 9个 PMD logo 图片轮播
- ✅ 图片 hover 效果 (去灰度 + 缩放)
- ✅ 自定义滚动速度 (25秒完成一轮)

## 使用方式

```vue
<template>
  <LogoMarquee />
</template>

<script setup>
import LogoMarquee from '~/components/LogoMarquee/index.vue'
</script>
```

## 配置说明

- **duration**: 25秒 - 滚动一轮的时间
- **clone**: true - 启用无缝滚动
- **pauseOnHover**: true - 鼠标悬停时暂停

## 图片资源

组件使用以下9个图片文件：
- `/image/pmd/pmd1.png`
- `/image/pmd/pmd2.png`
- `/image/pmd/pmd3.png`
- `/image/pmd/pmd4.png`
- `/image/pmd/pmd5.png`
- `/image/pmd/pmd6.png`
- `/image/pmd/pmd7.png`
- `/image/pmd/pmd8.png`
- `/image/pmd/pmd9.png`

## 响应式断点

- **桌面端**: 图片高度 60px，间距 30px
- **平板端** (768px以下): 图片高度 45px，间距 20px
- **移动端** (480px以下): 图片高度 35px，间距 15px

## 依赖

- Vue 3
- vue3-marquee (v4.2.2) 