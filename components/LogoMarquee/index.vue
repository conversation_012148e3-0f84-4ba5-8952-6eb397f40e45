<template>
  <div class="logo-marquee-container">
    <h3 class="marquee-title">Built for Industry Leaders</h3>
    <div class="marquee-container">
      <Vue3Marquee
        :clone="true"
        :duration="40"
        :pauseOnHover="true"
        class="marquee-wrapper"
      >
        <div 
          v-for="(logoSrc, index) in logoImages" 
          :key="index"
          class="logo-item"
        >
          <img 
            :src="logoSrc" 
            :alt="`Partner logo ${index + 1}`"
            class="logo-image"
          />
        </div>
      </Vue3Marquee>
      <!-- 左右渐变遮罩 -->
      <div class="fade-left"></div>
      <div class="fade-right"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Vue3Marquee } from 'vue3-marquee'

// 9个PMD图片的路径数组
const logoImages = [
  '/image/pmd/pmd1.png',
  '/image/pmd/pmd2.png',
  '/image/pmd/pmd3.png',
  '/image/pmd/pmd4.png',
  '/image/pmd/pmd5.png',
  '/image/pmd/pmd6.png',
  '/image/pmd/pmd7.png',
  '/image/pmd/pmd8.png',
  '/image/pmd/pmd9.png'
]
</script>

<style scoped>
.logo-marquee-container {
  text-align: center;
  width: 100%;
}

.marquee-title {
  font-family: 'Alexandria', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-weight: 500;
  font-size: 24px;
  line-height: 100%;
  color: #000000;
  margin: 0 0 32px 0;
}

.marquee-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.marquee-wrapper {
  width: 100%;
  overflow: hidden;
}

/* 左右渐变遮罩 */
.fade-left,
.fade-right {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 120px; /* 渐变区域宽度 */
  z-index: 2;
  pointer-events: none; /* 不阻挡鼠标事件 */
}

.fade-left {
  left: 0;
  background: linear-gradient(to right, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
}

.fade-right {
  right: 0;
  background: linear-gradient(to left, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
}

/* 移动端跑马灯无视页边距，占满整个屏幕宽度 */
@media (max-width: 1023px) {
  .logo-marquee-container {
    width: 100vw;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    margin-left: 0;
    margin-right: 0;
  }
  
  .marquee-container {
    width: 100vw;
  }
  
  .marquee-wrapper {
    width: 100vw;
  }
}

.logo-item {
  flex-shrink: 0;
  margin: 0 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-image {
  height: 60px;
  width: auto;
  max-width: 120px;
  object-fit: contain;
  filter: grayscale(50%) opacity(0.8);
  transition: all 0.3s ease;
}

.logo-image:hover {
  filter: grayscale(0%) opacity(1);
  transform: scale(1.05);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .marquee-title {
    font-size: 1rem; /* 移动端字体更小 */
    margin: -1rem 0 24px 0; /* 向上移动，类似request按钮的对齐方式 */
  }
  
  .logo-item {
    margin: 0 20px;
  }
  
  .logo-image {
    height: 80px; /* 移动端logo更大 */
    max-width: 160px; /* 移动端logo更大 */
  }
  
  /* 移动端渐变区域稍微宽一些 */
  .fade-left,
  .fade-right {
    width: 100px;
  }
}

@media (max-width: 480px) {
  .marquee-title {
    font-size: 0.9rem; /* 小屏幕字体再小一些 */
    margin: -1rem 0 20px 0; /* 保持向上移动 */
  }
  
  .logo-item {
    margin: 0 15px;
  }
  
  .logo-image {
    height: 70px; /* 小屏幕logo也调大 */
    max-width: 140px; /* 小屏幕logo也调大 */
  }
  
  /* 小屏幕渐变区域调整 */
  .fade-left,
  .fade-right {
    width: 80px;
  }
}
</style> 