<template>
  <button class="talent-pool-button" @click="handleClick" type="button">
    <img src="~/assets/image/fdj1.png" alt="Search" class="w-6 h-6" />
    <span class="text-base font-bold">DINQ</span>
  </button>
</template>

<script setup lang="ts">
// 定义emits
const emit = defineEmits<{
  click: []
}>()

const handleClick = (event: Event) => {
  event.preventDefault()
  event.stopPropagation()
  console.log('TalentPoolButton clicked')
  emit('click')
}
</script>

<style scoped>
.talent-pool-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  width: 156px;
  height: 56px;
  background-color: #1C1C21;
  color: white;
  border-radius: 9999px;
  transition: all 0.2s;
  border: none;
  cursor: pointer;
}

.talent-pool-button:hover {
  background-color: #C87C5D;
}
</style> 