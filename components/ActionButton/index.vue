<template>
  <!-- <button class="fx-cer gap-2 py-4 px-7 rounded-full bg-black-100 text-white hover:bg-black-100/90 transition-colors" @click="$emit('click')"> -->
  <button class="fx-cer justify-center gap-2 py-4 px-7 rounded-full bg-black-100 text-white hover:bg-black-100/90 transition-colors action-button" @click="$emit('click')">
    <img :src="imgSrc" alt="" class="button-icon" />
    <span class="text-base font-bold button-text">{{ displayText }}</span>
  </button>
</template>

<script setup lang="ts">
const props = defineProps({
  imgSrc: {
    type: String,
    required: true
  },
  buttonText: {
    type: String,
    required: true
  }
})

// 按钮文字保持不变，始终显示原始文字
const displayText = computed(() => {
  return props.buttonText
})
</script>

<style scoped>
/* 500px以下屏幕隐藏图标 */
@media (max-width: 500px) {
  .button-icon {
    display: none;
  }
  
  .action-button {
    gap: 0; /* 移除图标后取消间距 */
  }
}
</style>
