<template>
  <div ref="chartRef" class="chart-container"></div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref } from 'vue'
import * as echarts from 'echarts'
import type { ECharts, EChartsOption } from 'echarts'
import Subtract from "~/assets/image/Subtract.png"
import type { YearlyCitations, YearlyPapers } from '~/api/types';

const props = defineProps<{
  yearlyPapers: YearlyPapers,
  yearlyCitations: YearlyCitations
}>()

const chartRef = ref<HTMLDivElement | null>(null)
let chartInstance: ECharts | null = null

// 检测深色模式
const isDark = ref(false)

const updateDarkMode = () => {
  if (process.client) {
    isDark.value = document.documentElement.classList.contains('dark')
  }
}

const updateChart = () => {
  if (chartInstance) {
    chartInstance.setOption(getChartOption(), true)
  }
}

const getAllYears = () => {
  const yearsSet = new Set<string>([
    ...Object.keys(props.yearlyPapers),
    ...Object.keys(props.yearlyCitations)
  ]);
  
  // 获取当前年份
  const currentYear = new Date().getFullYear();
  // 计算10年前的年份
  const tenYearsAgo = currentYear - 10;
  
  // 过滤年份，只保留最近10年的数据
  return Array.from(yearsSet)
    .map(Number)
    .filter(year => year >= tenYearsAgo && year <= currentYear)
    .sort((a, b) => a - b)
    .map(String);
};
const getSeriesData = (data: Record<string, number>, years: string[]) => {
  return years.map(year => data[year] || 0);
};

const getMaxValue = (data: Record<string, number>) => {
  const maxValue = Math.max(...Object.values(data));
  if (maxValue === 0) return 10;
  
  // 根据数值大小动态确定取整基数
  const magnitude = Math.pow(10, Math.floor(Math.log10(maxValue)));
  
  // 对于较大的数值，使用更大的取整基数
  let roundBase;
  if (maxValue >= 10000) {
    roundBase = magnitude; // 例如：60617 -> 基数10000，取整到70000
  } else if (maxValue >= 1000) {
    roundBase = magnitude / 2; // 例如：2500 -> 基数500，取整到3000
  } else if (maxValue >= 100) {
    roundBase = magnitude / 5; // 例如：267 -> 基数20，取整到280
  } else {
    roundBase = 10; // 小数值仍使用10
  }
  
  return Math.ceil(maxValue / roundBase) * roundBase;
};

const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)
  chartInstance.setOption(getChartOption())

  if (process.client) {
    window.addEventListener('resize', handleResize)
  }
}

const handleResize = () => {
  chartInstance?.resize()
}

const getChartOption = (): EChartsOption => {
  const allYears = getAllYears();
  const papersMaxValue = getMaxValue(props.yearlyPapers);
  const citationsMaxValue = getMaxValue(props.yearlyCitations);
  
  // 检测深色模式
  const isDark = process.client ? document.documentElement.classList.contains('dark') : false;
  
  return {
    legend: {
      show: true,
      bottom: 0,
      textStyle: {
        color: isDark ? '#A0A0A0' : '#666',
        fontSize: 12,
        fontFamily: 'Poppins'
      }
    },
    tooltip: {
      trigger: 'axis',
      position: 'top',
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      borderColor: 'transparent',
      textStyle: {
        color: '#FFF',
        fontSize: 12,
        fontFamily: 'Poppins'
      },
      formatter: (params: any) => {
        return params.map((item: any) => `${item.seriesName}: ${item.value}`).join('<br/>')
      }
    },
    grid: {
      left: 10,
      right: 10,
      bottom: 42 ,
      top: 20,
      containLabel: true
    },
    graphic: {
      type: 'image',
      right: 0,
      left: 58,
      top: 10,
      bottom: 30,
      style: {
        image: Subtract,
        width: 476,
        height: 320
      },
      zlevel: -1
    },
    xAxis: {
      type: 'category',
      data: allYears,
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: {
        color: isDark ? '#A0A0A0' : '#666',
        fontSize: 12,
        fontFamily: 'Poppins',
        margin: 15
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '',
        nameTextStyle: {
          color: isDark ? '#A0A0A0' : '#666',
          fontSize: 12,
          fontFamily: 'Poppins'
        },
        splitLine: { show: false },
        axisLine: { show: false },
        axisTick: { show: false },
        max: papersMaxValue,
        axisLabel: {
          show: false
        }
      },
      {
        type: 'value',
        name: '',
        position: 'left',
        nameTextStyle: {
          color: isDark ? '#A0A0A0' : '#666',
          fontSize: 12,
          fontFamily: 'Poppins'
        },
        splitLine: { 
          show: true,
          lineStyle: {
            color: isDark ? '#404040' : '#E5E5E5',
            width: 1,
            type: 'solid'
          }
        },
        axisLine: { show: false },
        axisTick: { show: false },
        max: citationsMaxValue,
        axisLabel: {
          color: isDark ? '#A0A0A0' : '#666',
          fontSize: 12,
          fontFamily: 'Poppins'
        }
      }
    ],
    series: [
      {
        name: 'Papers',
        data: getSeriesData(props.yearlyPapers, allYears),
        type: 'bar',
        yAxisIndex: 0,
        itemStyle: {
          color: isDark ? '#596C86' : '#C3DCFF',
          borderRadius: [8, 8, 0, 0]
        },
        label: {
          show: true,
          position: 'insideTop',
          offset: [0, -20],
          color: isDark ? '#A0A0A0' : '#666',
          fontSize: 12,
          formatter: '{c}',
          fontFamily: 'Poppins'
        }
      },
      {
        name: 'Citations',
        data: getSeriesData(props.yearlyCitations, allYears),
        type: 'line',
        yAxisIndex: 1,
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          width: 3,
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [{
              offset: 0,
              color: '#5BC4FF'
            }, {
              offset: 1,
              color: '#FF5BEF'
            }]
          }
        },
        itemStyle: {
          color: '#FFF',
          borderColor: '#AE8FF7',
          borderWidth: 3
        }
      }
    ]
  }
}

onMounted(() => {
  // 初始化深色模式检测
  updateDarkMode()
  
  // 监听深色模式变化
  const observer = new MutationObserver(() => {
    updateDarkMode()
    updateChart() // 深色模式变化时更新图表
  })
  observer.observe(document.documentElement, { 
    attributes: true, 
    attributeFilter: ['class'] 
  })
  
  initChart()
})

onBeforeUnmount(() => {
  if (chartInstance) {
    if (process.client) {
      window.removeEventListener('resize', handleResize)
    }
    chartInstance.dispose()
  }
})
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 400px;
  /* background-image: url('~/assets/image/Subtract.png');
  background-size: 100% 200px;
  background-position: left 75%;
  background-repeat: no-repeat; */
}
</style>