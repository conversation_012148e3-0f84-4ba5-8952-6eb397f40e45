<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-black/10">
    <div class="bg-white rounded-3xl w-[540px] relative flex flex-col shadow-xl max-h-[90vh] overflow-hidden">
      <div class="absolute right-6 top-6 text-3xl text-[#BDBDBD] cursor-pointer z-10" @click="$emit('close')">×</div>
      <div class="p-8 pb-0 flex-shrink-0">
      <div class="text-2xl font-bold text-black mb-8 w-full text-left">Verification details</div>
      </div>
      
      <!-- 可滚动内容区域 -->
      <div class="flex-1 overflow-y-auto verification-scroll px-8 pb-8">
      <div class="w-full rounded-2xl border border-[#E5E5E5] mb-8 p-6 flex flex-col gap-6">
        <div class="flex items-center gap-6 mb-4">
          <div class="flex flex-col items-center w-32">
            <div class="text-base text-[#8D8D8D] mb-2">Avatar</div>
            <img :src="verification.avatar_url || '/image/default-avatar.png'" class="w-20 h-20 rounded-full object-cover" />
          </div>
          <div class="flex-1 grid grid-cols-2 gap-x-8 gap-y-2">
            <div>
              <div class="text-xs text-[#8D8D8D] mb-1">Full Name</div>
              <div class="text-base font-semibold text-black">{{ verification.full_name }}</div>
            </div>
            <div>
              <div class="text-xs text-[#8D8D8D] mb-1">Current Role</div>
              <div class="text-base font-semibold text-black">{{ verification.current_role }}</div>
            </div>
            <div class="col-span-2">
              <div class="text-xs text-[#8D8D8D] mb-1">Certification Name</div>
              <div class="text-base font-semibold text-black">{{ verification.certification_name }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="w-full flex flex-col gap-4 mb-6">
        <div>
          <div class="text-xs text-[#8D8D8D] mb-1">Organization / Company</div>
          <div class="text-base font-semibold text-black">{{ verification.company_name }}</div>
        </div>
        <div>
          <div class="text-xs text-[#8D8D8D] mb-1">Work / Research Summary</div>
          <div class="text-base text-black">{{ verification.work_research_summary || '--' }}</div>
        </div>
        <div>
          <div class="text-xs text-[#8D8D8D] mb-1">Organization/Company Email</div>
          <div class="text-base text-black">
            <a :href="'mailto:' + verification.recruiter_company_email" class="text-[#3B82F6] underline">{{ verification.recruiter_company_email }}</a>
            <span v-if="verification.email_verified" class="ml-2 text-xs text-green-600 font-semibold">(Verified)</span>
          </div>
        </div>
        <div>
          <div class="text-xs text-[#8D8D8D] mb-1">Employment Verification Upload</div>
          <div class="flex flex-col gap-2 mt-2">
            <template v-for="(doc, idx) in verification.company_documents" :key="doc.url">
              <a v-if="doc.url" :href="doc.url" target="_blank" class="flex items-center gap-2 px-4 py-2 rounded-lg bg-[#F7F7F7] border border-[#E5E5E5] text-[#C47A5A] font-semibold">
                <img src="/image/pdf-icon.png" class="w-5 h-5" />
                {{ doc.name }}
              </a>
            </template>
          </div>
        </div>
      </div>
      <div class="w-full flex flex-col gap-4 mb-6">
        <div class="flex items-center gap-3">
          <img src="/image/github-icon.png" class="w-10 h-10" />
          <div class="text-base text-black">{{ verification.github_username || '--' }}</div>
        </div>
        <div class="flex items-center gap-3">
          <img src="/image/linkedin-icon.png" class="w-10 h-10" />
          <div class="text-base text-black">{{ verification.linkedin_url || '--' }}</div>
        </div>
        <div class="flex items-center gap-3">
          <img src="/image/gmail.png" class="w-10 h-10" />
          <div class="text-base text-black">{{ verification.google_scholar_url || '--' }}</div>
        </div>
        <div class="flex items-center gap-3">
          <img src="/image/x-icon.png" class="w-10 h-10" />
          <div class="text-base text-black">{{ verification.twitter_username || '--' }}</div>
        </div>
      </div>
      <div class="w-full flex flex-col gap-2 mb-2">
        <div>
          <div class="text-xs text-[#8D8D8D] mb-1">Status</div>
          <div class="text-base font-semibold text-black">{{ verification.verification_status }}</div>
        </div>
        <div>
          <div class="text-xs text-[#8D8D8D] mb-1">Submitted on</div>
          <div class="text-base text-black">{{ formatDate(verification.created_at) }}</div>
        </div>
        <div>
          <div class="text-xs text-[#8D8D8D] mb-1">Verified on</div>
          <div class="text-base text-black">{{ formatDate(verification.completed_at) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  verification: { type: Object, required: true }
})

function formatDate(dateStr) {
  if (!dateStr) return '--';
  const d = new Date(dateStr)
  if (isNaN(d.getTime())) return dateStr;
  const pad = n => n.toString().padStart(2, '0')
  const yyyy = d.getFullYear()
  const mm = pad(d.getMonth() + 1)
  const dd = pad(d.getDate())
  const hh = pad(d.getHours())
  const min = pad(d.getMinutes())
  return `${yyyy}-${mm}-${dd} ${hh}:${min}`
}
</script>

<style scoped>
/* 视觉稿100%还原的样式可根据需要进一步细化 */

/* 自定义滚动条样式 - 去掉上下箭头 */
.verification-scroll::-webkit-scrollbar {
  width: 6px;
}

.verification-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.verification-scroll::-webkit-scrollbar-thumb {
  background: rgba(203, 124, 93, 0.3);
  border-radius: 3px;
}

.verification-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(203, 124, 93, 0.5);
}

/* 去掉滚动条上下箭头 */
.verification-scroll::-webkit-scrollbar-button {
  display: none;
}

/* Firefox 滚动条样式 */
.verification-scroll {
  scrollbar-width: thin;
  scrollbar-color: rgba(203, 124, 93, 0.3) transparent;
}
</style> 