<template>
  <div class="border rounded-full border-white bg-white p-0.5 f-cer" :style="{
    width: `${size}px`,
    height: `${size}px`,
    minWidth: `${size}px`,
    minHeight: `${size}px`
  }">
    <img 
    :src="src" class="rounded-full w-full h-full object-cover" :alt="alt || 'avatar'" @error="handleImageError" />
  </div>
</template>

<script setup lang="ts">
interface Props {
  src: string
  size?: number
  alt?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 90,
  alt: ''
})

if (import.meta.env.DEV) {
  if (typeof props.size !== 'number') {
    console.error(
      `Invalid prop: size must be a number, got ${typeof props.size}. ` +
      `Value: ${props.size}`
    )
  }
}

const handleImageError = (event: Event) => {
  const imgElement = event.target as HTMLImageElement
  console.log('handle img error')
  imgElement.src = '/image/avator.png'
}
</script>