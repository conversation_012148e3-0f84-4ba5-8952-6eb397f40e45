<template>
  <div v-if="show" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
    <div class="relative">
      <div
        class="rounded-2xl shadow-xl p-6 w-[1200px] h-[610px] bg-gradient-to-b from-[#FFFFFF] to-[#F4F2F1] dark:from-[#141415] dark:to-[#141415] border border-transparent dark:border-[#27282D]"
        data-card-id="share-card"
      >
        <div>
          <div class="w-[850px]">
            <!-- 顶部用户信息 -->
            <div class="flex items-center justify-start h-[80px]">
              <img :src="user?.avatar" class="w-20 h-20 rounded-full mr-4" />
              <div class="flex flex-col flex-1" style="gap: 16px;">
                <div class="flex items-center gap-4">
                  <h2 class="text-2xl font-bold whitespace-nowrap truncate max-w-[300px]" style="font-size: 24px;">{{ user?.name }}</h2>
                  <div class="flex items-center gap-1 text-sm text-gray-500 flex-1">
                    <img src="/image/share-ver.png" alt="verified" class="w-4 h-4" />
                    <span class="truncate">{{ user?.role }}</span>
                  </div>
                </div>
                <div class="flex items-center gap-8">
                  <p class="text-lg font-semibold">
                    <span style="font-family: 'UDC 1.04'; font-weight: 700; font-size: 24px;">{{ formatThousand(user?.papers || 0) }}</span> <span class="text-[#6C6C6C] font-400" style="font-size: 14px;">Papers</span>
                  </p>
                  <p class="text-lg font-semibold">
                    <span style="font-family: 'UDC 1.04'; font-weight: 700; font-size: 24px;">{{ formatThousand(user?.citations || 0) }}</span> <span class="text-[#6C6C6C] font-400" style="font-size: 14px;">Citations</span>
                  </p>
                </div>
              </div>
            </div>

            <div class="flex items-center justify-between mt-6">
              <!-- 饼图区域 -->
              <div
                class="flex flex-col items-center justify-between h-[365px] w-[449px] rounded-2xl border border-gray-200 dark:border-[#27282D] shadow-lg bg-[#FFFFFF]/60 dark:bg-[#14141580]"
                style="backdrop-filter: blur(34px);"
              >
                <div class="w-full">
                  <p class="fx-cer gap-2 mt-4 ml-4 text-black dark:text-[#FAF9F5] font-700" style="font-size: 16px;">
                    <img src="/image/share-overview.png" alt="insight" class="w-4 h-4" /> Insight
                  </p>
                  <div class="grid grid-cols-3 gap-4 mt-6">
                    <div class="text-center relative">
                      <p class="text-2xl font-bold" style="font-family: 'UDC 1.04'; font-weight: 700; font-size: 24px;">{{ formatThousand(props.stats?.firstAuthor || 0) }}</p>
                      <p class="text-sm text-gray-500" style="font-size: 12px;">1st Author Papers</p>
                      <div class="absolute right-0 top-1/2 transform -translate-y-1/2 w-px h-8 bg-gray-300"></div>
                    </div>
                    <div class="text-center relative">
                      <p class="text-2xl font-bold" style="font-family: 'UDC 1.04'; font-weight: 700; font-size: 24px;">{{ formatThousand(props.stats?.total || 0) }}</p>
                      <p class="text-sm text-gray-500" style="font-size: 12px;">Total</p>
                      <div class="absolute right-0 top-1/2 transform -translate-y-1/2 w-px h-8 bg-gray-300"></div>
                    </div>
                    <div class="text-center">
                      <p class="text-2xl font-bold" style="font-family: 'UDC 1.04'; font-weight: 700; font-size: 24px;">{{ formatThousand(props.stats?.citation || 0) }}</p>
                      <p class="text-sm text-gray-500" style="font-size: 12px;">1st Author Citations</p>
                    </div>
                  </div>
                </div>
                <div style="width: 449px; margin-top: -20px;">
                  <DonutChart :conference-distribution="conferenceDistribution" :top-tier-papers="topTierPapers" />
                </div>
              </div>

              <!-- 年收入和榜样信息 -->
              <div class="flex flex-col items-center justify-between gap-3" style="flex: 0 0 auto;">
                <div class="w-[386px] h-[153px] flex flex-col p-4 rounded-lg relative border border-gray-200 dark:border-[#27282D] shadow-lg bg-[#FFFFFF]/60 dark:bg-[#14141580]" style="backdrop-filter: blur(34px);">
                  <p class="fx-cer gap-2 text-black dark:text-[#FAF9F5] font-700" style="font-size: 16px;">
                    <img src="/image/share-salary.png" alt="salary" class="w-4 h-4" /> Salary
                  </p>
                  <div class="absolute" style="top: 48px; left: 50%; transform: translateX(-50%);">
                    <p class="font-bold font-700 text-center text-9" style="font-family: 'Poppins'; font-weight: 600; font-size: 36px;">
                      ${{ formatSalaryDisplay(props.income || 0) }}
                    </p>
                    <p class="text-sm text-gray-500 text-center">Earnings Per Year</p>
                  </div>
                </div>

                <div class="w-[386px] h-[199px] flex flex-col p-4 rounded-lg relative border border-gray-200 dark:border-[#27282D] shadow-lg bg-[#FFFFFF]/60 dark:bg-[#14141580]" style="backdrop-filter: blur(34px);">
                  <p class="fx-cer gap-2 text-black dark:text-[#FAF9F5] font-700" style="font-size: 16px;">
                    <img src="/image/share-role.png" alt="role model" class="w-4 h-4" /> Role Model
                  </p>
                  <div class="flex items-center gap-3 absolute" style="top: 50px; left: 16px;">
                    <img :src="props.roleModel?.avatar" class="rounded-full mr-2" style="width: 60px; height: 60px;" />
                    <div class="flex flex-col justify-center" style="gap: 9px;">
                      <p class="text-sm font-semibold leading-5" style="font-size: 20px;">{{ props.roleModel?.name }}</p>
                      <p class="text-xs text-gray-500 leading-5 fx-cer gap-2" style="font-size: 14px;">
                        <img src="/image/share-ver.png" alt="verified" class="w-4 h-4" /> {{ props.roleModel?.title }}
                      </p>
                    </div>
                  </div>
                  <div
                    class="w-[346px] px-2 py-3 text-3 bg-[#FDF0EB] dark:bg-[#222222] font-400 leading-3.5 rounded-1 absolute fx-cer gap-2 text-[#495160] dark:text-[#C6C6C6]"
                    style="bottom: 20px; left: 20px; right: 20px; width: auto;"
                    data-achievement-bg
                  >
                    <img src="/image/share-medal.png" alt="medal" class="w-4 h-4" />
                    {{ props.roleModel?.achievement }}
                  </div>
                </div>
              </div>
            </div>

            <img
              :src="user?.avatar"
              alt=""
              class="absolute top-[264px] right-[150px] w-[45px] h-[45px] rounded-full"
            />
            
            <!-- 右侧装饰图片 -->
            <img
              src="/image/gpic.png"
              alt="decoration"
              class="absolute top-[10px] right-[10px] w-[600px] h-auto z-0 opacity-80"
            />
          </div>
          <div class="fx-cer justify-between border-t dark:border-[#323232] h-[70px] mt-7.5">
            <img
              :src="isDark ? '/image/darklogo2.png' : '/image/newlogo1.png'"
              width="95"
              height="42"
              :alt="isDark ? 'DINQ dark logo' : 'DINQ logo'"
            />
            <div class="flex items-center gap-3" data-action-buttons>
              <!-- Download Button -->
              <button
                class="fx-cer bg-[#FFFFFF]/60 dark:bg-[#14141580] border border-gray-200 dark:border-[#27282D] rounded-full py-2 px-4 text-black dark:text-white gap-2 transition-all duration-200 select-none dark:shadow-lg disabled:opacity-70 disabled:cursor-not-allowed hover:bg-[#F5F5F5] dark:hover:bg-[#27282D] disabled:hover:bg-[#FFFFFF]/60 dark:disabled:hover:bg-[#14141580] min-h-[40px] cursor-pointer"
                :disabled="isDownloading"
                @click="handleDownload"
                style="backdrop-filter: blur(34px);"
              >
                <div
                  v-if="isDownloading"
                  class="animate-spin w-4 h-4 border-2 border-gray-400 dark:border-gray-300 border-t-transparent rounded-full pointer-events-none"
                ></div>
                <div v-else class="i-material-symbols:download w-4 h-4 pointer-events-none"></div>
                <span class="text-sm font-medium pointer-events-none">{{ isDownloading ? 'Downloading...' : 'Download' }}</span>
              </button>
              
              <!-- Share Button -->
              <ShareButton card-id="share-card" :is-dark="isDark" variant="transparent" />
            </div>
          </div>
        </div>
      </div>

      <button
        class="absolute top-4 right-4 p-2 rounded-full transition-colors bg-transparent dark:bg-[#141415] hover:bg-black/10 dark:hover:bg-white/10"
        @click="$emit('close')"
      >
        <div class="i-carbon:close text-xl text-gray-500"></div>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { defineProps, ref } from 'vue'
  import html2canvas from 'html2canvas-pro'
  import DonutChart from '../DonutChart/index.vue'
  import SvgIcon from '../SvgIcon/index.vue'
  import ShareButton from '../ShareButton/index.vue'
  import { formatThousand } from '~/utils'

  const props = defineProps({
    show: Boolean,
    user: Object,
    stats: Object,
    income: Number,
    roleModel: Object,
    conferenceDistribution: Object,
    topTierPapers: Number,
    isDark: Boolean,
  })

  const isDownloading = ref(false)

  // 下载功能
  const handleDownload = async () => {
    if (isDownloading.value || !process.client) return

    isDownloading.value = true
    try {
      const elementToCapture = document.querySelector('[data-card-id="share-card"]')
      if (!elementToCapture) {
        throw new Error('Share card element not found')
      }

      // 等待一小段时间确保所有元素都渲染完成
      await new Promise(resolve => setTimeout(resolve, 500))

      // 使用html2canvas-pro，它对现代CSS有更好的支持
      const canvas = await html2canvas(elementToCapture as HTMLElement, {
        backgroundColor: '#ffffff',
        scale: 2,
        useCORS: true,
        allowTaint: true,
        logging: false,
        imageTimeout: 15000,
        // html2canvas-pro有更好的SVG和CSS支持，所以简化配置
        foreignObjectRendering: false,
        // 确保元素完全在视图中
        scrollX: 0,
        scrollY: 0,
        // 在截图时替换按钮为版权信息
        onclone: (clonedDoc) => {
          const clonedElement = clonedDoc.querySelector('[data-card-id="share-card"]')
          if (clonedElement) {
            // 查找按钮容器并替换为版权信息和二维码
            const buttonContainer = clonedElement.querySelector('[data-action-buttons]')
            if (buttonContainer) {
              // 创建右下角信息容器（版权信息+二维码）
              const bottomRightContainer = clonedDoc.createElement('div')
              bottomRightContainer.style.cssText = 'position: absolute; bottom: 16px; right: 16px; display: flex; align-items: center; gap: 12px; z-index: 10;'

              // 创建版权信息元素
              const copyrightDiv = clonedDoc.createElement('div')
              copyrightDiv.className = 'text-3.5 font-400'
              copyrightDiv.style.fontSize = '14px'
              copyrightDiv.style.color = '#666'
              copyrightDiv.textContent = 'Copyright @ 2025 DINQ Inc. All rights reserved'

              // 创建二维码元素
              const qrCode = clonedDoc.createElement('img')
              qrCode.src = '/image/qrcode.png'
              qrCode.alt = 'QR Code'
              qrCode.style.cssText = 'width: 60px; height: 60px; flex-shrink: 0;'

              // 将版权信息和二维码添加到右下角容器
              bottomRightContainer.appendChild(copyrightDiv)
              bottomRightContainer.appendChild(qrCode)

              // 替换按钮容器为右下角容器
              buttonContainer.parentNode?.replaceChild(bottomRightContainer, buttonContainer)
            }
            
            // 修复深色模式下的边框颜色
            const isDarkMode = props.isDark
            if (isDarkMode) {
              // 修复所有卡片的边框颜色
              const cardElements = clonedElement.querySelectorAll('.border-gray-200')
              cardElements.forEach(card => {
                card.style.borderColor = '#27282D'
              })
              
              // 修复主卡片的边框颜色
              const mainCard = clonedElement.querySelector('[data-card-id="share-card"] > div')
              if (mainCard) {
                mainCard.style.borderColor = '#27282D'
              }
              
              // 修复卡片标题文字颜色
              const titleElements = clonedElement.querySelectorAll('.text-black')
              titleElements.forEach(title => {
                title.style.color = '#FAF9F5'
              })
            }
            
            // 修复rolemodel部分的背景色显示问题
            const achievementElement = clonedElement.querySelector('[data-achievement-bg]')
            if (achievementElement) {
              // 检查是否是深色模式
              const isDarkMode = props.isDark
              // 强制设置背景色
              achievementElement.style.backgroundColor = isDarkMode ? '#222222' : '#FDF0EB'
              // 确保文字颜色也正确
              achievementElement.style.color = isDarkMode ? '#C6C6C6' : '#495160'
              // 确保圆角显示
              achievementElement.style.borderRadius = '4px'
            }
          }
        }
      })

      // 创建下载链接
      const link = document.createElement('a')
      link.download = `dinq-role-model-${Date.now()}.png`
      link.href = canvas.toDataURL('image/png', 1.0)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } catch (error) {
      console.error('Download failed:', error)
      // 提供备用方案提示
      alert('Screenshot failed. This might be due to CORS issues with external images. Please try again or contact support.')
    } finally {
      isDownloading.value = false
    }
  }

  // 格式化数字，超过1K时显示为K单位，超过1M时显示为M单位
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(0) + 'K'
    }
    return num.toString()
  }

  // 格式化薪资显示，将 - 替换为 ~ 并应用数字格式化
  const formatSalaryDisplay = (salaryRange: string | number): string => {
    if (!salaryRange) return ''
    
    // 如果是数字，直接格式化
    if (typeof salaryRange === 'number') {
      return formatNumber(salaryRange)
    }
    
    // 先替换分隔符
    let formatted = salaryRange.replace(/-/g, '~')
    
    // 匹配薪资数字并格式化
    // 匹配格式如: "50000~80000 USD/month", "$50000~80000/month", "50000 USD/month"
    formatted = formatted.replace(/(\d+(?:,\d{3})*)/g, (match) => {
      const num = parseInt(match.replace(/,/g, ''))
      return formatNumber(num)
    })
    
    return formatted
  }
</script>

<style scoped>
  .animate-spin {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
</style>
