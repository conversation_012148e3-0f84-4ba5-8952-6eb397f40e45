<template>
  <motion.div
    :initial="{ x: props.x, opacity: 0 }"
    :in-view="{ x: 0, opacity: 1 }"
    :in-view-options="{ once: true }"
    class="dark:bg-[#141415] border border-[#8C827E] dark:border-[#27282D] backdrop-blur-[34px] flex flex-col gap-5 justify-between role-model-card"
    style="border-radius: 15px; padding: 20px; background-color: #FFFFFF99;"
    :data-card-id="cardId"
  >
    <div class="flex items-center justify-between">
      <div class="fx-cer gap-2">
        <div
          class="rounded-md f-cer justify-center min-w-6 wh-6 bg-[#F1E0D9] dark:bg-[#292929] border border-special-cream dark:border-[#3E3E3E] cursor-pointer"
        >
          <SvgIcon :name="icon" class="text-[#CB7C5D] dark:text-[#FAF9F5]" />
        </div>
        <span class="text-base text-black dark:text-white font-bold">{{ title }}</span>
      </div>
      <ShareButton 
        :card-id="cardId" 
        type="popup" 
        :is-dark="isDarkMode"
        variant="colored"
        @share-popup="$emit('share-popup')" 
      />
    </div>
    <div class="flex-1">
      <slot></slot>
    </div>
    <div class="text-right text-xs card-footer-text">
      Made with <a href="/" class="dinq-link">DINQ</a>-Al Powered Academic Talent Analysis
    </div>
  </motion.div>
</template>

<script setup lang="ts">
  import { motion } from 'motion-v'
  import { ref, onMounted } from 'vue'
  import ShareButton from '../ShareButton/index.vue'

  const props = defineProps({
    icon: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
    x: {
      type: Number,
      default: 50,
      validator: (value: number) => {
        return typeof value === 'number'
      },
    },
    cardId: {
      type: String,
      default: '',
    },
  })

  const emit = defineEmits<{
    'share-popup': []
  }>()

  // 检测深色模式
  const isDarkMode = ref(false)

  onMounted(() => {
    // 初始化深色模式检测
    isDarkMode.value = document.documentElement.classList.contains('dark')
    
    // 监听深色模式变化
    const observer = new MutationObserver(() => {
      isDarkMode.value = document.documentElement.classList.contains('dark')
    })
    observer.observe(document.documentElement, { 
      attributes: true, 
      attributeFilter: ['class'] 
    })
  })
</script>

<style scoped>
.card-footer-text {
  color: #7A7A7A;
}

.dark .card-footer-text {
  color: #7A7A7A;
}

.dinq-link {
  color: #CB7C5D;
  text-decoration: none;
}

.role-model-card {
  backdrop-filter: blur(34px);
}

.dark .role-model-card {
  box-shadow: 0px 3px 8px 0px #0000001A;
  backdrop-filter: none;
  background-color: #141415 !important;
}
</style>
