<template>
  <div class="w-full mx-auto mt-10 mb-24">
    <h2 class="faq-title text-center font-extrabold mb-10 clash-semibold">
      Frequently asked questions
    </h2>
    <div class="space-y-4">
      <div
        v-for="(item, idx) in faqs"
        :key="idx"
        class="bg-white rounded-2xl overflow-hidden faq-item"
        :class="{ 'is-open': isOpen(idx) }"
      >
        <button
          class="faq-question w-full text-left px-7 py-6 focus:outline-none flex items-start justify-between bg-white"
          @click="toggle(idx)"
        >
          <span class="flex-1 pr-4">{{ item.q }}</span>
          <span class="flex-shrink-0 mt-1">
            <svg
              width="24"
              height="24"
              fill="none"
              stroke="#B0B0B0"
              stroke-width="2"
              viewBox="0 0 24 24"
              class="transform transition-transform duration-300 ease-in-out"
              :class="{ 'rotate-180': isOpen(idx) }"
            >
              <path d="M6 9l6 6 6-6" />
            </svg>
          </span>
        </button>
        <div 
          class="accordion-content"
          :class="{ 'is-open': isOpen(idx) }"
        >
          <div
            v-html="linkify(item.a)"
            class="faq-answer px-7 pb-4 pt-4 bg-white whitespace-pre-line"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  const openItems = ref<Set<number>>(new Set([0])) // 使用Set来管理多个打开的项目
  const faqs = [
    {
      q: 'Q1: What is DINQ? How can I use it?',
      a: `DINQ is an intelligent platform designed to help you analyze, compare, or discover AI researchers in under 60 seconds. Whether you're evaluating a scholar's work, benchmarking experts, or searching for collaborators, DINQ simplifies the process with quick, actionable insights.`,
    },
    {
      q: 'Q2: Is DINQ free to use?',
      a: 'DINQ offers a free tier with 5 credits per month for basic analyses and comparisons. For users with higher needs, paid plans—Standard (50 credits/month) and Premium (150 credits/month)—unlock extended credits and premium features.',
    },
    {
      q: 'Q3: What are credits, and why do they matter?',
      a: "Credits are DINQ's usage currency. Each analysis or comparison consumes 1 credit, ensuring fair resource allocation. Credits allow you to scale your research operations efficiently.",
    },
    {
      q: 'Q4: How can I earn or purchase credits?',
      a: `Credits are available through:
        Free Plan: 5 credits/month (automatically replenished).
        Standard Subscription: 50 credits/month.
        Premium Subscription: 150 credits/month.
        For pricing details and benefits, visit our Subscription Page.`,
    },
    {
      q: 'Q5: Who developed DINQ? Are you hiring?',
      a: "DINQ was created by a passionate, fully remote team dedicated to empowering AI research discovery. We value curiosity, collaboration, and continuous growth. If you're excited about our mission and want to join us, send your <NAME_EMAIL> with a brief note on why you'd be a great fit. We can't wait to hear from you!",
    },
  ]
  
  function toggle(idx: number) {
    const newOpenItems = new Set(openItems.value)
    if (newOpenItems.has(idx)) {
      newOpenItems.delete(idx)
    } else {
      newOpenItems.add(idx)
    }
    openItems.value = newOpenItems
  }

  function isOpen(idx: number) {
    return openItems.value.has(idx)
  }

  // 处理文本的方法：匹配邮箱和网址并返回 HTML 链接
  function linkify(text: string) {
    if (!text) return ''

    // 邮箱正则
    const emailRegex = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g

    // URL 正则（支持 http(s) 和 www 开头）
    const urlRegex = /((https?:\/\/|www\.)[^\s]+)/g

    return (
      text
        // 邮箱链接
        .replace(
          emailRegex,
          '<a href="mailto:$1" target="_blank" class="text-blue-600 underline">$1</a>'
        )
        // URL 链接
        .replace(urlRegex, match => {
          const url = match.startsWith('http') ? match : 'http://' + match
          return `<a href="${url}" target="_blank" class="text-blue-600 underline">${match}</a>`
        })
    )
  }
</script>

<style scoped>
  .faq-title {
    font-family: 'Alexandria', sans-serif;
    font-weight: 600;
    font-size: 42px;
    line-height: 130%;
    color: #1D1D1D;
  }

  .faq-item {
    border: 1px solid;
    border-image-source: linear-gradient(180deg, #FFFFFF 0%, rgba(255, 255, 255, 0.4) 100%);
    box-shadow: none;
  }

  .faq-item.is-open {
    border: 1px solid #EDEDED;
    border-image: none;
    box-shadow: 0px 3px 13px 0px #30303014;
  }

  .faq-question {
    font-family: 'Alexandria', sans-serif;
    font-weight: 500;
    font-size: 16px;
    line-height: 16px;
    color: #0C0C0C;
  }

  .faq-answer {
    font-family: 'Alexandria', sans-serif;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #51575C;
    position: relative;
  }

  .faq-answer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 28px; /* px-7 = 28px */
    right: 28px; /* px-7 = 28px */
    height: 1px;
    border-top: 1px dashed #E3E3E3;
    border-image: repeating-linear-gradient(to right, #E3E3E3 0, #E3E3E3 4px, transparent 4px, transparent 8px) 1;
  }

  .accordion-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .accordion-content.is-open {
    max-height: 500px; /* 足够大的值来容纳内容 */
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .faq-title {
      font-size: 32px;
    }
    
    .faq-question {
      font-size: 14px;
      line-height: 18px;
      padding-right: 40px; /* 给箭头留更多空间 */
    }
    
    .faq-answer {
      font-size: 13px;
      line-height: 20px;
    }
  }

  @media (max-width: 640px) {
    .faq-title {
      font-size: 28px;
      margin-bottom: 24px;
    }
    
    .faq-question {
      font-size: 13px;
      line-height: 16px;
      white-space: normal; /* 允许换行 */
      word-break: break-word; /* 长单词可以换行 */
      hyphens: auto; /* 自动连字符 */
      padding-right: 50px; /* 给箭头留足够空间 */
    }
    
    .faq-answer {
      font-size: 12px;
      line-height: 18px;
    }
    
    .w-full.mx-auto.mt-10.mb-24 {
      margin-top: 24px;
      margin-bottom: 16px;
      padding: 0 16px;
    }
  }

  @media (max-width: 480px) {
    .faq-title {
      font-size: 24px;
    }
    
    .faq-question {
      font-size: 12px;
      line-height: 14px;
    }
    
    .faq-answer {
      font-size: 11px;
      line-height: 16px;
    }
  }
</style> 