<template>
  <svg width="100" height="66" viewBox="0 0 88 66" fill="none" xmlns="http://www.w3.org/2000/svg">
    <!-- 背景半圆 (100% 进度条) -->
    <path :d="backgroundPath" stroke="#E1DAD7" stroke-width="8" stroke-linecap="round"/>
    
    <!-- 进度半圆 (动态计算) -->
    <path :d="progressPath" :stroke="gradientUrl" stroke-width="8" stroke-linecap="round"/>
    
    <!-- 进度指示器 (动态位置，在圆弧上居中) -->
    <circle :cx="indicatorX" :cy="indicatorY" r="6" fill="#CB7C5D"/>
    <circle :cx="indicatorX" :cy="indicatorY" r="3" fill="white"/>
    
    <!-- 百分比文本 -->
    <text 
      :x="centerX" 
      :y="46" 
      text-anchor="middle" 
      dominant-baseline="baseline"
      style="font-family: 'UDC 1.04'; font-weight: 700; font-size: 22px; line-height: 120%; letter-spacing: 0%; fill: #CB7C5D;"
    >
      {{ Math.round(progress) }}%
    </text>
    
    <!-- 渐变定义 -->
    <defs>
      <linearGradient :id="gradientId" x1="82.5" y1="38.54" x2="4.5" y2="38.54" gradientUnits="userSpaceOnUse">
        <stop stop-color="#CB7C5D"/>
        <stop offset="1" stop-color="#EEC3B2"/>
      </linearGradient>
    </defs>
  </svg>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// 只需要传入progress参数
interface Props {
  progress: number // 0-100 的进度值
}

const props = defineProps<Props>()

// 固定的基本参数
const centerX = 43.5
const centerY = 44
const radius = 39
const startX = 4.5
const startY = 44
const endX = 82.5
const endY = 44

// 生成唯一的渐变ID
const gradientId = computed(() => `gradient_${Math.random().toString(36).substr(2, 9)}`)
const gradientUrl = computed(() => `url(#${gradientId.value})`)

// 背景半圆路径 (固定)
const backgroundPath = computed(() => {
  return `M${endX} ${endY}C${endX} 22.4609 65.0391 5 ${centerX} 5C21.9609 5 ${startX} 22.4609 ${startX} ${startY}`
})

// 计算进度终点坐标
const progressEndPoint = computed(() => {
  const clampedProgress = Math.max(0, Math.min(100, props.progress))
  const angle = (Math.PI * clampedProgress) / 100 // 转换为弧度
  
  const x = centerX + radius * Math.cos(Math.PI - angle)
  const y = centerY - radius * Math.sin(Math.PI - angle)
  
  return { x, y }
})

// 进度半圆路径 (动态计算，使用SVG弧线)
const progressPath = computed(() => {
  if (props.progress <= 0) return ''
  
  const clampedProgress = Math.max(0, Math.min(100, props.progress))
  const endPoint = progressEndPoint.value
  
  if (clampedProgress >= 100) {
    return backgroundPath.value
  }
  
  // 对于半圆进度条，我们总是使用小弧(large-arc-flag=0)
  // 因为我们的进度永远不会超过180度
  return `M${startX} ${startY}A${radius} ${radius} 0 0 1 ${endPoint.x} ${endPoint.y}`
})

// 指示器位置 (在圆弧上居中，不是相切)
const indicatorX = computed(() => {
  if (props.progress <= 0) return startX
  if (props.progress >= 100) return endX
  
  const angle = (Math.PI * props.progress) / 100
  return centerX + radius * Math.cos(Math.PI - angle)
})

const indicatorY = computed(() => {
  if (props.progress <= 0) return startY
  if (props.progress >= 100) return endY
  
  const angle = (Math.PI * props.progress) / 100
  return centerY - radius * Math.sin(Math.PI - angle)
})
</script> 