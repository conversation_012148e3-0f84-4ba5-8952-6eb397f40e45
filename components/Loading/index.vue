<template>
  <transition name="fade">
    <div
      v-if="visible"
      class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
      @click.self
    >
      <transition name="scale">
        <div
          class="bg-white dark:bg-[#141415] rounded-5 shadow-xl px-6 py-4 w-130 text-center rel h-107"
          v-show="visible"
        >
          <h2 class="text-7 font-bold mb-2 mt-2 text-black dark:text-white">Analyzing</h2>
          <div
            ref="scrollContainer"
            class="w-full h-80 overflow-y-auto px-2 my-4 dark:[&::-webkit-scrollbar-thumb]:bg-[#1f1f1f] dark:[&::-webkit-scrollbar-thumb:hover]:bg-[#2a2a2a]"
            style="-ms-overflow-style: none;"
          >
            <div class="space-y-4 text-base w-full">
              <div
                class="flex items-start gap-1.5 pl-8"
                v-for="(item, index) in data"
                :key="index"
              >
                <div
                  v-if="index === data.length - 1"
                  class="i-eos-icons:loading text-#21C05D wh-4 min-w-4 min-h-4 mt-1.5"
                ></div>
                <div
                  v-else
                  class="i-icon-park-solid:check-one text-#21C05D wh-4 min-w-4 min-h-4 mt-1.5"
                ></div>
                <span class="text-xs leading-7.5 text-gray-700 dark:text-gray-300 truncate">{{ item }}</span>
              </div>
            </div>
          </div>
          <div class="abs top-4 right-4">
            <div
              class="wh-6 text-#333 i-material-symbols:close cursor-pointer opacity-32 dark:text-white"
              @click="close"
            ></div>
          </div>
        </div>
      </transition>
    </div>
  </transition>
</template>

<script setup lang="ts">
  import { watch, ref, nextTick } from 'vue'

  const props = defineProps<{
    visible: boolean
    data: string[]
  }>()

  const emit = defineEmits<{
    (e: 'update:visible', val: boolean): void
  }>()

  const close = () => {
    emit('update:visible', false)
  }

  const scrollContainer = ref<HTMLElement | null>(null)

  watch(
    () => props.data,
    () => {
      nextTick(() => {
        if (scrollContainer.value) {
          scrollContainer.value.scrollTop = scrollContainer.value.scrollHeight
        }
      })
    },
    { deep: true }
  )
</script>

<style scoped>
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }

  .scale-enter-active,
  .scale-leave-active {
    transition: transform 0.3s ease;
  }

  .scale-enter-from,
  .scale-leave-to {
    transform: scale(0.8);
  }

  /* 隐藏滚动条箭头 */
  ::-webkit-scrollbar-button {
    display: none;
  }
  
  /* 自定义滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px;
  }
  
  ::-webkit-scrollbar-track {
    background: transparent;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: #999;
  }
</style>
