<template>
  <transition name="fade">
    <div
      v-if="visible"
      class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
      @click.self
    >
      <transition name="scale">
        <div
          class="rounded-2xl shadow-xl px-8 py-6 text-center relative"
          :style="{
            width: '360px',
            height: '162px',
            backgroundColor: isDark ? '#141415' : '#FFFFFF'
          }"
          v-show="visible"
        >
          <!-- DINQing 标题 -->
          <h1 
            class="mb-2"
            :style="{
              fontFamily: 'Poppins, sans-serif',
              fontWeight: '700',
              fontSize: '18px',
              color: isDark ? '#FAF9F5' : '#000000'
            }"
          >
            DINQing
          </h1>

          <!-- Searching Talent 副标题 -->
          <p 
            class="mb-4"
            :style="{
              fontFamily: 'Poppins, sans-serif',
              fontWeight: '500',
              fontSize: '14px',
              color: isDark ? '#C6C6C6' : '#585858'
            }"
          >
            Searching Talent
          </p>

          <!-- 进度条 -->
          <div 
            class="w-full rounded-full h-3 mb-3"
            :style="{
              backgroundColor: isDark ? '#323232' : '#FBEAE3'
            }"
          >
            <div 
              class="h-3 rounded-full transition-all duration-1000 ease-out"
              style="background-color: #CB7C5D;"
              :style="{ width: `${progress}%` }"
            ></div>
          </div>

          <!-- 时间估算 -->
          <p class="text-base font-medium text-[#CB7C5D] dark:text-[#7A7A7A]">
            {{ estimatedTime }} seconds
          </p>
        </div>
      </transition>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  (e: 'update:visible', val: boolean): void
}>()

const progress = ref(0)
const estimatedTime = ref(30)
const isDataLoaded = ref(false)

// 检测深色模式
const isDark = ref(false)

const checkDarkMode = () => {
  if (process.client) {
    isDark.value = document.documentElement.classList.contains('dark')
  }
}

onMounted(() => {
  checkDarkMode()
  
  // 监听主题变化
  const observer = new MutationObserver(() => {
    checkDarkMode()
  })
  
  if (process.client) {
    observer.observe(document.documentElement, { 
      attributes: true, 
      attributeFilter: ['class'] 
    })
  }
  
  onUnmounted(() => {
    observer.disconnect()
  })
})

let progressInterval: NodeJS.Timeout | null = null

// 智能进度更新
const updateProgress = () => {
  const slowProgressTime = 5000 // 5秒慢速进度
  const interval = 100 // 每100ms更新一次
  const slowIncrement = (80 / (slowProgressTime / interval)) // 慢速增长到80%

  progressInterval = setInterval(() => {
    if (!props.visible) {
      clearInterval(progressInterval!)
      return
    }

    if (isDataLoaded.value) {
      // 数据已加载，快速完成进度条
      if (progress.value < 100) {
        progress.value = Math.min(100, progress.value + 15) // 更快速增长
        estimatedTime.value = Math.max(0, estimatedTime.value - 1)
      } else {
        clearInterval(progressInterval!)
        estimatedTime.value = 0
      }
    } else {
      // 数据未加载，慢速增长
      if (progress.value < 80) {
        progress.value += slowIncrement
        // 更新剩余时间估算
        const remainingProgress = 80 - progress.value
        estimatedTime.value = Math.max(1, Math.round((remainingProgress / 80) * 5))
      } else {
        // 到达80%后，如果数据还没来，就停在80%和1秒
        progress.value = 80
        estimatedTime.value = 1
      }
    }
  }, interval)
}

// 快速完成进度条
const completeProgress = () => {
  isDataLoaded.value = true
}

// 重置状态
const resetProgress = () => {
  progress.value = 0
  estimatedTime.value = 5 // 起步时间为5秒
  isDataLoaded.value = false
  
  if (progressInterval) {
    clearInterval(progressInterval)
    progressInterval = null
  }
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetProgress()
    updateProgress()
  } else {
    if (progressInterval) {
      clearInterval(progressInterval)
      progressInterval = null
    }
  }
})

// 暴露方法给父组件调用
defineExpose({
  completeProgress
})
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.scale-enter-active,
.scale-leave-active {
  transition: transform 0.3s ease;
}

.scale-enter-from,
.scale-leave-to {
  transform: scale(0.9);
}

/* 基础样式已通过动态绑定处理 */

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

.dark ::-webkit-scrollbar-thumb {
  background: #4a5568;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #2d3748;
}
</style> 