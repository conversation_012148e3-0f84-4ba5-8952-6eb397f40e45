<template>
  <motion.div
    :initial="{ opacity: 0, y: 10 }"
    :animate="{ opacity: 1, y: 0 }"
    :transition="{ duration: 0.3, ease: 'easeInOut', delay: 0.2 }"
    class="f-cer mt-7.5 mb-7"
  >
    <div
      class="custom-input border rounded-full bg-white border-black min-h-16 w-160 p-1 flex items-center justify-between gap-4 pl-7.5"
    >
      <TalentPoolInput ref="searchInputRef" :initial-query="initialQuery" @enter-search="handleSearch" />
      <TalentPoolButton @click="handleSearchClick" />
    </div>
  </motion.div>
</template>

<script setup lang="ts">
  import { motion } from 'motion-v'
  import TalentPoolInput from '~/components/SearchInput/TalentPoolInput.vue'
  import TalentPoolButton from '../ActionButton/TalentPoolButton.vue'
  
  // 定义props
  const props = defineProps<{
    initialQuery?: string
  }>()
  
  // 定义emits
  const emit = defineEmits<{
    search: [query: string]
  }>()
  
  const searchInputRef = ref()
  
  const handleSearch = (query: string) => {
    console.log('TalentPoolBar handleSearch called with query:', query)
    emit('search', query || '')
  }
  
  const handleSearchClick = () => {
    console.log('TalentPoolBar search button clicked')
    // 点击搜索按钮时，获取当前应该搜索的内容（用户输入或当前标签）
    const searchValue = searchInputRef.value?.getCurrentSearchValue?.() || ''
    console.log('Search button clicked, search value:', searchValue)
    emit('search', searchValue)
  }
</script>

<style scoped></style> 