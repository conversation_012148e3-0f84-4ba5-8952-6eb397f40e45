<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-black/10">
    <div class="bg-white rounded-3xl w-[420px] p-8 relative flex flex-col items-center shadow-xl">
      <div class="absolute right-6 top-6 text-3xl text-[#BDBDBD] cursor-pointer" @click="$emit('close')">×</div>
      <div class="text-2xl font-bold text-black mb-8">Verification</div>
      <div class="flex w-full gap-4 mb-6">
        <div class="flex-1 verify-type-card hover:shadow-md" @click="openVerificationFormModal">
          <div class="verify-type-title">I'm Looking For Opportunities</div>
        </div>
        <div class="flex-1 verify-type-card hover:shadow-md" @click="openVerificationTalentFormModal">
          <div class="verify-type-title">I'm Looking For Talent</div>
        </div>
      </div>
      <div class="flex w-full gap-4">
        <div class="flex-1 text-center text-[#8D8D8D] text-base leading-tight">E.G. Students, Researchers, Engineers, Freelancers</div>
        <div class="flex-1 text-center text-[#8D8D8D] text-base leading-tight">E.G. HR Professionals, Recruiters, Company Founders, Project Leads</div>
      </div>
    </div>
  </div>
  <VerificationFormModal v-if="showVerificationFormModal" @close="showVerificationFormModal = false" />
  <VerificationTalentFormModal v-if="showVerificationTalentFormModal" @close="showVerificationTalentFormModal = false" />
</template>

<script setup>
import { ref } from 'vue'
import VerificationFormModal from '../VerificationFormModal/index.vue'
import VerificationTalentFormModal from '../VerificationTalentFormModal/index.vue'

const showVerificationFormModal = ref(false)
const showVerificationTalentFormModal = ref(false)

function openVerificationFormModal() {
  showVerificationFormModal.value = true
  setTimeout(() => {
    // $emit('close')
  }, 0)
}

function openVerificationTalentFormModal() {
  showVerificationTalentFormModal.value = true
  setTimeout(() => {
    // $emit('close')
  }, 0)
}
</script>

<style scoped>
.verify-type-card {
  border: 2px solid #E6BFA7;
  border-radius: 1rem;
  background: #FDF4F0;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
  cursor: pointer;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
}
.verify-type-card:hover {
  background: #CB7C5D;
}
.verify-type-card:hover .verify-type-title {
  color: #fff;
}
.verify-type-title {
  color: #C47A5A;
  font-size: 1.125rem;
  font-weight: bold;
  text-align: center;
  line-height: 1.3;
  transition: color 0.2s;
}
</style> 