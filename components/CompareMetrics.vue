<template>
  <div
    class="compare-metrics-container relative shadow-md mx-auto space-y-8 p-3 border-2 rounded-lg"
  >
    <div>
      <!-- Citations -->
      <div class="rounded-lg h-12 flex items-center justify-center mb-6">
        <!-- l -->
        <div
          class="rounded-lg flex justify-end items-center"
          :class="[
            'w-[600px] leading-[36px]',
            researcher1.total_citations > researcher2.total_citations
              ? 'text-[#CB7C5D] font-bold h-[60px] metrics-card-larger'
              : 'text-[#797979] h-[50px] metrics-card-smaller',
          ]"
          :style="researcher1.total_citations > researcher2.total_citations 
            ? 'font-family: \'Poppins\', sans-serif; font-weight: 600; font-size: 36px; line-height: 36px; letter-spacing: 0%;' 
            : 'font-family: \'Poppins\', sans-serif; font-weight: 500; font-size: 28px; line-height: 36px; letter-spacing: 0%;'"
        >
          <span class="mr-30">
            {{ formatNumber(researcher1.total_citations) }}
          </span>
        </div>
        <!-- a -->
        <div class="absolute w-[200px] flex justify-center item-center">
          <div
            class="h-15 w-[134px] px-4 text-sm rounded-full text-white flex items-center justify-center text-center metric-button"
          >
            Citations
          </div>
        </div>
        <!-- r -->
        <div
          class="rounded-lg flex items-center"
          :class="[
            'w-[600px] leading-[36px]',
            researcher2.total_citations > researcher1.total_citations
              ? 'text-[#CB7C5D] font-bold h-[60px] metrics-card-larger'
              : 'text-[#797979] h-[50px] metrics-card-smaller',
          ]"
          :style="researcher2.total_citations > researcher1.total_citations 
            ? 'font-family: \'Poppins\', sans-serif; font-weight: 600; font-size: 36px; line-height: 36px; letter-spacing: 0%;' 
            : 'font-family: \'Poppins\', sans-serif; font-weight: 500; font-size: 28px; line-height: 36px; letter-spacing: 0%;'"
        >
          <span class="ml-30">
            {{ formatNumber(researcher2.total_citations) }}
          </span>
        </div>
      </div>
      <!-- Top Tier Papers -->
      <div class="rounded-lg h-12 flex items-center justify-center mb-6">
        <!-- l -->
        <div
          class="rounded-lg flex justify-end items-center"
          :class="[
            'w-[600px] leading-[36px]',
            researcher1.top_tier_papers > researcher2.top_tier_papers
              ? 'text-[#CB7C5D] font-bold h-[60px] metrics-card-larger'
              : 'text-[#797979] h-[50px] metrics-card-smaller',
          ]"
          :style="researcher1.top_tier_papers > researcher2.top_tier_papers 
            ? 'font-family: \'Poppins\', sans-serif; font-weight: 600; font-size: 36px; line-height: 36px; letter-spacing: 0%;' 
            : 'font-family: \'Poppins\', sans-serif; font-weight: 500; font-size: 28px; line-height: 36px; letter-spacing: 0%;'"
        >
          <span class="mr-30">
            {{ formatNumber(researcher1.top_tier_papers) }}
          </span>
        </div>
        <div class="absolute w-[200px] flex justify-center item-center">
          <div
            class="h-15 w-[134px] px-4 text-sm rounded-full text-white flex items-center justify-center text-center metric-button"
          >
            Top Tier Papers
          </div>
        </div>
        <!-- r -->
        <div
          class="rounded-lg flex items-center"
          :class="[
            'w-[600px] leading-[36px]',
            researcher2.top_tier_papers > researcher1.top_tier_papers
              ? 'text-[#CB7C5D] font-bold h-[60px] metrics-card-larger'
              : 'text-[#797979] h-[50px] metrics-card-smaller',
          ]"
          :style="researcher2.top_tier_papers > researcher1.top_tier_papers 
            ? 'font-family: \'Poppins\', sans-serif; font-weight: 600; font-size: 36px; line-height: 36px; letter-spacing: 0%;' 
            : 'font-family: \'Poppins\', sans-serif; font-weight: 500; font-size: 28px; line-height: 36px; letter-spacing: 0%;'"
        >
          <span class="ml-30">
            {{ formatNumber(researcher2.top_tier_papers) }}
          </span>
        </div>
      </div>

      <!-- First Author Papers -->
      <div class="rounded-lg h-12 flex items-center justify-center mb-6">
        <!-- l -->
        <div
          class="rounded-lg flex justify-end items-center"
          :class="[
            'w-[600px] leading-[36px]',
            researcher1.first_author_papers > researcher2.first_author_papers
              ? 'text-[#CB7C5D] font-bold h-[60px] metrics-card-larger'
              : 'text-[#797979] h-[50px] metrics-card-smaller',
          ]"
          :style="researcher1.first_author_papers > researcher2.first_author_papers 
            ? 'font-family: \'Poppins\', sans-serif; font-weight: 600; font-size: 36px; line-height: 36px; letter-spacing: 0%;' 
            : 'font-family: \'Poppins\', sans-serif; font-weight: 500; font-size: 28px; line-height: 36px; letter-spacing: 0%;'"
        >
          <span class="mr-30">
            {{ formatNumber(researcher1.first_author_papers) }}
          </span>
        </div>
        <div class="absolute w-[200px] flex justify-center item-center">
          <div
            class="h-15 w-[134px] px-4 text-sm rounded-full text-white flex items-center justify-center text-center metric-button"
          >
            First Author Papers
          </div>
        </div>
        <!-- r -->
        <div
          class="rounded-lg flex items-center"
          :class="[
            'w-[600px] leading-[36px]',
            researcher2.first_author_papers > researcher1.first_author_papers
              ? 'text-[#CB7C5D] font-bold h-[60px] metrics-card-larger'
              : 'text-[#797979] h-[50px] metrics-card-smaller',
          ]"
          :style="researcher2.first_author_papers > researcher1.first_author_papers 
            ? 'font-family: \'Poppins\', sans-serif; font-weight: 600; font-size: 36px; line-height: 36px; letter-spacing: 0%;' 
            : 'font-family: \'Poppins\', sans-serif; font-weight: 500; font-size: 28px; line-height: 36px; letter-spacing: 0%;'"
        >
          <span class="ml-30">
            {{ formatNumber(researcher2.first_author_papers) }}
          </span>
        </div>
      </div>
      <!-- First Author Citations -->
      <div class="rounded-lg h-12 flex items-center justify-center mb-6">
        <!-- l -->
        <div
          class="rounded-lg flex justify-end items-center"
          :class="[
            'w-[600px] leading-[36px]',
            researcher1.first_author_citations > researcher2.first_author_citations
              ? 'text-[#CB7C5D] font-bold h-[60px] metrics-card-larger'
              : 'text-[#797979] h-[50px] metrics-card-smaller',
          ]"
          :style="researcher1.first_author_citations > researcher2.first_author_citations 
            ? 'font-family: \'Poppins\', sans-serif; font-weight: 600; font-size: 36px; line-height: 36px; letter-spacing: 0%;' 
            : 'font-family: \'Poppins\', sans-serif; font-weight: 500; font-size: 28px; line-height: 36px; letter-spacing: 0%;'"
        >
          <span class="mr-30">
            {{ formatNumber(researcher1.first_author_citations) }}
          </span>
        </div>
        <div class="absolute w-[200px] flex justify-center item-center">
          <div
            class="h-15 w-[134px] px-4 text-sm rounded-full text-white flex items-center justify-center text-center metric-button"
          >
            First Author Citations
          </div>
        </div>
        <!-- r -->
        <div
          class="rounded-lg flex items-center"
          :class="[
            'w-[600px] leading-[36px]',
            researcher2.first_author_citations > researcher1.first_author_citations
              ? 'text-[#CB7C5D] font-bold h-[60px] metrics-card-larger'
              : 'text-[#797979] h-[50px] metrics-card-smaller',
          ]"
          :style="researcher2.first_author_citations > researcher1.first_author_citations 
            ? 'font-family: \'Poppins\', sans-serif; font-weight: 600; font-size: 36px; line-height: 36px; letter-spacing: 0%;' 
            : 'font-family: \'Poppins\', sans-serif; font-weight: 500; font-size: 28px; line-height: 36px; letter-spacing: 0%;'"
        >
          <span class="ml-30">
            {{ formatNumber(researcher2.first_author_citations) }}
          </span>
        </div>
      </div>

      <!-- Representative Papers -->
      <div class="representative-papers-container rounded-lg relative">
        <div class="absolute left-1/2 transform -translate-x-1/2" style="top: 30px;">
          <div
            class="h-15 w-[134px] px-4 text-sm rounded-full text-white flex items-center justify-center text-center translate-y-[-50%] metric-button"
          >
            Representative Papers
          </div>
        </div>
        <div class="flex items-center justify-center">
          <div
            :class="[
              'text-2xl w-[600px] leading-[48px] p-4 paper-card-left',
              researcher1.most_cited_paper.citations > researcher2.most_cited_paper.citations
                ? 'text-primary-100 font-bold h-[322px] paper-card-larger'
                : 'text-[#797979] dark:text-white h-[290px] paper-card-smaller',
            ]"
          >
            <div class="h-full flex flex-col">
              <div class="flex items-center justify-center h-[60px]">
                <span
                  :class="[
                    researcher1.most_cited_paper.citations > researcher2.most_cited_paper.citations
                      ? 'text-[#CB7C5D] text-[26px]'
                      : 'text-black dark:text-white text-[20px]',
                    'font-semibold'
                  ]"
                >
                  Citations: {{ formatNumber(researcher1.most_cited_paper.citations) }}
                </span>
              </div>
              <div class="flex-1">
                <div
                  class="h-full relative overflow-hidden"
                  :class="[
                    'rounded-lg border-l-4 p-3',
                    researcher1.most_cited_paper.citations > researcher2.most_cited_paper.citations
                      ? 'paper-border-larger'
                      : 'paper-border-smaller'
                  ]"
                >
                  <!-- 上半部分背景 -->
                  <div class="paper-bg-top absolute inset-0 h-[65%]"
                    :class="[
                      researcher1.most_cited_paper.citations > researcher2.most_cited_paper.citations
                        ? 'paper-bg-top-larger'
                        : 'paper-bg-top-smaller'
                    ]"
                  ></div>
                  <!-- 下半部分背景 -->
                  <div class="paper-bg-bottom absolute inset-0 top-[65%]"
                    :class="[
                      researcher1.most_cited_paper.citations > researcher2.most_cited_paper.citations
                        ? 'paper-bg-bottom-larger'
                        : 'paper-bg-bottom-smaller'
                    ]"
                  ></div>
                  <!-- 内容部分 -->
                  <div class="relative h-full flex flex-col">
                    <!-- 上半部分：头像和期刊信息 + 标题 (65%) -->
                    <div class="h-[65%] flex flex-col">
                      <div class="flex items-center gap-4 pb-2 mb-2 border-b paper-divider"
                        :class="[
                          researcher1.most_cited_paper.citations > researcher2.most_cited_paper.citations
                            ? 'paper-divider-larger'
                            : 'paper-divider-smaller'
                        ]"
                      >
                        <img 
                          :src="researcher1.avatar" 
                          :class="[
                            'rounded-full',
                            researcher1.most_cited_paper.citations > researcher2.most_cited_paper.citations
                              ? 'w-[50px] h-[50px]'
                              : 'w-[40px] h-[40px]'
                          ]" 
                        />
                        <div>
                          <div
                            :class="[
                              researcher1.most_cited_paper.citations > researcher2.most_cited_paper.citations
                                ? 'font-poppins text-[16px] font-bold paper-venue-larger'
                                : 'font-poppins text-[14px] font-bold paper-venue-smaller'
                            ]"
                          >
                            {{ researcher1.most_cited_paper.venue }}
                          </div>
                        </div>
                      </div>
                      <!-- 标题部分，在分割线下方垂直居中 -->
                      <div class="flex-1 flex items-center">
                        <div
                          :class="[
                            researcher1.most_cited_paper.citations > researcher2.most_cited_paper.citations
                              ? 'font-poppins text-[18px] font-bold paper-title-larger'
                              : 'font-poppins text-[14px] font-bold paper-title-smaller'
                          ]"
                          :style="researcher1.most_cited_paper.citations > researcher2.most_cited_paper.citations 
                            ? 'line-height: 24px;' 
                            : ''"
                        >
                          {{ researcher1.most_cited_paper.title }}
                        </div>
                      </div>
                    </div>
                    <!-- 下半部分：论文评价 (35%) -->
                    <div class="h-[35%] flex items-center">
                      <div
                        :class="[
                          researcher1.most_cited_paper.citations > researcher2.most_cited_paper.citations
                            ? 'text-[14px] font-poppins font-normal leading-[20px] paper-evaluation-larger line-clamp-3'
                            : 'text-[12px] font-poppins font-normal leading-[18px] paper-evaluation-smaller line-clamp-3'
                        ]"
                      >
                        {{ formatPaperEvaluation(researcher1.paper_evaluation) }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            :class="[
              'text-2xl w-[600px] leading-[48px] p-4 paper-card-right',
              researcher2.most_cited_paper.citations > researcher1.most_cited_paper.citations
                ? 'text-primary-100 font-bold h-[322px] paper-card-larger'
                : 'text-[#797979] dark:text-white h-[290px] paper-card-smaller',
            ]"
          >
            <div class="h-full flex flex-col">
              <div class="flex items-center justify-center h-[60px]">
                <span
                  :class="[
                    researcher2.most_cited_paper.citations > researcher1.most_cited_paper.citations
                      ? 'text-[#CB7C5D] text-[26px]'
                      : 'text-black dark:text-white text-[20px]',
                    'font-semibold'
                  ]"
                >
                  Citations: {{ formatNumber(researcher2.most_cited_paper.citations) }}
                </span>
              </div>
              <div class="flex-1">
                <div
                  class="h-full relative overflow-hidden"
                  :class="[
                    'rounded-lg border-l-4 p-3',
                    researcher2.most_cited_paper.citations > researcher1.most_cited_paper.citations
                      ? 'paper-border-larger'
                      : 'paper-border-smaller'
                  ]"
                >
                  <!-- 上半部分背景 -->
                  <div class="paper-bg-top absolute inset-0 h-[65%]"
                    :class="[
                      researcher2.most_cited_paper.citations > researcher1.most_cited_paper.citations
                        ? 'paper-bg-top-larger'
                        : 'paper-bg-top-smaller'
                    ]"
                  ></div>
                  <!-- 下半部分背景 -->
                  <div class="paper-bg-bottom absolute inset-0 top-[65%]"
                    :class="[
                      researcher2.most_cited_paper.citations > researcher1.most_cited_paper.citations
                        ? 'paper-bg-bottom-larger'
                        : 'paper-bg-bottom-smaller'
                    ]"
                  ></div>
                  <!-- 内容部分 -->
                  <div class="relative h-full flex flex-col">
                    <!-- 上半部分：头像和期刊信息 + 标题 (65%) -->
                    <div class="h-[65%] flex flex-col">
                      <div class="flex items-center gap-4 pb-2 mb-2 border-b paper-divider"
                        :class="[
                          researcher2.most_cited_paper.citations > researcher1.most_cited_paper.citations
                            ? 'paper-divider-larger'
                            : 'paper-divider-smaller'
                        ]"
                      >
                        <img 
                          :src="researcher2.avatar" 
                          :class="[
                            'rounded-full',
                            researcher2.most_cited_paper.citations > researcher1.most_cited_paper.citations
                              ? 'w-[50px] h-[50px]'
                              : 'w-[40px] h-[40px]'
                          ]" 
                        />
                        <div>
                          <div
                            :class="[
                              researcher2.most_cited_paper.citations > researcher1.most_cited_paper.citations
                                ? 'font-poppins text-[16px] font-bold paper-venue-larger'
                                : 'font-poppins text-[14px] font-bold paper-venue-smaller'
                            ]"
                          >
                            {{ researcher2.most_cited_paper.venue }}
                          </div>
                        </div>
                      </div>
                      <!-- 标题部分，在分割线下方垂直居中 -->
                      <div class="flex-1 flex items-center">
                        <div
                          :class="[
                            researcher2.most_cited_paper.citations > researcher1.most_cited_paper.citations
                              ? 'font-poppins text-[18px] font-bold paper-title-larger'
                              : 'font-poppins text-[14px] font-bold paper-title-smaller'
                          ]"
                          :style="researcher2.most_cited_paper.citations > researcher1.most_cited_paper.citations 
                            ? 'line-height: 24px;' 
                            : ''"
                        >
                          {{ researcher2.most_cited_paper.title }}
                        </div>
                      </div>
                    </div>
                    <!-- 下半部分：论文评价 (35%) -->
                    <div class="h-[35%] flex items-center">
                      <div
                        :class="[
                          researcher2.most_cited_paper.citations > researcher1.most_cited_paper.citations
                            ? 'text-[14px] font-poppins font-normal leading-[20px] paper-evaluation-larger line-clamp-3'
                            : 'text-[12px] font-poppins font-normal leading-[18px] paper-evaluation-smaller line-clamp-3'
                        ]"
                      >
                        {{ formatPaperEvaluation(researcher2.paper_evaluation) }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 翻书阴影层 -->
    <div
      class="absolute left-0 right-0 bottom-[-20px] flex flex-col items-center pointer-events-none z-0"
    >
      <!-- 第一层阴影（最宽、最深） -->
      <div
        class="w-[98%] h-[8px] rounded-b-[16px] border-b-1 border-[#D1D5DB] dark:border-[#27282D]/60 bg-gradient-to-b from-[#9CA3AF]/60 to-[#D1D5DB]/35 dark:bg-gradient-to-b dark:from-black/20 dark:to-[#999]/20"
      ></div>
      <!-- 第二层阴影 -->
      <div
        class="w-[96%] h-[8px] mt-[-2px] rounded-b-[14px] border-b-1 border-[#D1D5DB]/80 dark:border-[#27282D]/40 bg-gradient-to-b from-[#9CA3AF]/45 to-[#D1D5DB]/20 dark:bg-gradient-to-b dark:from-black/15 dark:to-[#999]/10"
      ></div>
      <!-- 第三层阴影 -->
      <div
        class="w-[94%] h-[8px] mt-[-2px] rounded-b-[12px] border-b-1 border-[#D1D5DB]/60 dark:border-[#27282D]/20 bg-gradient-to-b from-[#9CA3AF]/30 to-transparent dark:bg-gradient-to-b dark:from-black/10 dark:to-transparent"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
  interface MostCitedPaper {
    title: string
    venue: string
    year: number
    citations: number
    paper_evaluation?: string
  }

  interface Researcher {
    name: string
    avatar: string
    total_citations: number
    top_tier_papers: number
    first_author_papers: number
    first_author_citations: number
    most_cited_paper: MostCitedPaper
    paper_evaluation?: string
  }

  defineProps<{
    researcher1: Researcher
    researcher2: Researcher
  }>()

  // 添加数字格式化函数
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    }
    return num.toLocaleString()
  }

  // 处理论文评价，去除转义符号
  const formatPaperEvaluation = (evaluation?: string) => {
    if (!evaluation) return 'No evaluation available'

    // 去除转义符号
    return evaluation.replace(/\\"/g, '"').replace(/\\n/g, ' ').trim()
  }
</script>

<style scoped>
  .compare-metrics-container {
    border-color: #d1d5db;
    background-color: rgba(255, 255, 255, 0.8);
  }

  .dark .compare-metrics-container {
    background-color: #141415;
    border-color: #27282D;
    border-width: 1px;
  }

  /* Paper card styles */
  .paper-card-left {
    border-radius: 0.5rem 0 0 0.5rem;
  }

  .paper-card-right {
    border-radius: 0 0.5rem 0.5rem 0;
  }

  .paper-card-larger {
    background-color: #FFFFFF;
    border: 1px solid #CB7C5D;
  }

  .paper-card-smaller {
    background-color: #F1F1F3;
    border: 1px solid #e2e8f0;
  }

  .dark .paper-card-larger {
    background-color: #141415;
    border: 1px solid #939393;
  }

  .dark .paper-card-smaller {
    background-color: #181818;
    border: 1px solid #27272A;
  }

  .bg-primary-100 {
    background-color: #c69279;
  }
  .text-primary-100 {
    color: #c69279;
  }
  .text-gray-500 {
    color: #797979;
  }
  .bg-gray-100 {
    background-color: #f1f1f3;
  }

  /* Add Poppins font styling for numbers */
  .rounded-lg span {
    font-family: 'Poppins', sans-serif;
  }

  /* Metrics card styles */
  .metrics-card-larger {
    background-color: transparent;
    border: 1px solid #CB7C5D;
    border-radius: 0.5rem;
  }

  .metrics-card-smaller {
    background-color: #F1F1F3;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
  }

  .dark .metrics-card-larger {
    border: 1px solid #939393;
  }

  .dark .metrics-card-smaller {
    background-color: #181818;
    border: 1px solid #27272A;
  }

  /* Metric button styles */
  .metric-button {
    background: #CB7C5D;
    border: 1px solid transparent;
  }

  .dark .metric-button {
    background: var(--Color-2-, #654D43);
    border: 1px solid #866457;
  }

  /* Representative Papers background styles */
  .paper-bg-top.paper-bg-top-larger {
    background-color: #FAF2EF !important;
  }

  .paper-bg-bottom.paper-bg-bottom-larger {
    background: linear-gradient(90deg, #FAF2EF 0%, #FAEFEA 100%) !important;
  }

  .paper-bg-top.paper-bg-top-smaller {
    background-color: #F7F7F7 !important;
  }

  .paper-bg-bottom.paper-bg-bottom-smaller {
    background: linear-gradient(90deg, #F7F7F7 0%, #EDEDED 100%) !important;
  }

  /* Dark mode backgrounds */
  .dark .paper-bg-top.paper-bg-top-larger {
    background-color: #292929 !important;
  }

  .dark .paper-bg-bottom.paper-bg-bottom-larger {
    background: linear-gradient(90deg, #2E2E2E 0%, rgba(46, 46, 46, 0) 100%) !important;
  }

  .dark .paper-bg-top.paper-bg-top-smaller {
    background-color: #101010 !important;
  }

  .dark .paper-bg-bottom.paper-bg-bottom-smaller {
    background: #141414 !important;
  }

  /* Paper content styles */
  .paper-venue-larger {
    color: #000000;
  }

  .paper-venue-smaller {
    color: #000000;
  }

  .paper-title-larger {
    color: #000000;
  }

  .paper-title-smaller {
    color: #686868;
  }

  .paper-evaluation-larger {
    color: #4D4846;
  }

  .paper-evaluation-smaller {
    color: #989898;
  }

  .paper-divider-larger {
    border-color: #DFE1F2;
  }

  .paper-divider-smaller {
    border-color: #DFE1F2;
  }

  /* Dark mode content styles */
  .dark .paper-venue-larger {
    color: #FAF9F5;
  }

  .dark .paper-venue-smaller {
    color: #7A7A7A;
  }

  .dark .paper-title-larger {
    color: #C6C6C6;
  }

  .dark .paper-title-smaller {
    color: #777777;
  }

  .dark .paper-evaluation-larger {
    color: #C6C6C6;
  }

  .dark .paper-evaluation-smaller {
    color: #5D5D5D;
  }

  .dark .paper-divider-larger {
    border-color: rgba(250, 249, 245, 0.1);
  }

  .dark .paper-divider-smaller {
    border-color: rgba(255, 255, 255, 0.1);
  }

  /* Paper left border styles */
  .paper-border-larger {
    border-left-color: #CB7C5D;
  }

  .paper-border-smaller {
    border-left-color: #C3C3C3;
  }

  .dark .paper-border-larger {
    border-left-color: #654D43;
  }

  .dark .paper-border-smaller {
    border-left-color: #373737;
  }

  /* Representative Papers container */
  .representative-papers-container {
    background-color: #ffffff;
  }

  .dark .representative-papers-container {
    background-color: transparent;
  }
</style>
