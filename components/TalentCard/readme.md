<!-- 父组件使用 -->
<TalentCard
  v-model:show="showPopup"  //  控制弹窗显示
  :users="nearbyUsers"  //  传入用户数据
  @close="showPopup = false" //  关闭弹窗
  @handleQuery="handleQuery" //  点击头像时触发
/>

<!--
  示例数据
  第一个为中间人员头像，
  如果需要点击四周头像时，周围头像重新传入，则传递一个请求方法进去，
  点击周围头像时调用传递的请求，请求新的数据后重新通过users传递进组件，
  新的数据第一个必须为刚才点击的用户的信息才能确保用户在中心
-->

const nearbyUsers = [
  <!-- 中心的用户 -->
  {
    name: 'Yue <PERSON>',
    reason:
    'Direct coauthor on multiple papers in 2024 and 2025, including work on Multimodal Large Language Model Evaluation and Trustworthiness of Generative Foundation Models.',
    position_or_work:
    'Coauthor on UPME: An Unsupervised Peer Review Framework for Multimodal Large Language Model Evaluation, Breaking Focus: Contextual Distraction Curse in Large Language Models, On the Trustworthiness of Generative Foundation Models: Guideline, Assessment, and Perspective, Justice or Prejudice? Quantifying Biases in LLM-as-a-Judge, and AutoBench-V: Can Large Vision-Language Models Benchmark Themselves?',
    openreview_id: '~Yue_Huang9',
    scholar_id: 'HvzvvqQAAAAJ',
    avatar_url:
    'https://scholar.googleusercontent.com/citations?view_op=medium_photo&user=HvzvvqQAAAAJ',
  },
  <!-- 四周的用户 -->
  {
    name: 'Yue Huang',
    reason:
    'Direct coauthor on multiple papers in 2024 and 2025, including work on Multimodal Large Language Model Evaluation and Trustworthiness of Generative Foundation Models.',
    position_or_work:
    'Coauthor on UPME: An Unsupervised Peer Review Framework for Multimodal Large Language Model Evaluation, Breaking Focus: Contextual Distraction Curse in Large Language Models, On the Trustworthiness of Generative Foundation Models: Guideline, Assessment, and Perspective, Justice or Prejudice? Quantifying Biases in LLM-as-a-Judge, and AutoBench-V: Can Large Vision-Language Models Benchmark Themselves?',
    openreview_id: '~Yue_Huang9',
    scholar_id: 'Hvzvvq',
    avatar_url:
    'https://scholar.googleusercontent.com/citations?view_op=medium_photo&user=HvzvvqQAAAAJ',
  },
  {
    name: 'Yue Huang',
    reason:
    'Direct coauthor on multiple papers in 2024 and 2025, including work on Multimodal Large Language Model Evaluation and Trustworthiness of Generative Foundation Models.',
    position_or_work:
    'Coauthor on UPME: An Unsupervised Peer Review Framework for Multimodal Large Language Model Evaluation, Breaking Focus: Contextual Distraction Curse in Large Language Models, On the Trustworthiness of Generative Foundation Models: Guideline, Assessment, and Perspective, Justice or Prejudice? Quantifying Biases in LLM-as-a-Judge, and AutoBench-V: Can Large Vision-Language Models Benchmark Themselves?',
    openreview_id: '~Yue_Huang9',
    scholar_id: 'HvzvvqQAA',
    avatar_url:
    'https://scholar.googleusercontent.com/citations?view_op=medium_photo&user=HvzvvqQAAAAJ',
  },
  {
    name: 'Yue Huang',
    reason:
    'Direct coauthor on multiple papers in 2024 and 2025, including work on Multimodal Large Language Model Evaluation and Trustworthiness of Generative Foundation Models.',
    position_or_work:
    'Coauthor on UPME: An Unsupervised Peer Review Framework for Multimodal Large Language Model Evaluation, Breaking Focus: Contextual Distraction Curse in Large Language Models, On the Trustworthiness of Generative Foundation Models: Guideline, Assessment, and Perspective, Justice or Prejudice? Quantifying Biases in LLM-as-a-Judge, and AutoBench-V: Can Large Vision-Language Models Benchmark Themselves?',
    openreview_id: '~Yue_Huang9',
    scholar_id: 'HvzvAAAJ',
    avatar_url:
    'https://scholar.googleusercontent.com/citations?view_op=medium_photo&user=HvzvvqQAAAAJ',
  },
  {
    name: 'Yue Huang',
    reason:
    'Direct coauthor on multiple papers in 2024 and 2025, including work on Multimodal Large Language Model Evaluation and Trustworthiness of Generative Foundation Models.',
    position_or_work:
    'Coauthor on UPME: An Unsupervised Peer Review Framework for Multimodal Large Language Model Evaluation, Breaking Focus: Contextual Distraction Curse in Large Language Models, On the Trustworthiness of Generative Foundation Models: Guideline, Assessment, and Perspective, Justice or Prejudice? Quantifying Biases in LLM-as-a-Judge, and AutoBench-V: Can Large Vision-Language Models Benchmark Themselves?',
    openreview_id: '~Yue_Huang9',
    scholar_id: 'zvvQAAAAJ',
    avatar_url:
    'https://scholar.googleusercontent.com/citations?view_op=medium_photo&user=HvzvvqQAAAAJ',
  },
]
