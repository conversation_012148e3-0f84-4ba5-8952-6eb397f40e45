<template>
  <div
    class="compare-metrics-container relative shadow-md mx-auto space-y-8 p-3 border-2 rounded-lg"
  >
    <div>
      <!-- Code Contribution -->
      <div class="rounded-lg h-12 flex items-center justify-center mb-6">
        <!-- l -->
        <div
          class="rounded-lg flex justify-end items-center"
          :class="[
            'w-[600px] leading-[36px]',
            getMetricClass(user1.code_contribution?.total, user2.code_contribution?.total, true, 'count')
          ]"
          :style="getMetricStyle(user1.code_contribution?.total, user2.code_contribution?.total, true, 'count')"
        >
          <span class="mr-30" :class="{ 'opacity-60': isNAValue(user1.code_contribution?.total, 'count') }">
            {{ formatNumber(user1.code_contribution?.total, 'count') }}
          </span>
        </div>
        <!-- a -->
        <div class="absolute w-[200px] flex justify-center item-center">
          <div
            class="h-15 w-[134px] px-4 text-sm rounded-full text-white flex items-center justify-center text-center metric-button"
          >
            Code Contribution
          </div>
        </div>
        <!-- r -->
        <div
          class="rounded-lg flex items-center"
          :class="[
            'w-[600px] leading-[36px]',
            getMetricClass(user2.code_contribution?.total, user1.code_contribution?.total, false, 'count')
          ]"
          :style="getMetricStyle(user2.code_contribution?.total, user1.code_contribution?.total, false, 'count')"
        >
          <span class="ml-30" :class="{ 'opacity-60': isNAValue(user2.code_contribution?.total, 'count') }">
            {{ formatNumber(user2.code_contribution?.total, 'count') }}
          </span>
        </div>
      </div>
      
      <!-- Active Days -->
      <div class="rounded-lg h-12 flex items-center justify-center mb-6">
        <!-- l -->
        <div
          class="rounded-lg flex justify-end items-center"
          :class="[
            'w-[600px] leading-[36px]',
            getMetricClass(user1.overview?.active_days, user2.overview?.active_days, true, 'count')
          ]"
          :style="getMetricStyle(user1.overview?.active_days, user2.overview?.active_days, true, 'count')"
        >
          <span class="mr-30" :class="{ 'opacity-60': isNAValue(user1.overview?.active_days, 'count') }">
            {{ formatNumber(user1.overview?.active_days, 'count') }}
          </span>
        </div>
        <div class="absolute w-[200px] flex justify-center item-center">
          <div
            class="h-15 w-[134px] px-4 text-sm rounded-full text-white flex items-center justify-center text-center metric-button"
          >
            Active Days
          </div>
        </div>
        <!-- r -->
        <div
          class="rounded-lg flex items-center"
          :class="[
            'w-[600px] leading-[36px]',
            getMetricClass(user2.overview?.active_days, user1.overview?.active_days, false, 'count')
          ]"
          :style="getMetricStyle(user2.overview?.active_days, user1.overview?.active_days, false, 'count')"
        >
          <span class="ml-30" :class="{ 'opacity-60': isNAValue(user2.overview?.active_days, 'count') }">
            {{ formatNumber(user2.overview?.active_days, 'count') }}
          </span>
        </div>
      </div>

      <!-- Pull Requests -->
      <div class="rounded-lg h-12 flex items-center justify-center mb-6">
        <!-- l -->
        <div
          class="rounded-lg flex justify-end items-center"
          :class="[
            'w-[600px] leading-[36px]',
            getMetricClass(user1.overview?.pull_requests, user2.overview?.pull_requests, true, 'count')
          ]"
          :style="getMetricStyle(user1.overview?.pull_requests, user2.overview?.pull_requests, true, 'count')"
        >
          <span class="mr-30" :class="{ 'opacity-60': isNAValue(user1.overview?.pull_requests, 'count') }">
            {{ formatNumber(user1.overview?.pull_requests, 'count') }}
          </span>
        </div>
        <div class="absolute w-[200px] flex justify-center item-center">
          <div
            class="h-15 w-[134px] px-4 text-sm rounded-full text-white flex items-center justify-center text-center metric-button"
          >
            Pull Requests
          </div>
        </div>
        <!-- r -->
        <div
          class="rounded-lg flex items-center"
          :class="[
            'w-[600px] leading-[36px]',
            getMetricClass(user2.overview?.pull_requests, user1.overview?.pull_requests, false, 'count')
          ]"
          :style="getMetricStyle(user2.overview?.pull_requests, user1.overview?.pull_requests, false, 'count')"
        >
          <span class="ml-30" :class="{ 'opacity-60': isNAValue(user2.overview?.pull_requests, 'count') }">
            {{ formatNumber(user2.overview?.pull_requests, 'count') }}
          </span>
        </div>
      </div>

      <!-- Top Contributions -->
      <div class="rounded-lg h-12 flex items-center justify-center mb-6">
        <!-- l -->
        <div
          class="rounded-lg flex justify-end items-center"
          :class="[
            'w-[600px] leading-[36px]',
            getTopProjectStars(user1) > getTopProjectStars(user2)
              ? 'text-[#CB7C5D] font-bold h-[60px] metrics-card-larger'
              : 'text-[#797979] h-[50px] metrics-card-smaller',
          ]"
          :style="getTopProjectStars(user1) > getTopProjectStars(user2) 
            ? 'font-family: \'Poppins\', sans-serif; font-weight: 600; font-size: 36px; line-height: 36px; letter-spacing: 0%;' 
            : 'font-family: \'Poppins\', sans-serif; font-weight: 500; font-size: 28px; line-height: 36px; letter-spacing: 0%;'"
        >
          <span class="mr-30">
            {{ getTopProjectName(user1) }}
          </span>
        </div>
        <div class="absolute w-[200px] flex justify-center item-center">
          <div
            class="h-15 w-[134px] px-4 text-sm rounded-full text-white flex items-center justify-center text-center metric-button"
          >
            Top Contributions
          </div>
        </div>
        <!-- r -->
        <div
          class="rounded-lg flex items-center"
          :class="[
            'w-[600px] leading-[36px]',
            getTopProjectStars(user2) > getTopProjectStars(user1)
              ? 'text-[#CB7C5D] font-bold h-[60px] metrics-card-larger'
              : 'text-[#797979] h-[50px] metrics-card-smaller',
          ]"
          :style="getTopProjectStars(user2) > getTopProjectStars(user1) 
            ? 'font-family: \'Poppins\', sans-serif; font-weight: 600; font-size: 36px; line-height: 36px; letter-spacing: 0%;' 
            : 'font-family: \'Poppins\', sans-serif; font-weight: 500; font-size: 28px; line-height: 36px; letter-spacing: 0%;'"
        >
          <span class="ml-30">
            {{ getTopProjectName(user2) }}
          </span>
        </div>
      </div>

      <!-- Featured Projects -->
      <div class="representative-papers-container rounded-lg relative">
        <div class="absolute left-1/2 transform -translate-x-1/2" style="top: 30px;">
          <div
            class="h-15 w-[134px] px-4 text-sm rounded-full text-white flex items-center justify-center text-center translate-y-[-50%] metric-button"
          >
            Featured Projects
          </div>
        </div>
        <div class="flex items-center justify-center">
          <div
            :class="[
              'text-2xl w-[600px] leading-[48px] p-4 paper-card-left',
              (user1.feature_project?.stargazerCount || 0) > (user2.feature_project?.stargazerCount || 0)
                ? 'text-primary-100 font-bold h-[322px] paper-card-larger'
                : 'text-[#797979] dark:text-white h-[290px] paper-card-smaller',
            ]"
          >
            <div class="h-full flex flex-col">
              <div class="flex items-center justify-center h-[60px]">
                <span
                  :class="[
                    (user1.feature_project?.stargazerCount || 0) > (user2.feature_project?.stargazerCount || 0)
                      ? 'text-[#CB7C5D] text-[26px]'
                      : 'text-black dark:text-white text-[20px]',
                    'font-semibold'
                  ]"
                >
                  Stars: {{ formatNumber(user1.feature_project?.stargazerCount, 'count') }}
                </span>
              </div>
              <div class="flex-1">
                <div
                  class="h-full relative overflow-hidden"
                  :class="[
                    'rounded-lg border-l-4 p-3',
                    (user1.feature_project?.stargazerCount || 0) > (user2.feature_project?.stargazerCount || 0)
                      ? 'paper-border-larger'
                      : 'paper-border-smaller'
                  ]"
                >
                  <!-- 上半部分背景 -->
                  <div class="paper-bg-top absolute inset-0 h-[65%]"
                    :class="[
                      (user1.feature_project?.stargazerCount || 0) > (user2.feature_project?.stargazerCount || 0)
                        ? 'paper-bg-top-larger'
                        : 'paper-bg-top-smaller'
                    ]"
                  ></div>
                  <!-- 下半部分背景 -->
                  <div class="paper-bg-bottom absolute inset-0 top-[65%]"
                    :class="[
                      (user1.feature_project?.stargazerCount || 0) > (user2.feature_project?.stargazerCount || 0)
                        ? 'paper-bg-bottom-larger'
                        : 'paper-bg-bottom-smaller'
                    ]"
                  ></div>
                  <!-- 内容部分 -->
                  <div class="relative h-full flex flex-col">
                    <!-- 上半部分：头像和项目信息 + 标题 (65%) -->
                    <div class="h-[65%] flex flex-col">
                      <div class="flex items-center gap-4 pb-2 mb-2 border-b paper-divider"
                        :class="[
                          (user1.feature_project?.stargazerCount || 0) > (user2.feature_project?.stargazerCount || 0)
                            ? 'paper-divider-larger'
                            : 'paper-divider-smaller'
                        ]"
                      >
                        <img 
                          :src="user1.feature_project?.owner?.avatarUrl || user1.user?.avatar_url || '/image/default-avatar.png'" 
                          :class="[
                            'rounded-full',
                            (user1.feature_project?.stargazerCount || 0) > (user2.feature_project?.stargazerCount || 0)
                              ? 'w-[50px] h-[50px]'
                              : 'w-[40px] h-[40px]'
                          ]"
                        />
                        <div class="flex-1 min-w-0">
                          <div 
                            :class="[
                              'font-semibold truncate',
                              (user1.feature_project?.stargazerCount || 0) > (user2.feature_project?.stargazerCount || 0)
                                ? 'text-[16px] paper-venue-larger'
                                : 'text-[14px] paper-venue-smaller'
                            ]"
                          >
                            {{ user1.feature_project?.name || 'Featured Project' }}
                          </div>
                          <div 
                            :class="[
                              'text-xs opacity-80 truncate',
                              (user1.feature_project?.stargazerCount || 0) > (user2.feature_project?.stargazerCount || 0)
                                ? 'paper-venue-larger'
                                : 'paper-venue-smaller'
                            ]"
                          >
                            {{ user1.name }}
                          </div>
                        </div>
                      </div>
                      <div class="flex-1 overflow-hidden">
                        <div 
                          :class="[
                            'font-bold line-clamp-3',
                            (user1.feature_project?.stargazerCount || 0) > (user2.feature_project?.stargazerCount || 0)
                              ? 'text-[18px] leading-[24px] paper-title-larger'
                              : 'text-[16px] leading-[22px] paper-title-smaller'
                          ]"
                        >
                          {{ user1.feature_project?.name || 'Featured Project' }}
                        </div>
                      </div>
                    </div>
                    <!-- 下半部分：项目描述 (35%) -->
                    <div class="h-[35%] overflow-hidden">
                      <div 
                        :class="[
                          'text-sm leading-relaxed line-clamp-4',
                          (user1.feature_project?.stargazerCount || 0) > (user2.feature_project?.stargazerCount || 0)
                            ? 'paper-evaluation-larger'
                            : 'paper-evaluation-smaller'
                        ]"
                      >
                        {{ user1.feature_project?.description || 'No description available.' }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            :class="[
              'text-2xl w-[600px] leading-[48px] p-4 paper-card-right',
              (user2.feature_project?.stargazerCount || 0) > (user1.feature_project?.stargazerCount || 0)
                ? 'text-primary-100 font-bold h-[322px] paper-card-larger'
                : 'text-[#797979] dark:text-white h-[290px] paper-card-smaller',
            ]"
          >
            <div class="h-full flex flex-col">
              <div class="flex items-center justify-center h-[60px]">
                <span
                  :class="[
                    (user2.feature_project?.stargazerCount || 0) > (user1.feature_project?.stargazerCount || 0)
                      ? 'text-[#CB7C5D] text-[26px]'
                      : 'text-black dark:text-white text-[20px]',
                    'font-semibold'
                  ]"
                >
                  Stars: {{ formatNumber(user2.feature_project?.stargazerCount, 'count') }}
                </span>
              </div>
              <div class="flex-1">
                <div
                  class="h-full relative overflow-hidden"
                  :class="[
                    'rounded-lg border-l-4 p-3',
                    (user2.feature_project?.stargazerCount || 0) > (user1.feature_project?.stargazerCount || 0)
                      ? 'paper-border-larger'
                      : 'paper-border-smaller'
                  ]"
                >
                  <!-- 上半部分背景 -->
                  <div class="paper-bg-top absolute inset-0 h-[65%]"
                    :class="[
                      (user2.feature_project?.stargazerCount || 0) > (user1.feature_project?.stargazerCount || 0)
                        ? 'paper-bg-top-larger'
                        : 'paper-bg-top-smaller'
                    ]"
                  ></div>
                  <!-- 下半部分背景 -->
                  <div class="paper-bg-bottom absolute inset-0 top-[65%]"
                    :class="[
                      (user2.feature_project?.stargazerCount || 0) > (user1.feature_project?.stargazerCount || 0)
                        ? 'paper-bg-bottom-larger'
                        : 'paper-bg-bottom-smaller'
                    ]"
                  ></div>
                  <!-- 内容部分 -->
                  <div class="relative h-full flex flex-col">
                    <!-- 上半部分：头像和项目信息 + 标题 (65%) -->
                    <div class="h-[65%] flex flex-col">
                      <div class="flex items-center gap-4 pb-2 mb-2 border-b paper-divider"
                        :class="[
                          (user2.feature_project?.stargazerCount || 0) > (user1.feature_project?.stargazerCount || 0)
                            ? 'paper-divider-larger'
                            : 'paper-divider-smaller'
                        ]"
                      >
                        <img
                          :src="user2.feature_project?.owner?.avatarUrl || user2.user?.avatar_url || '/image/default-avatar.png'"
                          :class="[
                            'rounded-full',
                            (user2.feature_project?.stargazerCount || 0) > (user1.feature_project?.stargazerCount || 0)
                              ? 'w-[50px] h-[50px]'
                              : 'w-[40px] h-[40px]'
                          ]"
                        />
                        <div class="flex-1 min-w-0">
                          <div
                            :class="[
                              'font-semibold truncate',
                              (user2.feature_project?.stargazerCount || 0) > (user1.feature_project?.stargazerCount || 0)
                                ? 'text-[16px] paper-venue-larger'
                                : 'text-[14px] paper-venue-smaller'
                            ]"
                          >
                            {{ user2.feature_project?.name || 'Featured Project' }}
                          </div>
                          <div
                            :class="[
                              'text-xs opacity-80 truncate',
                              (user2.feature_project?.stargazerCount || 0) > (user1.feature_project?.stargazerCount || 0)
                                ? 'paper-venue-larger'
                                : 'paper-venue-smaller'
                            ]"
                          >
                            {{ user2.name }}
                          </div>
                        </div>
                      </div>
                      <div class="flex-1 overflow-hidden">
                        <div
                          :class="[
                            'font-bold line-clamp-3',
                            (user2.feature_project?.stargazerCount || 0) > (user1.feature_project?.stargazerCount || 0)
                              ? 'text-[18px] leading-[24px] paper-title-larger'
                              : 'text-[16px] leading-[22px] paper-title-smaller'
                          ]"
                        >
                          {{ user2.feature_project?.name || 'Featured Project' }}
                        </div>
                      </div>
                    </div>
                    <!-- 下半部分：项目描述 (35%) -->
                    <div class="h-[35%] overflow-hidden">
                      <div
                        :class="[
                          'text-sm leading-relaxed line-clamp-4',
                          (user2.feature_project?.stargazerCount || 0) > (user1.feature_project?.stargazerCount || 0)
                            ? 'paper-evaluation-larger'
                            : 'paper-evaluation-smaller'
                        ]"
                      >
                        {{ user2.feature_project?.description || 'No description available.' }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  interface GitHubUser {
    name: string
    user: {
      avatar_url: string
      bio?: string
      tags?: string[]
    }
    code_contribution?: {
      total: number | string | null
    }
    overview?: {
      active_days: number | string | null
      pull_requests: number | string | null
    }
    top_projects?: Array<{
      repository: {
        name: string
        stargazerCount?: number | string | null
      }
    }>
    feature_project?: {
      name: string
      description: string
      stargazerCount: number | string | null
      owner?: {
        avatarUrl: string
      }
    }
  }

  defineProps<{
    user1: GitHubUser
    user2: GitHubUser
  }>()

  // 改进的数字格式化函数 - 支持多种数据类型和N/A处理
  const formatNumber = (value: number | string | null | undefined, metricType: 'count' | 'general' = 'count') => {
    // 处理明确的N/A情况
    if (value === null || value === undefined) {
      return 'N/A'
    }

    // 处理字符串类型的N/A
    if (typeof value === 'string') {
      const normalizedValue = value.toString().toLowerCase().trim()
      if (normalizedValue === 'n/a' || normalizedValue === 'na' || normalizedValue === '') {
        return 'N/A'
      }
      // 尝试转换字符串为数字
      const numValue = parseFloat(value)
      if (isNaN(numValue)) {
        return 'N/A'
      }
      value = numValue
    }

    // 对于count类型的指标，0是有效值
    if (metricType === 'count' && value === 0) {
      return '0'
    }

    // 对于general类型的指标，0可能表示N/A（保持向后兼容）
    if (metricType === 'general' && value === 0) {
      return 'N/A'
    }

    // 格式化有效的数字
    if (value >= 1000000) {
      return (value / 1000000).toFixed(1) + 'M'
    }
    if (value >= 1000) {
      return (value / 1000).toFixed(1) + 'K'
    }
    return value.toLocaleString()
  }

  // 改进的N/A值检查函数
  const isNAValue = (value: number | string | null | undefined, metricType: 'count' | 'general' = 'count') => {
    // 明确的null/undefined情况
    if (value === null || value === undefined) {
      return true
    }

    // 字符串类型的N/A检查
    if (typeof value === 'string') {
      const normalizedValue = value.toString().toLowerCase().trim()
      if (normalizedValue === 'n/a' || normalizedValue === 'na' || normalizedValue === '') {
        return true
      }
      // 检查是否为无效数字字符串
      const numValue = parseFloat(value)
      if (isNaN(numValue)) {
        return true
      }
      value = numValue
    }

    // 对于count类型，0是有效值
    if (metricType === 'count') {
      return false // 0 对于计数指标是有效的
    }

    // 对于general类型，0可能表示N/A（向后兼容）
    return value === 0
  }

  // 获取指标比较的CSS类 - 支持不同指标类型
  const getMetricClass = (value1: number | string | null | undefined, value2: number | string | null | undefined, isLeft: boolean, metricType: 'count' | 'general' = 'count') => {
    // 获取数值进行比较
    const getNumericValue = (val: number | string | null | undefined) => {
      if (val === null || val === undefined) return 0
      if (typeof val === 'string') {
        const parsed = parseFloat(val)
        return isNaN(parsed) ? 0 : parsed
      }
      return val
    }

    const val1 = getNumericValue(value1)
    const val2 = getNumericValue(value2)

    // 如果两个值都是N/A，显示为较小样式
    if (isNAValue(value1, metricType) && isNAValue(value2, metricType)) {
      return 'text-[#797979] h-[50px] metrics-card-smaller'
    }

    // 如果当前值是N/A但对方有值，显示为较小样式
    if (isNAValue(value1, metricType) && !isNAValue(value2, metricType)) {
      return 'text-[#797979] h-[50px] metrics-card-smaller'
    }

    // 如果对方是N/A但当前有值，显示为较大样式
    if (!isNAValue(value1, metricType) && isNAValue(value2, metricType)) {
      return 'text-[#CB7C5D] font-bold h-[60px] metrics-card-larger'
    }

    // 正常比较
    if (val1 > val2) {
      return 'text-[#CB7C5D] font-bold h-[60px] metrics-card-larger'
    } else {
      return 'text-[#797979] h-[50px] metrics-card-smaller'
    }
  }

  // 获取指标比较的样式 - 支持不同指标类型
  const getMetricStyle = (value1: number | string | null | undefined, value2: number | string | null | undefined, isLeft: boolean, metricType: 'count' | 'general' = 'count') => {
    // 获取数值进行比较
    const getNumericValue = (val: number | string | null | undefined) => {
      if (val === null || val === undefined) return 0
      if (typeof val === 'string') {
        const parsed = parseFloat(val)
        return isNaN(parsed) ? 0 : parsed
      }
      return val
    }

    const val1 = getNumericValue(value1)
    const val2 = getNumericValue(value2)

    // 如果两个值都是N/A，使用较小样式
    if (isNAValue(value1, metricType) && isNAValue(value2, metricType)) {
      return 'font-family: \'Poppins\', sans-serif; font-weight: 500; font-size: 28px; line-height: 36px; letter-spacing: 0%;'
    }

    // 如果当前值是N/A但对方有值，使用较小样式
    if (isNAValue(value1, metricType) && !isNAValue(value2, metricType)) {
      return 'font-family: \'Poppins\', sans-serif; font-weight: 500; font-size: 28px; line-height: 36px; letter-spacing: 0%;'
    }

    // 如果对方是N/A但当前有值，使用较大样式
    if (!isNAValue(value1, metricType) && isNAValue(value2, metricType)) {
      return 'font-family: \'Poppins\', sans-serif; font-weight: 600; font-size: 36px; line-height: 36px; letter-spacing: 0%;'
    }

    // 正常比较
    if (val1 > val2) {
      return 'font-family: \'Poppins\', sans-serif; font-weight: 600; font-size: 36px; line-height: 36px; letter-spacing: 0%;'
    } else {
      return 'font-family: \'Poppins\', sans-serif; font-weight: 500; font-size: 28px; line-height: 36px; letter-spacing: 0%;'
    }
  }

  // 获取顶级项目名称
  const getTopProjectName = (user: GitHubUser) => {
    return user.top_projects?.[0]?.repository?.name || 'N/A'
  }

  // 获取顶级项目星数（用于比较）- 改进版本
  const getTopProjectStars = (user: GitHubUser) => {
    const stars = user.top_projects?.[0]?.repository?.stargazerCount
    return stars !== null && stars !== undefined ? stars : null
  }

  // 安全获取数值的辅助函数
  const getSafeNumericValue = (value: number | string | null | undefined, defaultValue: number = 0): number => {
    if (value === null || value === undefined) return defaultValue
    if (typeof value === 'string') {
      const parsed = parseFloat(value)
      return isNaN(parsed) ? defaultValue : parsed
    }
    return value
  }

  // 格式化项目描述，处理过长文本
  const formatProjectDescription = (description: string | null | undefined, maxLength: number = 100): string => {
    if (!description || description.trim() === '') return 'No description available'
    if (description.length <= maxLength) return description
    return description.substring(0, maxLength) + '...'
  }
</script>

<style scoped>
  .compare-metrics-container {
    border-color: #d1d5db;
    background-color: rgba(255, 255, 255, 0.8);
  }

  .dark .compare-metrics-container {
    background-color: #141415;
    border-color: #27282D;
    border-width: 1px;
  }

  /* Paper card styles */
  .paper-card-left {
    border-radius: 0.5rem 0 0 0.5rem;
  }

  .paper-card-right {
    border-radius: 0 0.5rem 0.5rem 0;
  }

  .paper-card-larger {
    background-color: #FFFFFF;
    border: 1px solid #CB7C5D;
  }

  .paper-card-smaller {
    background-color: #F1F1F3;
    border: 1px solid #e2e8f0;
  }

  .dark .paper-card-larger {
    background-color: #141415;
    border: 1px solid #939393;
  }

  .dark .paper-card-smaller {
    background-color: #181818;
    border: 1px solid #27272A;
  }

  .bg-primary-100 {
    background-color: #c69279;
  }
  .text-primary-100 {
    color: #c69279;
  }
  .text-gray-500 {
    color: #797979;
  }
  .bg-gray-100 {
    background-color: #f1f1f3;
  }

  /* Add Poppins font styling for numbers */
  .rounded-lg span {
    font-family: 'Poppins', sans-serif;
  }

  /* Metrics card styles */
  .metrics-card-larger {
    background-color: transparent;
    border: 1px solid #CB7C5D;
    border-radius: 0.5rem;
  }

  .metrics-card-smaller {
    background-color: #F1F1F3;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
  }

  .dark .metrics-card-larger {
    border: 1px solid #939393;
  }

  .dark .metrics-card-smaller {
    background-color: #181818;
    border: 1px solid #27272A;
  }

  /* Metric button styles */
  .metric-button {
    background: #CB7C5D;
    border: 1px solid transparent;
  }

  .dark .metric-button {
    background: var(--Color-2-, #654D43);
    border: 1px solid #866457;
  }

  /* Representative Papers background styles */
  .paper-bg-top.paper-bg-top-larger {
    background-color: #FAF2EF !important;
  }

  .paper-bg-bottom.paper-bg-bottom-larger {
    background: linear-gradient(90deg, #FAF2EF 0%, #FAEFEA 100%) !important;
  }

  .paper-bg-top.paper-bg-top-smaller {
    background-color: #F7F7F7 !important;
  }

  .paper-bg-bottom.paper-bg-bottom-smaller {
    background: linear-gradient(90deg, #F7F7F7 0%, #EDEDED 100%) !important;
  }

  /* Dark mode backgrounds */
  .dark .paper-bg-top.paper-bg-top-larger {
    background-color: #292929 !important;
  }

  .dark .paper-bg-bottom.paper-bg-bottom-larger {
    background: linear-gradient(90deg, #292929 0%, #2A2A2A 100%) !important;
  }

  .dark .paper-bg-top.paper-bg-top-smaller {
    background-color: #1F1F1F !important;
  }

  .dark .paper-bg-bottom.paper-bg-bottom-smaller {
    background: linear-gradient(90deg, #1F1F1F 0%, #1A1A1A 100%) !important;
  }

  /* Content styles */
  .paper-venue-larger {
    color: #CB7C5D;
  }

  .paper-venue-smaller {
    color: #797979;
  }

  .paper-title-larger {
    color: #333333;
  }

  .paper-title-smaller {
    color: #666666;
  }

  .paper-evaluation-larger {
    color: #555555;
  }

  .paper-evaluation-smaller {
    color: #777777;
  }

  .paper-divider-larger {
    border-color: rgba(203, 124, 93, 0.2);
  }

  .paper-divider-smaller {
    border-color: rgba(0, 0, 0, 0.1);
  }

  /* Dark mode content styles */
  .dark .paper-venue-larger {
    color: #FAF9F5;
  }

  .dark .paper-venue-smaller {
    color: #7A7A7A;
  }

  .dark .paper-title-larger {
    color: #C6C6C6;
  }

  .dark .paper-title-smaller {
    color: #777777;
  }

  .dark .paper-evaluation-larger {
    color: #C6C6C6;
  }

  .dark .paper-evaluation-smaller {
    color: #5D5D5D;
  }

  .dark .paper-divider-larger {
    border-color: rgba(250, 249, 245, 0.1);
  }

  .dark .paper-divider-smaller {
    border-color: rgba(255, 255, 255, 0.1);
  }

  /* Paper left border styles */
  .paper-border-larger {
    border-left-color: #CB7C5D;
  }

  .paper-border-smaller {
    border-left-color: #C3C3C3;
  }

  .dark .paper-border-larger {
    border-left-color: #654D43;
  }

  .dark .paper-border-smaller {
    border-left-color: #373737;
  }

  /* Line clamp utilities */
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-4 {
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
