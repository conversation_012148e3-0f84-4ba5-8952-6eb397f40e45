<template>
  <button
    :class="buttonClass"
    :disabled="isLoading"
    @click="handleShare"
    style="backdrop-filter: blur(34px);"
  >
    <div
      v-if="isLoading"
      class="i-svg-spinners:3-dots-fade w-4 h-4 pointer-events-none"
    ></div>
    <div v-else class="i-proicons:x-twitter w-4 h-4 pointer-events-none"></div>
    <span class="pointer-events-none">{{ buttonText }}</span>
  </button>
</template>

<script setup lang="ts">
  import html2canvas from 'html2canvas-pro'
  import { ref, computed } from 'vue'

  const props = defineProps<{
    cardId: string
    type?: 'profile' | 'default' | 'popup'
    isDark?: boolean
    variant?: 'transparent' | 'colored'
  }>()

  const emit = defineEmits<{
    'share-popup': []
  }>()

  const isLoading = ref(false)
  const isReadyToShare = ref(false)
  const imageBlob = ref<Blob | null>(null)

  // 计算按钮文字
  const buttonText = computed(() => {
    if (isLoading.value) {
      return 'Preparing'
    }
    if (isReadyToShare.value) {
      return 'Click to Paste & Share'
    }
    return 'Share'
  })

  // 计算按钮样式
  const buttonClass = computed(() => {
    const baseClass = 'fx-cer rounded-full py-2 px-4 gap-2 text-sm font-medium transition-all duration-200 select-none min-h-[40px] cursor-pointer disabled:opacity-70 disabled:cursor-not-allowed'
    
    if (props.variant === 'transparent') {
      if (props.isDark) {
        return `${baseClass} bg-[#14141580] border border-[#27282D] text-[#FAF9F5] shadow-sm hover:bg-[#27282D] disabled:hover:bg-[#14141580] ${isReadyToShare.value ? 'animate-pulse' : ''}`
      } else {
        return `${baseClass} bg-[#FFFFFF]/60 border border-gray-200 text-gray-700 hover:bg-[#F5F5F5] disabled:hover:bg-[#FFFFFF]/60 ${isReadyToShare.value ? 'animate-pulse' : ''}`
      }
    } else if (props.variant === 'colored') {
      // 使用与ProfileCard一致的颜色样式
      if (props.isDark) {
        return `${baseClass} bg-[#654D43] text-white hover:bg-[#654D43]/80 disabled:hover:bg-[#654D43] ${isReadyToShare.value ? 'animate-pulse' : ''}`
      } else {
        return `${baseClass} bg-[#CB7C5D] text-white hover:bg-[#CB7C5D]/80 disabled:hover:bg-[#CB7C5D] ${isReadyToShare.value ? 'animate-pulse' : ''}`
      }
    } else {
      return `${baseClass} bg-black text-white hover:bg-gray-800 disabled:hover:bg-black ${isReadyToShare.value ? 'animate-pulse' : ''}`
    }
  })

  const handleShare = async () => {
    if (isLoading.value) return

    // 如果已经准备好分享，直接跳转到X
    if (isReadyToShare.value) {
      const twitterComposeUrl = 'https://twitter.com/intent/tweet'
      
      // 从DOM中获取用户名称
      let userName = 'Trevor Darrell' // 默认名称
      let isGitHubCard = false
      let isCompareCard = false
      let userName2 = ''

      // 尝试从GitHub Compare ShareCard中获取用户名称
      const githubCompareCardElement = document.querySelector('[data-card-id="share-card-github-compare"]')
      if (githubCompareCardElement) {
        isGitHubCard = true
        isCompareCard = true
        const nameElements = githubCompareCardElement.querySelectorAll('.font-bold')
        if (nameElements.length >= 2) {
          userName = nameElements[0].textContent?.trim() || 'User 1'
          userName2 = nameElements[1].textContent?.trim() || 'User 2'
        }
      }

      // 尝试从Scholar Compare ShareCard中获取用户名称
      if (!isGitHubCard) {
        const scholarCompareCardElement = document.querySelector('[data-card-id="share-card-compare"]')
        if (scholarCompareCardElement) {
          isCompareCard = true
          const nameElements = scholarCompareCardElement.querySelectorAll('.font-bold')
          if (nameElements.length >= 2) {
            userName = nameElements[0].textContent?.trim() || 'Researcher 1'
            userName2 = nameElements[1].textContent?.trim() || 'Researcher 2'
          }
        }
      }

      // 尝试从GitHub ShareCard中获取用户名称
      if (!isCompareCard) {
        const githubShareCardElement = document.querySelector('[data-card-id="share-card-github"]')
        if (githubShareCardElement) {
          isGitHubCard = true
          const nameElement = githubShareCardElement.querySelector('h2')
          if (nameElement && nameElement.textContent) {
            userName = nameElement.textContent.trim()
          }
        }
      }

      // 尝试从Scholar ShareCard中获取用户名称
      if (!isGitHubCard && !isCompareCard) {
        const shareCardElement = document.querySelector('[data-card-id="share-card"]')
        if (shareCardElement) {
          const nameElement = shareCardElement.querySelector('h2')
          if (nameElement && nameElement.textContent) {
            userName = nameElement.textContent.trim()
          }
        }
      }

      // 如果ShareCard中没有找到，尝试从ProfileCard中获取
      if (userName === 'Trevor Darrell' && !isCompareCard) {
        const profileCardElement = document.querySelector('.f-cer.w-full')
        if (profileCardElement) {
          const nameElement = profileCardElement.querySelector('.font-bold.text-xl, .font-bold.text-2xl')
          if (nameElement && nameElement.textContent) {
            userName = nameElement.textContent.trim()
          }
        }
      }
      
      // 根据卡片类型选择不同的文案模板
      let shareTexts: string[]

      if (isCompareCard && isGitHubCard) {
        // GitHub Compare Card
        shareTexts = [
          `${userName} vs ${userName2} GitHub developer comparison is out, from DINQ.me. Full of insights, check it out! @dinq_io #DINQ #GitHub`,
          `GitHub developer comparison is here! ${userName} vs ${userName2}, from DINQ.me. Don't miss it! @dinq_io #DINQ #GitHub`,
          `${userName} vs ${userName2} GitHub developer evaluation, released by DINQ.me. Worth your attention! @dinq_io #DINQ #GitHub`
        ]
      } else if (isCompareCard) {
        // Scholar Compare Card
        shareTexts = [
          `${userName} vs ${userName2} AI researcher comparison is out, from DINQ.me. Full of insights, check it out! @dinq_io #DINQ`,
          `AI researcher comparison is here! ${userName} vs ${userName2}, from DINQ.me. Don't miss it! @dinq_io #DINQ`,
          `${userName} vs ${userName2} AI researcher evaluation, released by DINQ.me. Worth your attention! @dinq_io #DINQ`
        ]
      } else if (isGitHubCard) {
        // GitHub Individual Card
        shareTexts = [
          `${userName}'s GitHub developer analysis is out, from DINQ.me. Full of insights, check it out! @dinq_io #DINQ #GitHub`,
          `GitHub developer analysis is here! By ${userName}, from DINQ.me. Don't miss it! @dinq_io #DINQ #GitHub`,
          `${userName}'s GitHub developer evaluation, released by DINQ.me. Worth your attention! @dinq_io #DINQ #GitHub`
        ]
      } else {
        // Scholar Individual Card
        shareTexts = [
          `${userName}'s AI talent assessment is out, from DINQ.me. Full of insights, check it out! @dinq_io #DINQ`,
          `AI talent analysis is here! By ${userName}, from DINQ.me. Don't miss it! @dinq_io #DINQ`,
          `${userName}'s AI talent evaluation, released by DINQ.me. Worth your attention! @dinq_io #DINQ`
        ]
      }
      
      // 随机选择一个文案
      const randomText = shareTexts[Math.floor(Math.random() * shareTexts.length)]
      const shareText = encodeURIComponent(randomText)
      const shareUrl = `${twitterComposeUrl}?text=${shareText}`
      
      window.open(shareUrl, '_blank')
      
      // 重置状态
      isReadyToShare.value = false
      imageBlob.value = null
      return
    }

    // 如果type为popup，则触发分享弹窗事件
    if (props.type === 'popup') {
      emit('share-popup')
      return
    }

    isLoading.value = true
    try {
      let elementToCapture

      // 根据type参数决定要截图的元素
      if (props.type === 'profile') {
        // 当type为profile时，查找整个ProfileCard组件（包括ShareArea）
        elementToCapture = document.querySelector('.f-cer.w-full')
        if (!elementToCapture) {
          throw new Error('Profile card element not found')
        }
      } else {
        // 默认行为：使用cardId来精确定位要截图的DOM元素
        elementToCapture = document.querySelector(`[data-card-id="${props.cardId}"]`)
        if (!elementToCapture) {
          throw new Error('Target element not found')
        }
      }

      // 等待一小段时间确保所有元素都渲染完成
      await new Promise(resolve => setTimeout(resolve, 500))

      // 使用html2canvas-pro，它对现代CSS有更好的支持
      const canvas = await html2canvas(elementToCapture as HTMLElement, {
        backgroundColor: '#ffffff',
        scale: 2,
        useCORS: true,
        allowTaint: true,
        logging: false,
        imageTimeout: 15000,
        // html2canvas-pro有更好的SVG和CSS支持，所以简化配置
        foreignObjectRendering: false,
        // 确保元素完全在视图中
        scrollX: 0,
        scrollY: 0,
        // 在截图时替换按钮为版权信息（如果是ShareCard）
        onclone: (clonedDoc) => {
          const clonedElement = clonedDoc.querySelector(`[data-card-id="${props.cardId}"]`) ||
                               clonedDoc.querySelector('.f-cer.w-full')

          if (clonedElement) {
            // 如果是ShareCard、GitHub ShareCard或Compare卡片，查找并替换按钮容器
            if (props.cardId === 'share-card' || props.cardId === 'share-card-github' ||
                props.cardId === 'share-card-compare' || props.cardId === 'share-card-github-compare') {
              const buttonContainer = clonedElement.querySelector('[data-action-buttons]')
              if (buttonContainer) {
                // 根据卡片类型确定样式参数
                const isGitHubType = props.cardId === 'share-card-github' || props.cardId === 'share-card-github-compare'
                const isCompareType = props.cardId === 'share-card-compare' || props.cardId === 'share-card-github-compare'

                // 为GitHub和比较卡片使用更紧凑的样式
                const bottomDistance = (isGitHubType || isCompareType) ? '8px' : '16px'
                const fontSize = (isGitHubType || isCompareType) ? '12px' : '14px'
                const gap = (isGitHubType || isCompareType) ? '8px' : '12px'
                const qrSize = (isGitHubType || isCompareType) ? '48px' : '60px'

                // 创建右下角信息容器（版权信息+二维码）
                const bottomRightContainer = clonedDoc.createElement('div')
                bottomRightContainer.style.cssText = `position: absolute; bottom: ${bottomDistance}; right: 16px; display: flex; align-items: center; gap: ${gap}; z-index: 10;`

                // 创建版权信息元素
                const copyrightDiv = clonedDoc.createElement('div')
                copyrightDiv.className = 'text-3.5 font-400'
                copyrightDiv.style.fontSize = fontSize
                copyrightDiv.style.color = isGitHubType ? (props.isDark ? '#7A7A7A' : '#666') : '#666'
                copyrightDiv.textContent = 'Copyright @ 2025 DINQ Inc. All rights reserved'

                // 创建二维码元素
                const qrCode = clonedDoc.createElement('img')
                qrCode.src = '/image/qrcode.png'
                qrCode.alt = 'QR Code'
                qrCode.style.cssText = `width: ${qrSize}; height: ${qrSize}; flex-shrink: 0;`

                // 将版权信息和二维码添加到右下角容器
                bottomRightContainer.appendChild(copyrightDiv)
                bottomRightContainer.appendChild(qrCode)

                // 替换按钮容器为右下角容器
                buttonContainer.parentNode?.replaceChild(bottomRightContainer, buttonContainer)
              }

              // 修复深色模式下的边框颜色
              const isDarkMode = props.isDark || false
              if (isDarkMode) {
                // 修复所有卡片的边框颜色
                const cardElements = clonedElement.querySelectorAll('.border-gray-200')
                cardElements.forEach(card => {
                  (card as HTMLElement).style.borderColor = '#27282D'
                })

                // 修复主卡片的边框颜色
                const mainCard = clonedElement.querySelector(`[data-card-id="${props.cardId}"] > div`)
                if (mainCard) {
                  (mainCard as HTMLElement).style.borderColor = '#27282D'
                }

                // 修复卡片标题文字颜色
                const titleElements = clonedElement.querySelectorAll('.text-black')
                titleElements.forEach(title => {
                  (title as HTMLElement).style.color = '#FAF9F5'
                })
              }

              // 修复rolemodel部分的背景色显示问题
              const achievementElement = clonedElement.querySelector('[data-achievement-bg]')
              if (achievementElement) {
                // 使用传入的isDark状态
                const isDarkMode = props.isDark || false
                const achievementEl = achievementElement as HTMLElement
                achievementEl.style.backgroundColor = isDarkMode ? '#222222' : '#FDF0EB'
                achievementEl.style.color = isDarkMode ? '#C6C6C6' : '#495160'
                // 确保圆角显示
                achievementEl.style.borderRadius = '4px'
              }

              // GitHub卡片特有的样式修复
              if (props.cardId === 'share-card-github' || props.cardId === 'share-card-github-compare') {
                // 替换SVG图标为PNG图片以解决html2canvas兼容性问题
                const svgIconElements = clonedElement.querySelectorAll('svg.svg-icon')
                svgIconElements.forEach((svgEl) => {
                  const svgElement = svgEl as SVGElement
                  const useElement = svgElement.querySelector('use')
                  if (!useElement) return

                  const iconId = useElement.getAttribute('xlink:href') || useElement.getAttribute('href')
                  if (!iconId) return

                  const imgElement = clonedDoc.createElement('img')

                  // 根据图标ID确定要使用的图片
                  if (iconId === '#icon-verified') {
                    imgElement.src = '/image/sharecard/github-verify.png'
                    imgElement.alt = 'verified'
                    imgElement.className = 'w-4 h-4 mt-0.5 flex-shrink-0'
                  } else if (iconId === '#icon-research') {
                    imgElement.src = '/image/sharecard/overview.png'
                    imgElement.alt = 'overview'
                    imgElement.className = 'w-5 h-5'
                  } else if (iconId === '#icon-add1') {
                    imgElement.src = '/image/sharecard/additions.png'
                    imgElement.alt = 'additions'
                    imgElement.className = 'w-5 h-5'
                  } else if (iconId === '#icon-trash-bin') {
                    imgElement.src = '/image/sharecard/deletions.png'
                    imgElement.alt = 'deletions'
                    imgElement.className = 'w-5 h-5'
                  } else if (iconId === '#icon-project') {
                    imgElement.src = '/image/sharecard/highlight.png'
                    imgElement.alt = 'highlight'
                    imgElement.className = 'w-5 h-5'
                  } else if (iconId === '#icon-stars') {
                    imgElement.src = '/image/sharecard/stars.png'
                    imgElement.alt = 'stars'
                    imgElement.className = 'w-4 h-4'
                  } else if (iconId === '#icon-forks') {
                    imgElement.src = '/image/sharecard/forks.png'
                    imgElement.alt = 'forks'
                    imgElement.className = 'w-4 h-4'
                  } else if (iconId === '#icon-growth') {
                    imgElement.src = '/image/sharecard/marketvalue.png'
                    imgElement.alt = 'market value'
                    imgElement.className = 'w-5 h-5'
                  } else if (iconId === '#icon-growth-investing') {
                    imgElement.src = '/image/sharecard/yoe.png'
                    imgElement.alt = 'yoe'
                    imgElement.className = 'w-5 h-5'
                  }

                  // 替换SVG为IMG
                  if (imgElement.src) {
                    svgElement.parentNode?.replaceChild(imgElement, svgElement)
                  }
                })

                // 替换头像的fallback路径
                const avatarImages = clonedElement.querySelectorAll('img[src*="@/assets/image/avator.png"]')
                avatarImages.forEach(img => {
                  const imgEl = img as HTMLImageElement
                  imgEl.src = '/image/avator.png'
                })

                // 修复深色模式下的边框颜色和高亮卡片样式
                const isDarkMode = props.isDark

                // 修复小卡片的背景图片显示
                const customBgCards = clonedElement.querySelectorAll('.custom-bg')
                customBgCards.forEach((card) => {
                  const cardEl = card as HTMLElement

                  // 根据卡片内容确定背景图片类型
                  const isAdditionsCard = cardEl.textContent?.includes('Additions') || cardEl.querySelector('img[alt="additions"]')
                  const isDeletionsCard = cardEl.textContent?.includes('Deletions') || cardEl.querySelector('img[alt="deletions"]')
                  const isMarketValueCard = cardEl.textContent?.includes('Market Value') || cardEl.querySelector('img[alt="market value"]')
                  const isYoECard = cardEl.textContent?.includes('YoE') || cardEl.querySelector('img[alt="yoe"]')

                  // 强制设置背景图片，使用!important确保优先级
                  if (isDarkMode) {
                    // 深色模式统一使用深色背景
                    cardEl.style.setProperty('background-image', 'url(/image/sharecard/Group2xdark.png)', 'important')
                  } else {
                    // 亮色模式根据卡片类型使用不同背景
                    if (isAdditionsCard || isMarketValueCard) {
                      // Additions和Market Value使用Group2x1.png
                      cardEl.style.setProperty('background-image', 'url(/image/sharecard/Group2x1.png)', 'important')
                    } else if (isDeletionsCard || isYoECard) {
                      // Deletions和YoE使用Group2x.png
                      cardEl.style.setProperty('background-image', 'url(/image/sharecard/Group2x.png)', 'important')
                    }
                  }

                  // 确保其他背景属性正确设置
                  cardEl.style.setProperty('background-repeat', 'no-repeat', 'important')
                  cardEl.style.setProperty('background-size', 'contain', 'important')
                  cardEl.style.setProperty('background-position', 'left top', 'important')

                  // 修复文字颜色以匹配背景图片
                  const textElements = cardEl.querySelectorAll('[class*="text-[#"]')
                  textElements.forEach(textEl => {
                    const textElement = textEl as HTMLElement
                    if (isDarkMode) {
                      // 深色模式下的文字颜色
                      if (isAdditionsCard || isMarketValueCard) {
                        // 蓝色系卡片 - 深色模式使用较亮的蓝色
                        if (textElement.classList.contains('text-[#5F6D94]') || textElement.classList.contains('dark:text-[#A5AEC6]')) {
                          textElement.style.setProperty('color', '#A5AEC6', 'important')
                        }
                      } else if (isDeletionsCard || isYoECard) {
                        // 橙色系卡片 - 深色模式使用较亮的橙色
                        if (textElement.classList.contains('text-[#CB7C5D]') || textElement.classList.contains('dark:text-[#B28383]')) {
                          textElement.style.setProperty('color', '#B28383', 'important')
                        }
                      }
                    } else {
                      // 浅色模式下的文字颜色
                      if (isAdditionsCard || isMarketValueCard) {
                        // 蓝色系卡片 - 确保文字颜色正确
                        if (textElement.classList.contains('text-[#5F6D94]')) {
                          textElement.style.setProperty('color', '#5F6D94', 'important')
                        }
                      } else if (isDeletionsCard || isYoECard) {
                        // 橙色系卡片 - 确保文字颜色正确
                        if (textElement.classList.contains('text-[#CB7C5D]')) {
                          textElement.style.setProperty('color', '#CB7C5D', 'important')
                        }
                      }
                    }
                  })
                })

                // 修复毛玻璃效果卡片的背景和样式
                const glassCards = clonedElement.querySelectorAll('[style*="backdrop-filter: blur(14px)"]')
                glassCards.forEach(card => {
                  const cardEl = card as HTMLElement
                  if (isDarkMode) {
                    // 深色模式：增强背景不透明度以模拟毛玻璃效果
                    cardEl.style.backgroundColor = 'rgba(20, 20, 21, 0.85)'
                    cardEl.style.backdropFilter = 'none' // 移除backdrop-filter，因为html2canvas不支持
                    cardEl.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.3)'
                  } else {
                    // 亮色模式：增强背景不透明度以模拟毛玻璃效果
                    cardEl.style.backgroundColor = 'rgba(255, 255, 255, 0.85)'
                    cardEl.style.backdropFilter = 'none' // 移除backdrop-filter，因为html2canvas不支持
                    cardEl.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.1)'
                  }
                })

                // 修复高亮卡片的背景颜色和边框（适用于所有模式）
                const highlightCards = clonedElement.querySelectorAll('.border-l-4')
                highlightCards.forEach(card => {
                  const cardEl = card as HTMLElement
                  if (isDarkMode) {
                    // 深色模式样式
                    cardEl.style.backgroundColor = '#222222'
                    cardEl.style.borderLeftColor = '#654D43'
                    cardEl.style.borderLeftWidth = '4px'
                    cardEl.style.borderLeftStyle = 'solid'
                  } else {
                    // 亮色模式样式
                    cardEl.style.backgroundColor = '#FAF2EF'
                    cardEl.style.borderLeftColor = '#C69279'
                    cardEl.style.borderLeftWidth = '4px'
                    cardEl.style.borderLeftStyle = 'solid'
                  }
                })
              }

              // GitHub比较卡片特有的标签样式修复
              if (props.cardId === 'share-card-github-compare') {
                const isDarkMode = props.isDark

                // 修复标签样式 - GitHub compare卡片
                const allTags = clonedElement.querySelectorAll('.tag-component')

                allTags.forEach((tag) => {
                  const tagEl = tag as HTMLElement

                  // 检查标签的父容器来判断是左侧还是右侧
                  let isLeftSide = false
                  let isRightSide = false

                  // 向上查找父元素，寻找justify-end（左侧）或justify-start（右侧）
                  let parent = tagEl.parentElement
                  while (parent && parent !== clonedElement) {
                    const parentClasses = parent.className

                    if (parentClasses.includes('justify-end')) {
                      isLeftSide = true
                      break
                    }
                    if (parentClasses.includes('justify-start')) {
                      isRightSide = true
                      break
                    }
                    parent = parent.parentElement
                  }

                  if (isDarkMode) {
                    if (isLeftSide) {
                      // share-blue variant 深色模式样式（左侧用户）
                      tagEl.style.backgroundColor = '#3C4356'
                      tagEl.style.borderColor = '#7F8EB7'
                      tagEl.style.color = '#C2C5CE'
                      tagEl.style.borderWidth = '0.5px'
                      tagEl.style.borderStyle = 'solid'
                    } else if (isRightSide) {
                      // share-orange variant 深色模式样式（右侧用户）
                      tagEl.style.backgroundColor = '#413834'
                      tagEl.style.borderColor = '#71635E'
                      tagEl.style.color = '#E1BCAD'
                      tagEl.style.borderWidth = '0.5px'
                      tagEl.style.borderStyle = 'solid'
                    }
                  } else {
                    // 亮色模式下也要确保标签样式正确
                    if (isLeftSide) {
                      // share-blue variant 亮色模式样式（左侧用户）
                      tagEl.style.backgroundColor = '#EEF1F9'
                      tagEl.style.borderColor = '#7F8EB7'
                      tagEl.style.color = '#7F8EB7'
                      tagEl.style.borderWidth = '0.5px'
                      tagEl.style.borderStyle = 'solid'
                    } else if (isRightSide) {
                      // share-orange variant 亮色模式样式（右侧用户）
                      tagEl.style.backgroundColor = '#FBEAE3'
                      tagEl.style.borderColor = '#CB7C5D'
                      tagEl.style.color = '#CB7C5D'
                      tagEl.style.borderWidth = '0.5px'
                      tagEl.style.borderStyle = 'solid'
                    }
                  }
                })
              }

              // Scholar比较卡片特有的标签样式修复
              if (props.cardId === 'share-card-compare') {
                const isDarkMode = props.isDark

                // 修复标签样式 - Scholar compare卡片
                const allTags = clonedElement.querySelectorAll('.tag-component')

                allTags.forEach((tag) => {
                  const tagEl = tag as HTMLElement

                  // 检查标签的父容器来判断是左侧还是右侧
                  let isLeftSide = false
                  let isRightSide = false

                  // 向上查找父元素，寻找位置标识
                  let parent = tagEl.parentElement
                  while (parent && parent !== clonedElement) {
                    const parentClasses = parent.className
                    const parentStyle = parent.getAttribute('style') || ''

                    if (parentClasses.includes('left-[15px]') || parentStyle.includes('left: 15px')) {
                      isLeftSide = true
                      break
                    }
                    if (parentClasses.includes('right-[15px]') || parentStyle.includes('right: 15px')) {
                      isRightSide = true
                      break
                    }
                    parent = parent.parentElement
                  }

                  if (isDarkMode) {
                    if (isLeftSide) {
                      // share-blue variant 深色模式样式
                      tagEl.style.backgroundColor = '#3C4356'
                      tagEl.style.borderColor = '#7F8EB7'
                      tagEl.style.color = '#C2C5CE'
                      tagEl.style.borderWidth = '0.5px'
                      tagEl.style.borderStyle = 'solid'
                    } else if (isRightSide) {
                      // share-orange variant 深色模式样式
                      tagEl.style.backgroundColor = '#413834'
                      tagEl.style.borderColor = '#71635E'
                      tagEl.style.color = '#E1BCAD'
                      tagEl.style.borderWidth = '0.5px'
                      tagEl.style.borderStyle = 'solid'
                    }
                  } else {
                    // 亮色模式下也要确保标签样式正确
                    if (isLeftSide) {
                      // share-blue variant 亮色模式样式
                      tagEl.style.backgroundColor = '#EEF1F9'
                      tagEl.style.borderColor = '#7F8EB7'
                      tagEl.style.color = '#7F8EB7'
                      tagEl.style.borderWidth = '0.5px'
                      tagEl.style.borderStyle = 'solid'
                    } else if (isRightSide) {
                      // share-orange variant 亮色模式样式
                      tagEl.style.backgroundColor = '#FBEAE3'
                      tagEl.style.borderColor = '#CB7C5D'
                      tagEl.style.color = '#CB7C5D'
                      tagEl.style.borderWidth = '0.5px'
                      tagEl.style.borderStyle = 'solid'
                    }
                  }
                })
              }
            }
          }
        }
      })

      // 将canvas转换为blob
      const blob = await new Promise<Blob | null>(resolve => {
        canvas.toBlob(resolve, 'image/png', 1.0)
      })

      if (!blob) {
        throw new Error('Failed to create image blob')
      }

      // 保存blob到组件状态
      imageBlob.value = blob

      // 复制到剪贴板
      await navigator.clipboard.write([
        new ClipboardItem({
          'image/png': blob,
        }),
      ])

      // 图片准备就绪，等待用户确认跳转
      isReadyToShare.value = true

    } catch (error) {
      console.error('Share failed:', error)
      // 提供备用方案提示
      alert('Share failed. This might be due to CORS issues with external images. The image has been copied to clipboard if possible.')
    } finally {
      isLoading.value = false
    }
  }
</script>

<style scoped>
  .animate-spin {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .share-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background-color: rgb(192, 132, 108);
    border: none;
    border-radius: 20px;
    cursor: pointer;
    color: white;
    font-weight: 500;
    transition: background-color 0.2s;
  }

  .share-button:hover {
    background-color: rgb(172, 112, 88);
  }

  .share-button img {
    width: 20px;
    height: 20px;
  }
</style>
