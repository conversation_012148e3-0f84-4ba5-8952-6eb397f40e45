<template>
  <div class="landing-layout">
    <!-- Landing Page 专用导航栏 - 固定顶部 -->
    <LandingHeader />
    
    <!-- 主要内容区域 -->
    <main class="landing-main">
      <slot />
    </main>
  </div>
</template>

<script setup lang="ts">
// Landing page 布局不需要通用页脚，因为第七页开始会有自己的页脚内容
</script>

<style scoped>
.landing-layout {
  min-height: 100vh;
  overflow-x: hidden; /* 防止水平滚动 */
  width: 100%; /* 确保宽度不超出视窗 */
  max-width: 100vw; /* 防止宽度超出视窗 */
  font-family: 'Alexandria', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

.landing-main {
  /* 第一页为固定导航栏预留空间，其他页面不需要 */
  padding-top: 0;
  width: 100%; /* 确保主要内容区域不超出 */
  overflow-x: hidden; /* 防止水平滚动 */
}

/* 只为第一页预留导航栏空间 */
.landing-main > section:first-child {
  padding-top: 10vh;
}

/* 移动端响应式 */
@media (max-width: 768px) {
  .landing-main > section:first-child {
    padding-top: 8vh; /* 移动端使用更小的导航栏高度 */
  }
}
</style> 