<template>
  <div class="gradient-bg h-full">
    <div class="max-w-360 rel mx-auto h-full flex flex-col min-h-screen">
      <AppHeader />
      <div class="flex-1 h-full flex-col flex">
        <slot />
      </div>
      <AppFooter />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useFirebaseAuth } from '~/composables/useFirebaseAuth'
useFirebaseAuth()
</script>

<style scoped>
.gradient-bg {
  background-image: url('/image/bg.png');
  background-position: top center;
  background-size: 100% 900px;
  background-repeat: no-repeat;
}

/* 深色模式下不显示背景图片 */
.dark .gradient-bg {
  background-image: none;
}
</style>