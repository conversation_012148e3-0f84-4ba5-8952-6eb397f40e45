import { GoogleAuthProvider, GithubAuthProvider } from 'firebase/auth';

export const googleProvider = new GoogleAuthProvider();
export const githubProvider = new GithubAuthProvider();

// 检测是否为Safari浏览器
export const isSafari = () => {
  if (typeof window === 'undefined') return false;
  return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
};

// 配置Google provider强制显示账号选择器
// 这可以解决Safari自动选择已登录账号而不给用户选择的问题
googleProvider.setCustomParameters({
  prompt: 'select_account'
});

// 可选：添加额外的OAuth 2.0作用域
googleProvider.addScope('email');
googleProvider.addScope('profile');

// 创建针对不同浏览器优化的Google Provider
export const createOptimizedGoogleProvider = () => {
  const provider = new GoogleAuthProvider();
  
  // 基础配置 - 对所有浏览器都应用
  provider.setCustomParameters({
    prompt: 'select_account'
  });
  
  provider.addScope('email');
  provider.addScope('profile');
  
  // Safari特定配置
  if (isSafari()) {
    console.log('Detected Safari browser, applying specific configurations...');
    // 为Safari添加额外参数以确保账号选择器显示
    provider.setCustomParameters({
      prompt: 'select_account',
      include_granted_scopes: 'true',
      access_type: 'online'
    });
  }
  
  return provider;
};
