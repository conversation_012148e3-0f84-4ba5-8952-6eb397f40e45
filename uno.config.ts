import { defineConfig, presetIcons, presetUno } from 'unocss'
import presetWind from '@unocss/preset-wind'
import transformerDirectives from '@unocss/transformer-directives'
import transformerVariantGroup from '@unocss/transformer-variant-group'

export default defineConfig({
  presets: [
    presetUno({
      dark: 'class'
    }), 
    presetWind(), 
    presetIcons()
  ],
  transformers: [transformerVariantGroup(), transformerDirectives()],
  shortcuts: [
    [/^wh-(.*)$/, ([, t]) => `w-${t} h-${t}`],
    [/^text-limit-(\d{0,})$/, ([, n]) => `line-clamp-${n}`],
    {
      rel: 'relative',
      abs: 'absolute',
      fix: 'fixed',
    },
    {
      'fx-cer': 'flex items-center',
      'f-cer': 'flex items-center justify-center',
    },
  ],
  theme: {
    colors: {
      neutral: {
        0: '#EDEDED',
        60: '#6B625D',
        100: '#434A4F',
        200: '#4E5969',
        300: '#D9D7E5'
      },
      black: {
        DEFAULT: '#000000',
        70: '#141415',
        100: '#1C1C21',
        200: '#1F1F1F',
        300: '#1D2129'
      },
      white: {
        DEFAULT: '#FFFFFF',
        60: '#FFFFFF60',
      },
      primary: {
        DEFAULT: '#5765F2',
        50: '#CB7C5D50',
        100: '#CB7C5D',
        900: '#9B5C3D',
      },
      secondary: {
        DEFAULT: '#D48146',
        100: '#D48146',
      },
      discord: {
        DEFAULT: '#5765F2',
      },
      twitter: {
        DEFAULT: '#282828',
      },
      gray: {
        50: '#F9FAFB',
        100: '#F3F4F6',
        200: '#E5E7EB',
        300: '#D1D5DB',
        400: '#9CA3AF',
        500: '#6B7280',
        600: '#4B5563',
        700: '#374151',
        800: '#1F2937',
        900: '#111827',
      },
      special: {
        cream: '#F1E0D9',
        pink: '#FFDEDA',
        lightPink: '#FBEAE3',
        blush: '#FDF0EB',
        softPeach: '#FAF2EF',
        lightGreen: '#D1F0C9'
      },
    },
  },
})
