/* Base theme styles */
:root {
  color-scheme: light;
}

:root[data-theme="dark"] {
  color-scheme: dark;
}

/* Theme transition */
html {
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Dark mode base styles */
html.dark {
  background-color: #0F0F0F;
  color: #ffffff;
}

html.dark body {
  background-color: #0F0F0F;
  color: #ffffff;
}

/* Light mode base styles */
html:not(.dark) {
  background-color: #ffffff;
  color: #000000;
}

/* Theme transition for specific elements */
.dark * {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* Dark mode specific styles - 移除全局的 bg-white 覆盖 */
.dark .bg-gray-50 {
  background-color: #111827 !important;
}

.dark .bg-gray-100 {
  background-color: #374151 !important;
}

.dark .border-gray-100 {
  border-color: #374151 !important;
}

.dark .text-gray-500 {
  color: #9CA3AF !important;
}

.dark .text-gray-600 {
  color: #D1D5DB !important;
}

.dark .text-gray-700 {
  color: #E5E7EB !important;
}

.dark .text-gray-800 {
  color: #F3F4F6 !important;
}

.dark .text-gray-900 {
  color: #F9FAFB !important;
} 

/* 全局样式文件（如 main.css） */
.dark .custom-input {
  background-color: white !important;
}

