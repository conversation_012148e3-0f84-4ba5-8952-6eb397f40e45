<svg width="265" height="148" viewBox="0 0 265 148" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="265" height="148" rx="4" fill="#292929"/>
<mask id="mask0_4347_210738" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="265" height="148">
<rect width="265" height="148" rx="4" fill="#EBEDF2"/>
</mask>
<g mask="url(#mask0_4347_210738)">
<g opacity="0.1">
<circle cx="258" cy="123" r="73.5" stroke="url(#paint0_linear_4347_210738)" stroke-width="39"/>
<circle cx="258" cy="123" r="43" stroke="url(#paint1_linear_4347_210738)" stroke-width="22"/>
</g>
</g>
<defs>
<linearGradient id="paint0_linear_4347_210738" x1="258" y1="30" x2="209.5" y2="160.5" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_4347_210738" x1="234.068" y1="76.9773" x2="286.841" y2="164.114" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
</defs>
</svg>
