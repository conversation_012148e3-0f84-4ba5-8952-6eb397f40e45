<svg width="400" height="400" viewBox="0 0 400 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <style>
    .outer-circle {
      fill: #FFFAF9;
    }
    .inner-circle {
      fill: none;
    }
    .decorative-path {
      fill: none;
      stroke: #CB7C5D;
    }
    @media (prefers-color-scheme: dark) {
      .outer-circle {
        fill: #1E1E1E;
      }
      .inner-circle {
        fill: #3C3C3C;
      }
      .decorative-path {
        stroke: #686868;
      }
    }
    .dark .outer-circle {
      fill: #1E1E1E;
    }
    .dark .inner-circle {
      fill: #3C3C3C;
    }
    .dark .decorative-path {
      stroke: #686868;
    }
  </style>
  <!-- 外圈大圆 -->
  <circle class="outer-circle" cx="200" cy="200" r="200" />
  <!-- 内圈圆 (缩小的) -->
  <circle class="inner-circle" cx="200" cy="200" r="130" />
  <!-- 装饰性路径 -->
  <path
    class="decorative-path"
    d="M322.696 188.215C325.266 167.898 323.81 147.274 318.409 127.52C313.009 107.766 303.771 89.2696 291.223 73.0859C278.674 56.9022 263.061 43.3486 245.274 33.1989C227.488 23.0493 207.876 16.5023 187.56 13.9319C167.243 11.3614 146.619 12.8178 126.865 18.2179C107.111 23.618 88.6142 32.8561 72.4305 45.4046C56.2468 57.9531 42.6932 73.5664 32.5436 91.3529C22.3939 109.14 15.847 128.751 13.2765 149.068C10.7061 169.385 12.1625 190.009 17.5626 209.762C22.9627 229.516 32.2007 248.013 44.7493 264.197C57.2978 280.381 72.9111 293.934 90.6976 304.084C108.484 314.233 128.096 320.78 148.412 323.351C168.729 325.921 189.353 324.465 209.107 319.065C228.861 313.665 247.358 304.427 263.542 291.878C279.725 279.30 293.279 263.716 303.428 245.93C313.578 228.143 320.125 208.532 322.696 188.215L322.696 188.215Z"
    stroke-width="21.8856" stroke-dasharray="0.51 5.09"
    transform="translate(32.01, 31.36)" />
</svg>