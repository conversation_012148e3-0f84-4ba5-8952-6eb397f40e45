#!/usr/bin/env node

/**
 * 部署验证脚本
 * 用于验证 Cloudflare Pages 部署是否成功
 */

import https from 'https'
import { URL } from 'url'

// 配置
const DOMAIN = process.argv[2] || 'your-domain.com'
const TEST_URLS = [
  `/github?query=octocat`,
  `/github/compare?user1=octocat&user2=defunkt`,
  `/compare?researcher1=John%20Doe&researcher2=Jane%20Smith`
]

console.log(`🔍 验证部署: https://${DOMAIN}\n`)

// 模拟不同的 User-Agent
const USER_AGENTS = {
  'Browser': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
  'Twitter': 'Twitterbot/1.0',
  'Facebook': 'facebookexternalhit/1.1',
  'LinkedIn': 'LinkedInBot/1.0'
}

async function fetchPage(url, userAgent = 'Browser') {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: DOMAIN,
      path: url,
      method: 'GET',
      headers: {
        'User-Agent': USER_AGENTS[userAgent] || USER_AGENTS['Browser']
      }
    }

    const req = https.request(options, (res) => {
      let data = ''
      res.on('data', (chunk) => data += chunk)
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data
        })
      })
    })

    req.on('error', reject)
    req.setTimeout(10000, () => reject(new Error('Request timeout')))
    req.end()
  })
}

function checkMetaTags(html, url) {
  const checks = {
    hasTitle: /<title[^>]*>([^<]+)<\/title>/.test(html),
    hasDescription: /<meta[^>]*name="description"[^>]*content="([^"]*)"/.test(html),
    hasOgTitle: /<meta[^>]*property="og:title"[^>]*content="([^"]*)"/.test(html),
    hasOgDescription: /<meta[^>]*property="og:description"[^>]*content="([^"]*)"/.test(html),
    hasOgImage: /<meta[^>]*property="og:image"[^>]*content="([^"]*)"/.test(html),
    hasTwitterCard: /<meta[^>]*name="twitter:card"[^>]*content="([^"]*)"/.test(html),
    hasCanonical: /<link[^>]*rel="canonical"[^>]*href="([^"]*)"/.test(html)
  }

  // 提取具体内容
  const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/)
  const descMatch = html.match(/<meta[^>]*name="description"[^>]*content="([^"]*)"/)
  const ogTitleMatch = html.match(/<meta[^>]*property="og:title"[^>]*content="([^"]*)"/)

  return {
    checks,
    title: titleMatch ? titleMatch[1] : null,
    description: descMatch ? descMatch[1] : null,
    ogTitle: ogTitleMatch ? ogTitleMatch[1] : null
  }
}

async function verifyUrl(url) {
  console.log(`\n📄 测试: ${url}`)
  
  try {
    // 测试不同的 User-Agent
    for (const [agentName, agent] of Object.entries(USER_AGENTS)) {
      console.log(`  🤖 ${agentName}:`)
      
      const response = await fetchPage(url, agentName)
      
      if (response.statusCode === 200) {
        console.log(`    ✅ 状态码: ${response.statusCode}`)
        
        const metaInfo = checkMetaTags(response.body, url)
        
        // 检查关键 meta 标签
        if (metaInfo.checks.hasTitle) {
          console.log(`    ✅ 标题: ${metaInfo.title}`)
        } else {
          console.log(`    ❌ 缺少标题`)
        }
        
        if (metaInfo.checks.hasOgTitle) {
          console.log(`    ✅ OG 标题: ${metaInfo.ogTitle}`)
        } else {
          console.log(`    ❌ 缺少 OG 标题`)
        }
        
        if (metaInfo.checks.hasTwitterCard) {
          console.log(`    ✅ Twitter Card 存在`)
        } else {
          console.log(`    ❌ 缺少 Twitter Card`)
        }
        
        // 检查是否是 SSR
        const isSSR = response.body.includes('<meta') && !response.body.includes('__NUXT__')
        console.log(`    ${isSSR ? '✅' : '❓'} SSR 检测: ${isSSR ? '可能是 SSR' : '可能是 CSR'}`)
        
      } else {
        console.log(`    ❌ 状态码: ${response.statusCode}`)
      }
      
      // 只对第一个 User-Agent 显示详细信息
      if (agentName === 'Browser') break
    }
    
  } catch (error) {
    console.log(`    ❌ 错误: ${error.message}`)
  }
}

async function main() {
  if (DOMAIN === 'your-domain.com') {
    console.log('❌ 请提供您的域名:')
    console.log('   node verify-deployment.js your-actual-domain.com')
    console.log('\n例如:')
    console.log('   node verify-deployment.js dinq-frontend.pages.dev')
    return
  }

  console.log('🚀 开始验证部署...')
  
  for (const url of TEST_URLS) {
    await verifyUrl(url)
  }
  
  console.log('\n📋 验证工具链接:')
  console.log(`🐦 Twitter: https://cards-dev.twitter.com/validator`)
  console.log(`📘 Facebook: https://developers.facebook.com/tools/debug/`)
  console.log(`💼 LinkedIn: https://www.linkedin.com/post-inspector/`)
  
  console.log('\n🔗 测试 URL:')
  TEST_URLS.forEach(url => {
    console.log(`   https://${DOMAIN}${url}`)
  })
  
  console.log('\n✨ 验证完成!')
}

main().catch(console.error)
