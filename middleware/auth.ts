export default defineNuxtRouteMiddleware(async (to) => {
  if (process.server) return
  
  const { currentUser, authInitialized } = useFirebaseAuth()
  
  // 等待 Firebase 认证初始化完成
  await new Promise<void>((resolve) => {
    if (authInitialized.value) {
      resolve()
      return
    }
    
    // 如果还未初始化，等待最多 3 秒
    let timeoutId: NodeJS.Timeout
    const unwatch = watch(authInitialized, (initialized) => {
      if (initialized) {
        clearTimeout(timeoutId)
        unwatch()
        resolve()
      }
    })
    
    // 3秒超时保护
    timeoutId = setTimeout(() => {
      console.warn('Firebase auth initialization timeout')
      unwatch()
      resolve()
    }, 3000)
  })
  
  if (!currentUser.value) {
    return navigateTo('/analysis')
  }
})
