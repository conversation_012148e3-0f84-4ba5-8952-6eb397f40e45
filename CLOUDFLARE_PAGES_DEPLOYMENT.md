# Cloudflare Pages 部署指南

## 🚨 当前问题解决方案

您遇到的 `wrangler.toml` 配置错误是因为 Cloudflare Pages 对配置文件有特殊要求。

### 解决方案 1: 删除 wrangler.toml（推荐）

我已经删除了 `wrangler.toml` 文件，因为 Nuxt 的 `cloudflare-pages` preset 会自动处理大部分配置。

### 解决方案 2: 使用 Cloudflare Pages 控制台配置

在 Cloudflare Pages 控制台中手动配置构建设置：

```
构建命令: npm run build
构建输出目录: .nuxt/dist
根目录: /
```

## 📋 部署步骤

### 1. 推送代码到 Git 仓库
```bash
git add .
git commit -m "feat: Complete SSR migration with dynamic meta tags"
git push origin main
```

### 2. 在 Cloudflare Pages 中创建项目
1. 登录 Cloudflare Dashboard
2. 进入 Pages 部分
3. 点击 "Create a project"
4. 连接您的 Git 仓库

### 3. 配置构建设置
```
Framework preset: Nuxt.js
Build command: npm run build
Build output directory: .nuxt/dist
Root directory: / (留空)
```

### 4. 环境变量（如果需要）
在 Cloudflare Pages 设置中添加：
```
NODE_VERSION=18
NUXT_SSR=true
```

## 🔧 验证部署

### 检查 SSR 是否工作
部署完成后，访问以下 URL 验证：
- `https://your-domain.com/github?query=octocat`
- `https://your-domain.com/github/compare?user1=octocat&user2=defunkt`

### 验证 Meta 标签
使用以下工具验证 meta 标签：
1. **Twitter Card Validator**: https://cards-dev.twitter.com/validator
2. **Facebook Sharing Debugger**: https://developers.facebook.com/tools/debug/
3. **curl 测试**:
   ```bash
   curl -H "User-Agent: Twitterbot/1.0" https://your-domain.com/github?query=octocat
   ```

## 🐛 故障排除

### 如果 SSR 不工作
1. 检查 Cloudflare Pages 的构建日志
2. 确认构建命令和输出目录正确
3. 验证 `nuxt.config.ts` 中的 `preset: 'cloudflare-pages'`

### 如果 Meta 标签不显示
1. 确认页面是 SSR 而不是客户端渲染
2. 检查浏览器开发者工具中的 Network 标签
3. 验证 HTML 源码中是否包含 meta 标签

### 常见错误
1. **构建失败**: 检查 Node.js 版本是否为 18+
2. **函数超时**: 可能需要优化代码或增加超时时间
3. **静态资源 404**: 检查资源路径是否正确

## 📊 性能优化建议

### 1. 代码分割
当前构建显示有大于 500KB 的 chunks，建议：
```typescript
// nuxt.config.ts
export default defineNuxtConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'echarts': ['echarts'],
          'firebase': ['firebase/app', 'firebase/auth'],
          'charts': ['vue-chartjs', 'chart.js']
        }
      }
    }
  }
})
```

### 2. 图片优化
使用 Nuxt Image 模块优化图片加载：
```vue
<NuxtImg src="/image.png" width="400" height="300" />
```

### 3. 字体优化
考虑减少字体文件数量或使用 Web Fonts API。

## ✅ 部署检查清单

- [ ] 代码已推送到 Git 仓库
- [ ] Cloudflare Pages 项目已创建
- [ ] 构建设置已正确配置
- [ ] 环境变量已设置（如需要）
- [ ] 首次部署成功
- [ ] SSR 页面正常工作
- [ ] Meta 标签正确显示
- [ ] Twitter/Facebook 分享测试通过

## 🎯 预期结果

部署成功后，您将拥有：
- ✅ 混合渲染应用（静态 + SSR）
- ✅ 动态 Meta 标签支持
- ✅ Twitter/Facebook 分享优化
- ✅ 高性能边缘部署
- ✅ 自动 HTTPS 和 CDN

如果遇到任何问题，请检查 Cloudflare Pages 的构建日志获取详细错误信息。
