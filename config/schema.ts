import * as yup from 'yup'

export const loginSchema = yup.object({
  name: yup.string().required('Username is required.'),
  email: yup.string().email('Invalid email address.').required('Email is required.'),
  affiliation: yup.string().required('Affiliation is required.'),
  country: yup.string().required('Country is required.'),
  jobTitle: yup.string().required('Job title is required.'),
  provide: yup.string().required('Provide is required.'),
  ReasonForContact: yup.string().required('Reason for contact is required.'),
})
