export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { user1, user2, ogImageUrl } = body

    if (!user1 || !user2 || !ogImageUrl) {
      throw createError({
        statusCode: 400,
        statusMessage: 'user1, user2 and ogImageUrl are required'
      })
    }

    // TODO: 这里需要根据您的数据库实现来保存比较页面OG图片URL
    // 示例：await db.compareOgImage.upsert({ 
    //   where: { user1_user2: `${user1}_${user2}` },
    //   create: { user1, user2, ogImageUrl },
    //   update: { ogImageUrl }
    // })
    
    // 暂时返回成功，实际实现需要连接您的数据库
    console.log(`Saving compare OG image URL for ${user1} vs ${user2}: ${ogImageUrl}`)
    
    return {
      success: true,
      message: 'Compare OG image URL saved successfully'
    }
  } catch (error) {
    console.error('Error saving compare OG image URL:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to save compare OG image URL'
    })
  }
})
