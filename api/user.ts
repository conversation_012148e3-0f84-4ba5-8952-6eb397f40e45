import { get, del, post, put, getFunc, postFile } from "../utils/request"

// 获取当前用户信息;
export const getCurrentUser = (headers: Record<string, any>) => getFunc<any>('/api/user/me', {}, headers);


// 获取当前firebase 用户信息;
export const getCurrentFirebaseUser = (headers: Record<string, any>) => getFunc<any>('/api/user/firebase-info', {}, headers);

// 更新用户信息;
export const updateUserInfo = (data: Record<string, any>, headers: Record<string, any>) => put<any>('/api/user/me', data, headers);


// 获取用户的验证状态；
export const getUserVerification = (headers: Record<string, any>) => getFunc<any>('/api/verification/status', {}, headers);


// 开始求职者的验证流程；
export const startCandidateVerification = (data: Record<string, string>, headers: Record<string, any>) => post<any>('/api/verification/start', data, headers);

// 文件上传;
export const uploadFile = (data: FormData, headers: Record<string, any>) => postFile<any>('/api/upload-image', data, headers);

// 更新验证状态
export const updateVerificationStatus = (data: Record<string, string>, headers: Record<string, any>) => post<any>('/api/verification/update-step', data, headers);

// 邮箱验证；
export const verifyEmail = (data: Record<string, string>, headers: Record<string, any>) => post<any>('/api/verification/send-email-verification', data, headers);


// 提交验证；
export const submitVerification = (data: Record<string, string>, headers: Record<string, any>) => post<any>('/api/verification/complete', data, headers);


// 邮箱验证；
export const verifyEmailAuth = (data: Record<string, string>, headers: Record<string, any>) => post<any>('/api/verification/verify-email', data, headers);