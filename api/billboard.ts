import { get, del, post, put, getFunc } from "../utils/request"

// 获取职位发布列表;
export const getJobBoardPosts = (data: Record<string , any>, headers: Record<string, any>) => getFunc<TalentRes>(`/api/job-board/posts`,{
    ...data,
}, headers)


// 获取单个post的内容;
export const getSinglePost = (postId: string) => get<TalentRes>(`/api/job-board/posts/${postId}`,{
})

// 实现删除单个post帖子;
export const delSinglePost = (postId: string, headers: Record<string, any>) => del<TalentRes>(`/api/job-board/posts/${postId}`, headers);

// 创建帖子;
export const createPost = (data: Record<string, any>, headers?: Record<string, any>) => post<any>(`/api/job-board/posts`, data, headers);

// 编辑帖子;
export const editPost = (post_id: string, data: Record<string, any>, headers?: Record<string, any>) => put<{success: boolean; data: Record<string, any>}>(`/api/job-board/posts/${post_id}`, data, headers);

// 收藏帖子;
export const collectPost = (post_id: string, data: Record<string, any>, headers?: Record<string, any>) => post<{success: boolean; data: Record<string, any>}>(`/api/job-board/posts/${post_id}/bookmark`, data, headers);

// 取消收藏帖子;
export const delCollectPost = (post_id: string, headers?: Record<string, any>) => del<{success: boolean; data: Record<string, any>}>(`/api/job-board/posts/${post_id}/bookmark`, headers);

// 获取当前登录人发布的帖子;
export const getMyPosts =  (data: Record<string , any>, headers: Record<string, any>) => getFunc<any>(`/api/job-board/my-posts`, {
    ...data
}, headers)


// 获取收藏帖子;
export const getCollectedPosts = (limit: number, offset: number, headers: Record<string, any>) => getFunc<any>(`/api/job-board/my-bookmarked-posts?limit=${limit}&&offset=${offset}`,{}, headers)
