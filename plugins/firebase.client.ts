import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";

export default defineNuxtPlugin(() => {
  // const firebaseConfig = {
  //   apiKey: "AIzaSyDqSSWVT7FM1h_CfVbVTKD3Eewuezei2iY",
  //   authDomain: "devgo-demo.firebaseapp.com",
  //   projectId: "devgo-demo",
  //   appId: "1:1023234252705:web:bd9c64e5799d344b0335e0",
  // };

  const firebaseConfig = {
    apiKey: "AIzaSyAfOdW0ff_S51-zz4PLlX4-oB2Eh4BvH6k",
    authDomain: "auth.dinq.io",
    projectId: "dinq-1a6c0",
    storageBucket: "dinq-1a6c0.firebasestorage.app",
    messagingSenderId: "869684242555",
    appId: "1:869684242555:web:6edd44ed302f4bfdf29ade",
    measurementId: "G-ER11YVS0Q7"
  };

  const app = initializeApp(firebaseConfig);
  const auth = getAuth(app);

  return {
    provide: {
      firebase: app,
      auth,
    },
  };
});
