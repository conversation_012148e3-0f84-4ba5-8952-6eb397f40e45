import { defineNuxtPlugin } from '#imports'
import useEmitter from '~/composables/useEmitter'

export default defineNuxtPlugin((nuxtApp) => {
  const { emit, on, off } = useEmitter()

  nuxtApp.provide('emitter', {
    emit,
    on,
    off
  })
})

declare module '#app' {
  interface NuxtApp {
    $emitter: {
      emit: <T extends keyof Events>(type: T, payload?: Events[T]) => void
      on: <T extends keyof Events>(type: T, handler: (payload: Events[T]) => void) => void
      off: <T extends keyof Events>(type: T, handler: (payload: Events[T]) => void) => void
    }
  }
}