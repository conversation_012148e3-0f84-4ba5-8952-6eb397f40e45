// API configuration
interface ApiConfig {
  baseUrl: string;
  endpoints: {
    stream: string;
    // Add more endpoints here as needed
  };
}

// Default configuration (development)
const defaultConfig: ApiConfig = {
  baseUrl: 'https://api.dinq.io',
  endpoints: {
    stream: '/api/stream',
  }
};

// Production configuration
const productionConfig: ApiConfig = {
  // 使用Nuxt的环境变量命名约定
  baseUrl: import.meta.env.NUXT_PUBLIC_API_BASE || 'https://api.dinq.io',
  endpoints: {
    stream: '/api/stream',
  }
};

// Determine which configuration to use based on environment
const config = process.env.NODE_ENV === 'production' ? productionConfig : defaultConfig;

// Helper function to get full URL for an endpoint
export const getApiUrl = (endpoint: keyof ApiConfig['endpoints']): string => {
  return `${config.baseUrl}${config.endpoints[endpoint]}`;
};

// 导出API基础URL，可以在其他地方使用
export const apiBaseUrl = config.baseUrl;

export default config;
