{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "build:prod": "nuxt build --dotenv .env.production", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint --ext .js,.ts,.vue --ignore-path .gitignore .", "lint:fix": "eslint --ext .js,.ts,.vue --ignore-path .gitignore . --fix", "format": "prettier --write \"**/*.{js,ts,vue,json,md}\""}, "dependencies": {"@nuxt/fonts": "0.11.1", "@nuxt/image": "1.10.0", "@unocss/reset": "0.63.3", "@vueuse/core": "^13.1.0", "chart.js": "^4.4.8", "chartjs-plugin-datalabels": "^2.2.0", "dom-to-image": "^2.6.0", "echarts": "^5.6.0", "firebase": "^10.14.1", "html2canvas-pro": "^1.5.11", "ipx": "^3.0.3", "mitt": "^3.0.1", "motion-v": "1.0.0-beta.1", "nuxt": "^3.16.2", "typed.js": "^2.1.0", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-chartjs": "^5.3.2", "vue-router": "^4.5.0", "vue-virtual-scroller": "^2.0.0-beta.8", "vue3-marquee": "^4.2.2", "vue3-resize": "^0.2.0", "yup": "^1.6.1"}, "devDependencies": {"@iconify/json": "^2.2.323", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@unocss/nuxt": "0.63.3", "@unocss/preset-wind": "0.63.3", "@unocss/transformer-directives": "0.63.3", "@unocss/transformer-variant-group": "0.63.3", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.21.1", "prettier": "^3.2.5", "sass-embedded": "^1.86.3", "unocss": "0.63.3", "vite": "^5.0.0", "vite-plugin-svg-icons": "^2.0.1"}}