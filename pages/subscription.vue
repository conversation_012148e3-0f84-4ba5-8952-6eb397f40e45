<template>
  <div class="w-full flex flex-col items-center px-4">
    <!-- Title -->
    <h1 class="mt-16 text-42px font-600 leading-130% text-#25324B dark:text-#E8E8E8 text-center clash-semibold">
      DINQ Subscription Plans
    </h1>
    
    <!-- Toggle Button Container -->
    <div class="mt-8 w-300px h-54px bg-#FFFFFF99 dark:bg-#0F0F10 dark:border dark:border-#27272A rounded-105px flex items-center relative p-1.5">
      <!-- Sliding background -->
      <div class="w-141px h-42px bg-#000000 dark:bg-#1F1F22 rounded-60px absolute transition-all duration-300 ease-in-out"
           :style="{ left: billingCycle === 'monthly' ? '6px' : '153px' }">
      </div>
      
      <!-- Monthly button -->
      <div class="flex-1 flex items-center justify-center cursor-pointer relative z-10 h-full" 
           @click="setBillingCycle('monthly')">
        <span class="text-14px font-500 transition-colors duration-300" 
              :class="billingCycle === 'monthly' ? 'text-white' : 'text-#666 dark:text-#999'">
          Monthly
        </span>
      </div>
      
      <!-- Yearly button -->
      <div class="flex-1 flex items-center justify-center cursor-pointer relative z-10 h-full" 
           @click="setBillingCycle('yearly')">
        <span class="text-14px font-500 transition-colors duration-300" 
              :class="billingCycle === 'yearly' ? 'text-white' : 'text-#666 dark:text-#999'">
          Yearly
        </span>
      </div>
    </div>
    
    <!-- Savings Banner -->
    <!-- <div class="mt-5 w-1200px h-40px bg-#FFFFFF99 dark:bg-#0F0F10 rounded-4px flex items-center justify-center">
      <p class="text-13px font-400 leading-22px text-#484848 dark:text-#7B7B7B" 
         style="font-family: 'Poppins', sans-serif;">
        Save up to 30% with the annual plan.
      </p>
    </div> -->

    <!-- Coming Soon Banner -->
    <div class="mt-5 w-1200px h-40px bg-#FFFFFF99 dark:bg-#0F0F10 dark:border dark:border-#27272A rounded-12px flex items-center justify-center">
      <p class="text-13px font-400 leading-22px text-#484848 dark:text-#7B7B7B" 
         style="font-family: 'Poppins', sans-serif;">
        Subscription Plans Coming Soon
      </p>
    </div>
    
    <!-- Subscription Cards -->
    <div class="mt-5 mb-8 flex gap-6 justify-center flex-wrap max-w-1200px">
      <!-- Free Plan -->
      <div class="w-384px h-569px bg-#FFFFFF dark:bg-#181818 rounded-16px p-8 flex flex-col relative">
        <!-- Current plan tag -->
        <div class="absolute left-0 top-0 bg-[#F8E6DD] dark:bg-[#3A2F2A] text-[#C47A5A] dark:text-[#E8B896] text-sm font-bold px-4 py-2 rounded-tl-2xl rounded-br-2xl">
          Current plan
        </div>
        <!-- Blur overlay for Free Plan -->
        <div class="absolute inset-0 backdrop-blur-md bg-white/30 dark:bg-black/30 rounded-16px z-10 pointer-events-none"></div>
        <div class="text-center mb-8 h-32">
          <h3 class="text-24px font-600 text-#25324B dark:text-#E8E8E8 mb-2">Free</h3>
          <div class="text-48px font-700 text-#25324B dark:text-#E8E8E8 mb-2">
            0
            <span class="text-16px font-400 text-#666 dark:text-#999">
              USD / month
            </span>
          </div>
          <div class="h-5">
            <!-- 占位空间，保持与其他卡片一致 -->
          </div>
        </div>
        
        <button class="w-320px h-48px bg-#F0F0F0 dark:bg-#2A2A2A text-#25324B dark:text-#E8E8E8 rounded-100px font-500 text-16px hover:bg-#E0E0E0 dark:hover:bg-#3A3A3A transition-colors mx-auto mb-6">
          Get Started
        </button>
        
        <!-- Divider -->
        <div class="w-full h-px bg-#E5E5E5 dark:bg-#333333 mb-6"></div>
        
        <div class="flex-1">
          <ul class="space-y-4">
            <li class="flex items-start gap-3">
              <div class="w-5 h-5 bg-#CB7C5D rounded-full flex items-center justify-center mt-0.5">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <span class="text-14px text-#484848 dark:text-#B8B8B8">5 credits per month</span>
            </li>
            <li class="flex items-start gap-3">
              <div class="w-5 h-5 bg-#CB7C5D rounded-full flex items-center justify-center mt-0.5">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <span class="text-14px text-#484848 dark:text-#B8B8B8">Slow Queue access</span>
            </li>
            <li class="flex items-start gap-3">
              <div class="w-5 h-5 bg-#CB7C5D rounded-full flex items-center justify-center mt-0.5">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <span class="text-14px text-#484848 dark:text-#B8B8B8">No Billboards (view-only access)</span>
            </li>
          </ul>
        </div>
      </div>
      
      <!-- Standard Plan -->
      <div class="w-384px h-569px bg-#FFFFFF dark:bg-#181818 rounded-16px p-8 flex flex-col relative">
        <!-- Blur overlay for Standard Plan -->
        <div class="absolute inset-0 backdrop-blur-md bg-white/30 dark:bg-black/30 rounded-16px z-10 pointer-events-none"></div>
        <div class="text-center mb-8 h-32">
          <h3 class="text-24px font-600 text-#25324B dark:text-#E8E8E8 mb-2">Standard</h3>
          <div class="flex items-center justify-center gap-2 mb-2">
            <div class="text-48px font-700 text-#25324B dark:text-#E8E8E8">
              {{ billingCycle === 'monthly' ? '359' : '25' }}
              <span class="text-16px font-400 text-#666 dark:text-#999">
                USD / month
              </span>
            </div>
            <div v-if="billingCycle === 'yearly'" class="bg-#FDF0EB text-#CB7C5D text-12px px-2 py-1 rounded-full font-500">
              -20% off
            </div>
          </div>
          <div class="h-5">
            <p v-if="billingCycle === 'yearly'" class="text-14px text-#999 line-through">
              25 USD/month
            </p>
          </div>
        </div>
        
        <button class="w-320px h-48px bg-#CB7C5D dark:bg-#A66B4A text-white dark:text-#F5F5F5 rounded-100px font-500 text-16px hover:bg-#B86B4E dark:hover:bg-#8F5A3F transition-colors mx-auto mb-6">
          Upgrade to Standard
        </button>
        
        <!-- Divider -->
        <div class="w-full h-px bg-#E5E5E5 dark:bg-#333333 mb-6"></div>
        
        <div class="flex-1">
          <ul class="space-y-4">
            <li class="flex items-start gap-3">
              <div class="w-5 h-5 bg-#CB7C5D rounded-full flex items-center justify-center mt-0.5">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <span class="text-14px text-#484848 dark:text-#B8B8B8">DINQ Standard Plan Includes</span>
            </li>
            <li class="flex items-start gap-3">
              <div class="w-5 h-5 bg-#CB7C5D rounded-full flex items-center justify-center mt-0.5">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <span class="text-14px text-#484848 dark:text-#B8B8B8">50 credits per month</span>
            </li>
            <li class="flex items-start gap-3">
              <div class="w-5 h-5 bg-#CB7C5D rounded-full flex items-center justify-center mt-0.5">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <span class="text-14px text-#484848 dark:text-#B8B8B8">Fast Queue access</span>
            </li>
            <li class="flex items-start gap-3">
              <div class="w-5 h-5 bg-#CB7C5D rounded-full flex items-center justify-center mt-0.5">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <span class="text-14px text-#484848 dark:text-#B8B8B8">Up to 3 Billboards per day</span>
            </li>
          </ul>
        </div>
      </div>
      
      <!-- Premium Plan -->
      <div class="w-384px h-569px bg-#FFFFFF dark:bg-#181818 rounded-16px p-8 flex flex-col relative">
        <!-- Blur overlay for Premium Plan -->
        <div class="absolute inset-0 backdrop-blur-md bg-white/30 dark:bg-black/30 rounded-16px z-10 pointer-events-none"></div>
        <div class="text-center mb-8 h-32">
          <h3 class="text-24px font-600 text-#25324B dark:text-#E8E8E8 mb-2">Premium</h3>
          <div class="flex items-center justify-center gap-2 mb-2">
            <div class="text-48px font-700 text-#25324B dark:text-#E8E8E8">
              {{ billingCycle === 'monthly' ? '119' : '125' }}
              <span class="text-16px font-400 text-#666 dark:text-#999">
                USD / month
              </span>
            </div>
            <div v-if="billingCycle === 'yearly'" class="bg-#FDF0EB text-#CB7C5D text-12px px-2 py-1 rounded-full font-500">
              -25% off
            </div>
          </div>
          <div class="h-5">
            <p v-if="billingCycle === 'yearly'" class="text-14px text-#999 line-through">
              125 USD/month
            </p>
          </div>
        </div>
        
        <button class="w-320px h-48px bg-#000000 dark:bg-#1A1A1A text-white dark:text-#F5F5F5 rounded-100px font-500 text-16px hover:bg-#333333 dark:hover:bg-#2A2A2A transition-colors mx-auto mb-6">
          Upgrade to Premium
        </button>
        
        <!-- Divider -->
        <div class="w-full h-px bg-#E5E5E5 dark:bg-#333333 mb-6"></div>
        
        <div class="flex-1">
          <ul class="space-y-4">
            <li class="flex items-start gap-3">
              <div class="w-5 h-5 bg-#CB7C5D rounded-full flex items-center justify-center mt-0.5">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <span class="text-14px text-#484848 dark:text-#B8B8B8">150 credits per month</span>
            </li>
            <li class="flex items-start gap-3">
              <div class="w-5 h-5 bg-#CB7C5D rounded-full flex items-center justify-center mt-0.5">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <span class="text-14px text-#484848 dark:text-#B8B8B8">Fast Queue access</span>
            </li>
            <li class="flex items-start gap-3">
              <div class="w-5 h-5 bg-#CB7C5D rounded-full flex items-center justify-center mt-0.5">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <span class="text-14px text-#484848 dark:text-#B8B8B8">Unlimited Billboards</span>
            </li>
            <li class="flex items-start gap-3">
              <div class="w-5 h-5 bg-#CB7C5D rounded-full flex items-center justify-center mt-0.5">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <span class="text-14px text-#484848 dark:text-#B8B8B8">Featured Rights (priority placement & visibility)</span>
            </li>
            <li class="flex items-start gap-3">
              <div class="w-5 h-5 bg-#CB7C5D rounded-full flex items-center justify-center mt-0.5">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <span class="text-14px text-#484848 dark:text-#B8B8B8">Early Access to new features</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const billingCycle = ref<'monthly' | 'yearly'>('monthly')

const setBillingCycle = (cycle: 'monthly' | 'yearly') => {
  billingCycle.value = cycle
}
</script>
