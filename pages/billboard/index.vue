<template>
  <!-- 底层纯白色背景 -->
  <div class="bg-white-base"></div>
  
  <!-- 渐变背景层 -->
  <div class="bg-gradient"></div>
  
  <!-- 背景图片 -->
  <div class="bg-grid"></div>

  <!-- 装饰性背景元素容器 -->
  <div class="decorative-bg-container">
    <div class="bg-square"></div>
    <div class="bg-vector1"></div>
    <div class="bg-vector2"></div>
  </div>

  <!-- '/image/billboard_bg.png' -->
  <div
    class="min-h-screen relative"
    style="margin-top: 24px;"
  >
    <!-- Left Menu -->
    <div
      class="shadow-sm flex flex-col sidebar-menu"
      style="width: 250px; top: 30px; left: 30px; border-radius: 15px; position: absolute; bottom: 0;"
    >
      <button
        class="mx-4 mt-4 mb-6 bg-black dark:bg-white text-white dark:text-black rounded-md py-2 px-4 flex items-center justify-center gap-2"
        @click="handlePublishClick"
      >
        <div class="i-carbon:add text-lg"></div>
        Publish
      </button>

      <div class="space-y-1 px-2">
        <button
          class="w-full flex items-center gap-3 px-4 py-2 rounded-lg bg-white/10 dark:bg-transparent text-primary-100 transition-all duration-200"
          :class="
            route.path === '/billboard'
              ? 'selected-button'
              : ''
          "
          @click="router.push('/billboard')"
        >
          <!-- <img src="/image/discover.png" class="w-10 h-10" alt="discover icon" /> -->
          <div class="discover-btn w-10 h-10"></div>
          <span
            style="font-size: 14px"
            :class="route.path === '/billboard' ? 'text-primary-100 font-bold dark:text-white' : 'text-black dark:text-[#919294]'"
            >Discover</span
          >
        </button>
        <button
          class="w-full flex items-center gap-3 px-4 py-2 rounded-lg bg-white/10 dark:bg-transparent transition-all duration-200"
          :class="
            route.path === '/my-bulletin'
              ? 'selected-button'
              : ''
          "
          @click="handleMyBulletinClick"
        >
          <!-- <div class="i-carbon:document text-xl"></div> -->
          <!-- <img src="/image/bullition.png" class="w-10 h-10" alt="discover icon" /> -->
          <div class="mybulletin-btn w-10 h-10"></div>
          <span
            style="font-size: 14px"
            :class="
              route.path === '/my-bulletin'
                ? 'text-primary-100 font-bold dark:text-white'
                : 'text-black dark:text-[#919294]'
            "
            >My Bulletin</span
          >
        </button>
      </div>
    </div>

    <!-- Main Content -->
    <div class="relative" style="margin-left: 280px; z-index: 20; padding: 32px 32px 32px 32px;">
      <div class="relative">
        <!-- Header -->
        <div class="text-center relative py-4 px-8 rounded-2xl">
          <h1 class="font-bold text-gray-900 mb-3 clash-semibold" style="font-size: 20px">
            Welcome to Billboard
          </h1>
          <h2 class="text-4xl font-semibold text-gray-800 mb-4 clash-semibold">
            Your Gateway to Opportunities,<br />Talents, and Partnerships
          </h2>
          <p class="mx-auto leading-relaxed text-gray-500 dark:text-gray-500" style="font-size: 14px; width: 674px;">
            A brand-new online community designed for those seeking opportunities, showcasing
            talents, and finding partners. Whether you are a company, a job seeker, or an
            entrepreneur, Billboard is the ideal platform to achieve your goals.
          </p>
        </div>

        <!-- Filter Bar -->
        <div class="flex justify-between items-center mb-6">
          <div class="relative">
            <select
              v-model="selectedCategory"
              class="appearance-none pl-4 pr-10 py-2.5 border border-gray-200 dark:border-[#444954] rounded-lg bg-white text-gray-500 text-xs focus:outline-none focus:border-gray-300 dark:focus:border-[#555] focus:ring-1 focus:ring-gray-200 dark:focus:ring-[#555] cursor-pointer min-w-[300px] w-[300px]"
              @change="handleCategoryChange"
            >
              <option value="">All Categories</option>
              <option value="job">Job</option>
              <option value="internship">Internship</option>
              <option value="collaboration">Collaboration</option>
              <option value="others">Others</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <div class="i-carbon:chevron-down text-gray-500"></div>
            </div>
          </div>
          <button
            class="flex items-center refresh-btn gap-2 px-4 py-2.5 bg-[#CB7C5D] dark:bg-[#54565A] text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-xs"
            @click="handleRefresh"
            :disabled="isSpinning"
          >
            <img src="/image/refresh.png" class="w-5 h-5" :class="{ 'animate-spin': isSpinning }" />
            Refresh
          </button>
        </div>

        <!-- Cards Grid -->
        <div
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3"
        >
          <!-- Skeleton Loading -->
          <template v-if="isSpinning">
            <div
              v-for="n in 6"
              :key="n"
              class="bg-white rounded-xl p-6 shadow-sm animate-pulse min-h-[280px] flex flex-col"
            >
              <div class="flex items-center gap-2 mb-4">
                <div class="h-5 w-24 bg-gray-100 rounded-full"></div>
                <div class="h-5 w-20 bg-gray-100 rounded-full"></div>
                <div class="ml-auto h-5 w-16 bg-gray-100 rounded-full"></div>
              </div>
              <div class="h-6 w-3/4 bg-gray-100 rounded mb-3"></div>
              <div class="space-y-2 flex-grow">
                <div class="h-4 w-full bg-gray-100 rounded"></div>
                <div class="h-4 w-5/6 bg-gray-100 rounded"></div>
                <div class="h-4 w-4/6 bg-gray-100 rounded"></div>
              </div>
              <div class="mt-4 flex items-center gap-2">
                <div class="h-5 w-5 bg-gray-100 rounded-full"></div>
                <div class="h-4 w-24 bg-gray-100 rounded"></div>
                <div class="ml-auto h-4 w-16 bg-gray-100 rounded"></div>
              </div>
            </div>
          </template>

          <!-- Empty State -->
          <template v-else-if="!data.length">
            <div class="col-span-full flex flex-col items-center justify-center py-12 px-4" style="margin-top: 50px;">
              <img 
                :src="isDark ? '/image/noResult-dark.png' : '/image/noResult.png'" 
                alt="No results" 
                style="width: 242px; height: 242px;" 
              />
              <div class="mt-6 text-center">
                <p class="text-gray-500 dark:text-gray-400 text-lg">No posts found</p>
                <p class="text-gray-400 dark:text-gray-500 text-sm mt-2">Try adjusting your filters or check back later</p>
              </div>
            </div>
          </template>

          <!-- Actual Content -->
          <template v-else>
            <div
              v-for="post in data"
              :key="post.id"
              class="bg-white rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer flex flex-col"
              @click="handleCardClick(post)"
            >
              <div class="flex items-center gap-2 mb-4">
                <span
                  class="px-2.5 py-0.5 bg-primary-50/20 text-primary-100 rounded text-xs truncate max-w-[120px] flex items-center justify-center gap-1"
                >
                  <div class="i-carbon:document text-sm"></div>
                  {{
                    post.post_type === 'job'
                      ? 'Job Openings'
                      : post.post_type === 'internship'
                        ? 'Internship'
                        : post.post_type === 'collaboration'
                          ? 'Collaboration'
                          : 'Others'
                  }}
                </span>
                <span
                  class="px-2.5 py-0.5 rounded text-xs truncate max-w-[120px] flex items-center justify-center gap-1 border-0.5"
                  :class="{
                    'bg-[#F0F2FA] text-[#7F8EB7] border-transparent dark:bg-[#545A6A] dark:text-[#C2CCE6] dark:border-[#6A6E78]': post.entity_type === 'company',
                    'bg-[#EAF3EB] text-[#779273] border-transparent dark:bg-[#475348] dark:text-[#AFC7AC] dark:border-[#677065]': post.entity_type === 'headhunter',
                    'bg-[#FBEAE3] text-[#CB7C5D] border-transparent dark:bg-[#59514E] dark:text-[#F2D5C9] dark:border-[#7B6E68]': post.entity_type === 'individual',
                    'bg-primary-50/20 text-primary-100 border-transparent': post.entity_type === 'others',
                  }"
                >
                  <img
                    v-if="post.entity_type !== 'others'"
                    :src="
                      post.entity_type === 'company'
                        ? '/image/Company.png'
                        : post.entity_type === 'headhunter'
                          ? '/image/Headhunting.png'
                          : '/image/Individual.png'
                    "
                    class="w-3.5 h-3.5 ml-0.5"
                    alt="entity type icon"
                  />
                  {{
                    post.entity_type === 'company'
                      ? 'Company'
                      : post.entity_type === 'headhunter'
                        ? 'Headhunter'
                        : post.entity_type === 'individual'
                          ? 'Individual'
                          : 'Others'
                  }}
                </span>
                <span class="ml-auto">
                  <button
                    class="p-1.5 bg-white dark:bg-black hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors flex items-center gap-1"
                    @click.stop="handleCollect(post)"
                  >
                    <div
                      class="text-lg"
                      :class="
                        post.is_collected
                          ? 'text-primary-100 i-carbon:bookmark-filled'
                          : 'text-gray-400 i-carbon:bookmark'
                      "
                    ></div>
                    <span class="text-xs text-gray-500">{{ post.bookmark_count || 0 }}</span>
                  </button>
                </span>
              </div>

              <h3 class="text-xl font-semibold mb-3 line-clamp-2">{{ post.title }}</h3>
              
              <!-- Tags -->
              <div v-if="post.tags && post.tags.length" class="mb-3 flex flex-wrap gap-1.5">
                <span
                  v-for="tag in post.tags.slice(0, 3)"
                  :key="tag"
                  class="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-xs truncate max-w-[160px] flex items-center gap-1"
                >
                  <div class="i-carbon:tag text-xs flex-shrink-0"></div>
                  {{ tag.replace('#', '') }}
                </span>
                <span
                  v-if="post.tags.length > 3"
                  class="px-2 py-1 bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-400 rounded-full text-xs"
                >
                  +{{ post.tags.length - 3 }}
                </span>
              </div>

              <p
                v-html="linkify(post.content)"
                class="text-gray-600 text-sm line-clamp-5 flex-grow"
              ></p>

              <!-- Salary Range -->
              <div v-if="post.salary_range" class="mt-4">
                <div class="inline-flex items-center gap-1 text-sm text-primary-100 bg-primary-50/10 dark:bg-primary-100/10 px-2 py-1 rounded">
                  <div class="i-carbon:money flex-shrink-0"></div>
                  <span class="truncate">{{ formatSalaryDisplay(post.salary_range) }}</span>
                </div>
              </div>

              <!-- Location -->
              <div v-if="post.location" class="mt-3">
                <div class="flex items-center gap-1 text-sm text-gray-500 dark:text-gray-500 px-2 py-1 rounded">
                  <div class="i-carbon:location flex-shrink-0"></div>
                  <span class="truncate">
                    {{ post.location }}
                  </span>
                </div>
              </div>

              <div class="mt-4 flex items-center justify-between">
                <!-- Left: User info -->
                <div class="flex items-center gap-2">
                  <img src="/image/avator.png" class="w-6 h-6 rounded-full flex-shrink-0" />
                  <span class="text-sm font-medium truncate max-w-[120px]">{{ post.display_name || post.user_id || 'Anonymous' }}</span>
                  <div class="i-carbon:checkmark-filled text-primary-100 flex-shrink-0"></div>
                </div>
                
                <!-- Right: View count -->
                <span class="text-sm text-gray-500 whitespace-nowrap fx-cer gap-1 dark:text-white">
                  <SvgIcon :name="isDark ? 'eyeDark' : 'eye'" />{{ post.view_count }}
                </span>
              </div>

              <!-- Bottom: Time info -->
              <div class="mt-2 text-xs text-gray-400 dark:text-gray-500 flex items-center gap-1">
                <div class="i-carbon:time flex-shrink-0"></div>
                <span>{{ formatTimeAgo(post.updated_at || post.created_at) }}</span>
              </div>
            </div>
          </template>
        </div>

        <!-- Detail Modal -->
        <div
          v-if="selectedPost"
          class="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
          @click.self="selectedPost = null"
          style="z-index: 50;"
        >
          <div class="bg-white rounded-2xl w-full max-w-lg max-h-[110vh] overflow-y-auto relative">
            <!-- Skeleton Loading -->
            <template v-if="isLoadingPost">
              <div class="p-5 animate-pulse">
                <div class="flex items-center gap-2 mb-3">
                  <div class="h-5 w-24 bg-gray-100 rounded-full"></div>
                  <div class="h-5 w-20 bg-gray-100 rounded-full"></div>
                  <div class="ml-auto h-5 w-16 bg-gray-100 rounded-full"></div>
                </div>

                <div class="h-6 w-3/4 bg-gray-100 rounded mb-3"></div>

                <div class="space-y-2 mb-4">
                  <div class="h-4 w-full bg-gray-100 rounded"></div>
                  <div class="h-4 w-5/6 bg-gray-100 rounded"></div>
                  <div class="h-4 w-4/6 bg-gray-100 rounded"></div>
                </div>

                <div class="grid grid-cols-2 gap-3 mb-4">
                  <div class="h-5 w-24 bg-gray-100 rounded"></div>
                  <div class="h-5 w-24 bg-gray-100 rounded"></div>
                  <div class="h-5 w-24 bg-gray-100 rounded"></div>
                </div>

                <div class="flex flex-wrap gap-2 mb-4">
                  <div class="h-5 w-16 bg-gray-100 rounded-full"></div>
                  <div class="h-5 w-20 bg-gray-100 rounded-full"></div>
                  <div class="h-5 w-24 bg-gray-100 rounded-full"></div>
                  <div class="h-5 w-16 bg-gray-100 rounded-full"></div>
                </div>

                <div class="flex items-center gap-2 pt-3 border-t">
                  <div class="h-5 w-5 bg-gray-100 rounded-full"></div>
                  <div class="h-5 w-24 bg-gray-100 rounded"></div>
                  <div class="ml-auto h-5 w-16 bg-gray-100 rounded"></div>
                </div>
              </div>
            </template>

            <!-- Content -->
            <template v-else>
              <div class="p-5 flex flex-col h-full">
                <!-- Close Button -->
                <button
                  class="absolute top-4 right-4 p-1.5 bg-white dark: bg-black hover:bg-gray-100 rounded-full transition-colors"
                  @click.stop="selectedPost = null"
                >
                  <div class="i-carbon:close text-xl text-gray-500"></div>
                </button>

                <!-- Header section -->
                <div class="flex items-center gap-2 mb-3 pr-12">
                  <span class="px-2 py-1 bg-primary-50/20 text-primary-100 rounded-full text-xs flex items-center gap-1">
                    <div class="i-carbon:document text-sm"></div>
                    {{
                      selectedPost.post_type === 'job'
                        ? 'Job Openings'
                        : selectedPost.post_type === 'internship'
                          ? 'Internship'
                          : selectedPost.post_type === 'collaboration'
                            ? 'Collaboration'
                            : 'Others'
                    }}
                  </span>
                                  <span
                  class="px-2 py-1 rounded-full text-xs flex items-center gap-1 border-0.5"
                  :class="{
                    'bg-[#F0F2FA] text-[#7F8EB7] border-transparent dark:bg-[#545A6A] dark:text-[#C2CCE6] dark:border-[#6A6E78]': selectedPost.entity_type === 'company',
                    'bg-[#EAF3EB] text-[#779273] border-transparent dark:bg-[#475348] dark:text-[#AFC7AC] dark:border-[#677065]': selectedPost.entity_type === 'headhunter',
                    'bg-[#FBEAE3] text-[#CB7C5D] border-transparent dark:bg-[#59514E] dark:text-[#F2D5C9] dark:border-[#7B6E68]': selectedPost.entity_type === 'individual',
                    'bg-primary-50/20 text-primary-100 border-transparent': selectedPost.entity_type === 'others',
                  }"
                >
                    <img
                      v-if="selectedPost.entity_type !== 'others'"
                      :src="
                        selectedPost.entity_type === 'company'
                          ? '/image/Company.png'
                          : selectedPost.entity_type === 'headhunter'
                            ? '/image/Headhunting.png'
                            : '/image/Individual.png'
                      "
                      class="w-3.5 h-3.5 ml-0.5"
                      alt="entity type icon"
                    />
                    {{
                      selectedPost.entity_type === 'company'
                        ? 'Company'
                        : selectedPost.entity_type === 'headhunter'
                          ? 'Headhunter'
                          : selectedPost.entity_type === 'individual'
                            ? 'Individual'
                            : 'Others'
                    }}
                  </span>
                  <span
                    v-if="selectedPost.company"
                    class="px-2 py-1 bg-gray-100 rounded-full text-xs flex items-center gap-1"
                  >
                    <div class="i-carbon:enterprise"></div>
                    {{ selectedPost.company }}
                  </span>
                  <span class="ml-auto text-xs text-gray-500">{{
                    formatDate(selectedPost.created_at)
                  }}</span>
                </div>

                <!-- Title -->
                <h2 class="text-lg font-bold mb-3">{{ selectedPost.title }}</h2>
                
                <!-- Content - flexible height -->
                <div
                  v-html="linkify(selectedPost.content)"
                  class="text-gray-600 dark:text-gray-300 mb-4 whitespace-pre-wrap text-sm overflow-y-auto modal-scroll flex-1 max-h-96"
                ></div>

                <!-- Links Section -->
                <div v-if="selectedPost.content && (extractLinks(selectedPost.content).emails.length > 0 || extractLinks(selectedPost.content).urls.length > 0)" class="mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                  <div class="flex items-center gap-2 mb-2">
                    <div class="i-carbon:link text-sm text-gray-500 dark:text-gray-400"></div>
                    <span class="text-xs font-medium text-gray-600 dark:text-gray-300">Links & Contacts</span>
                  </div>
                  
                  <!-- URLs -->
                  <div v-if="extractLinks(selectedPost.content).urls.length > 0" class="mb-2">
                    <div class="flex flex-wrap gap-2">
                      <a
                        v-for="(url, index) in extractLinks(selectedPost.content).urls"
                        :key="`url-${index}`"
                        :href="url"
                        target="_blank"
                        class="inline-flex items-center gap-1 px-2 py-1 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded text-xs hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors"
                        :title="url"
                      >
                        <div class="i-carbon:launch text-xs"></div>
                        <span>{{ generateShortLink(url, 25) }}</span>
                      </a>
                    </div>
                  </div>
                  
                  <!-- Emails -->
                  <div v-if="extractLinks(selectedPost.content).emails.length > 0">
                    <div class="flex flex-wrap gap-2">
                      <a
                        v-for="(email, index) in extractLinks(selectedPost.content).emails"
                        :key="`email-${index}`"
                        :href="`mailto:${email}`"
                        class="inline-flex items-center gap-1 px-2 py-1 bg-green-50 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded text-xs hover:bg-green-100 dark:hover:bg-green-900/50 transition-colors"
                      >
                        <div class="i-carbon:email text-xs"></div>
                        <span>{{ email }}</span>
                      </a>
                    </div>
                  </div>
                </div>

                <!-- Bottom section - pushed to bottom -->
                <div class="mt-auto">
                  <div class="grid grid-cols-2 gap-3 mb-4">
                    <div
                      v-if="selectedPost.location"
                      class="flex items-center gap-1 text-gray-600 text-xs"
                    >
                      <div class="i-carbon:location"></div>
                      <span>{{ selectedPost.location }}</span>
                    </div>
                    <div
                      v-if="selectedPost.position"
                      class="flex items-center gap-1 text-gray-600 text-xs"
                    >
                      <div class="i-carbon:user"></div>
                      <span>{{ selectedPost.position }}</span>
                    </div>
                    <div
                      v-if="selectedPost.salary_range"
                      class="flex items-center gap-1 text-primary-100 text-xs"
                    >
                      <div class="i-carbon:money"></div>
                      <span>{{ formatSalaryDisplay(selectedPost.salary_range) }}</span>
                    </div>
                  </div>

                  <div
                    v-if="selectedPost.tags && selectedPost.tags.length"
                    class="flex flex-wrap gap-2 mb-4"
                  >
                    <span
                      v-for="tag in selectedPost.tags"
                      :key="tag"
                      class="px-2 py-1 bg-gray-100 rounded-full text-xs"
                    >
                      {{ tag }}
                    </span>
                  </div>

                  <div class="flex items-center gap-2 pt-3 border-t dark:border-gray-700">
                    <img src="/image/avator.png" class="w-5 h-5 rounded-full" />
                    <div class="flex items-center gap-1">
                      <span class="font-medium text-xs">{{ selectedPost.display_name || selectedPost.user_id || 'Anonymous' }}</span>
                      <div class="i-carbon:checkmark-filled text-primary-100"></div>
                    </div>
                    <span class="ml-auto text-xs text-gray-500 fx-cer gap-1"
                      ><SvgIcon :name="isDark ? 'eyeDark' : 'eye'" />{{
                        selectedPost.view_count
                      }}</span
                    >
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>

        <!-- Delete Confirmation Modal -->
        <div
          v-if="showDeleteConfirm"
          class="fixed inset-0 bg-black/50 flex items-center justify-center z-[60]"
          @click.self="showDeleteConfirm = false"
        >
          <div class="bg-white rounded-xl p-6 w-full max-w-md">
            <div class="flex items-center gap-3 mb-4">
              <div class="i-carbon:warning-alt text-2xl text-red-500"></div>
              <h3 class="text-lg font-semibold text-gray-900">Delete Post</h3>
            </div>

            <p class="text-gray-600 mb-6">
              Are you sure you want to delete this post? This action cannot be undone.
            </p>

            <div class="flex justify-end gap-3">
              <button
                class="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                @click="showDeleteConfirm = false"
                :disabled="isDeleting"
              >
                Cancel
              </button>
              <button
                class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                @click="confirmDelete"
                :disabled="isDeleting"
              >
                <div v-if="isDeleting" class="i-carbon:circle-dash animate-spin"></div>
                <span>{{ isDeleting ? 'Deleting...' : 'Delete' }}</span>
              </button>
            </div>
          </div>
        </div>

        <!-- Error Toast -->
        <div
          v-if="showErrorToast"
          class="fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg flex items-center gap-2 z-[70] animate-fade-in"
        >
          <div class="i-carbon:warning-alt"></div>
          <span>{{ errorMessage }}</span>
        </div>

        <!-- Success Toast -->
        <div
          v-if="showSuccessToast"
          class="fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg flex items-center gap-2 z-[70] animate-fade-in"
        >
          <div class="i-carbon:checkmark"></div>
          <span>{{ successMessage }}</span>
        </div>

                <!-- Publish Modal -->
        <div
          v-if="showPublishModal"
          class="fixed inset-0 bg-black/30 flex items-center justify-center z-50 p-4"
        >
          <div class="relative w-full max-w-lg">
            <div class="bg-white dark:bg-gray-900 rounded-lg shadow-lg overflow-hidden">
              <!-- Scrollable Content -->
              <div class="max-h-[80vh] overflow-y-auto">
                <form @submit.prevent="handlePublish" class="p-6 space-y-4">
                  <!-- Title -->
                  <div class="mb-4">
                    <textarea
                      v-model="publishForm.title"
                      class="w-full p-0 font-semibold bg-gray-50 dark:bg-gray-600 border-0 outline-none resize-none text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                      style="border: none !important; box-shadow: none !important; font-size: 30px;"
                      placeholder="Post title..."
                      rows="1"
                      required
                      @input="autoResizeTitle"
                    />
                  </div>

                  <!-- Content -->
                  <div class="mb-4">
                    <textarea
                      ref="textareaRef"
                      v-model="publishForm.content"
                      class="w-full p-0 bg-gray-50 dark:bg-gray-600 border-0 outline-none resize-none text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                      style="border: none !important; box-shadow: none !important; font-size: 20px;"
                      rows="6"
                      placeholder="Describe your opportunity..."
                      @focus="isFocused = true"
                      @blur="handleBlur"
                      required
                    />
                  </div>

                  <!-- Category & Entity Type -->
                  <div class="grid grid-cols-2 gap-3">
                    <div class="relative">
                      <select
                        v-model="publishForm.post_type"
                        :class="{ 'text-gray-400': publishForm.post_type === '' }"
                        class="w-full px-3 py-2 pr-8 border border-gray-200 dark:border-gray-600 rounded-md focus:outline-none focus:border-gray-400 bg-white dark:bg-gray-800 text-gray-900 dark:text-white appearance-none cursor-pointer"
                        required
                      >
                        <option value="" disabled hidden>Category</option>
                        <option value="job">Job</option>
                        <option value="internship">Internship</option>
                        <option value="collaboration">Collaboration</option>
                        <option value="others">Others</option>
                      </select>
                      <div class="absolute right-2 top-1/2 -translate-y-1/2 pointer-events-none">
                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                      </div>
                    </div>

                    <div class="relative">
                      <select
                        v-model="publishForm.entity_type"
                        :class="{ 'text-gray-400': publishForm.entity_type === '' }"
                        class="w-full px-3 py-2 pr-8 border border-gray-200 dark:border-gray-600 rounded-md focus:outline-none focus:border-gray-400 bg-white dark:bg-gray-800 text-gray-900 dark:text-white appearance-none cursor-pointer"
                        required
                      >
                        <option value="" disabled hidden>Entity Type</option>
                        <option value="company">Company</option>
                        <option value="headhunter">Headhunter</option>
                        <option value="individual">Individual</option>
                      </select>
                      <div class="absolute right-2 top-1/2 -translate-y-1/2 pointer-events-none">
                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                      </div>
                    </div>
                  </div>

                  <!-- Salary -->
                  <div class="space-y-2">
                    <div class="relative">
                      <select
                        v-model="publishForm.salary_type"
                        :class="{ 'text-gray-400': publishForm.salary_type === '' }"
                        class="w-full px-3 py-2 pr-8 border border-gray-200 dark:border-gray-600 rounded-md focus:outline-none focus:border-gray-400 bg-white dark:bg-gray-800 text-gray-900 dark:text-white appearance-none cursor-pointer"
                      >
                        <option value="" disabled hidden>Salary type</option>
                        <option value="daily">Daily</option>
                        <option value="monthly">Monthly</option>
                        <option value="yearly">Yearly</option>
                        <option value="negotiable">Negotiable</option>
                      </select>
                      <div class="absolute right-2 top-1/2 -translate-y-1/2 pointer-events-none">
                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                      </div>
                    </div>

                    <div v-if="publishForm.salary_type && publishForm.salary_type !== 'negotiable'">
                      <div class="flex gap-0.5 items-center">
                        <input
                          v-model="publishForm.salary_amount"
                          type="number"
                          style="width: 191px;"
                          class="px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-md focus:outline-none focus:border-gray-400 bg-white dark:bg-gray-800 text-gray-900 dark:text-white no-spinner"
                          :placeholder="getMinSalaryPlaceholder()"
                          min="0"
                        />
                        <span class="text-gray-400">~</span>
                        <input
                          v-model="publishForm.salary_amount_max"
                          type="number"
                          style="width: 190px;"
                          class="px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-md focus:outline-none focus:border-gray-400 bg-white dark:bg-gray-800 text-gray-900 dark:text-white no-spinner"
                          :placeholder="getMaxSalaryPlaceholder()"
                          min="0"
                        />
                        <input
                          v-model="publishForm.salary_currency"
                          type="text"
                          style="width: 60px;"
                          class="px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-md focus:outline-none focus:border-gray-400 bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-center ml-2"
                          placeholder="USD"
                          maxlength="4"
                        />
                      </div>
                    </div>
                  </div>

                  <!-- Location -->
                  <input
                    v-model="publishForm.location"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-md focus:outline-none focus:border-gray-400 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                    placeholder="Location (e.g., San Francisco, CA or Remote)"
                  />

                  <!-- Tags -->
                  <div class="space-y-2">
                    <input
                      v-model="tagInput"
                      type="text"
                      class="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-md focus:outline-none focus:border-gray-400 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                      placeholder="Add tags (press Enter)"
                      @keydown.enter.prevent="addTag"
                    />
                    
                    <div v-if="publishForm.tags.length" class="flex flex-wrap gap-1">
                      <span
                        v-for="tag in publishForm.tags"
                        :key="tag"
                        class="inline-flex items-center gap-1 px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-sm"
                      >
                        {{ tag }}
                        <button
                          type="button"
                          class="hover:text-red-500 flex items-center justify-center w-4 h-4 rounded-full hover:bg-red-100 dark:hover:bg-red-900 transition-colors"
                          @click="removeTag(tag)"
                        >
                          <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                          </svg>
                        </button>
                      </span>
                    </div>
                  </div>

                  <!-- Submit Button -->
                  <div class="flex gap-2 pt-2">
                    <button
                      type="button"
                      class="flex-1 px-4 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md transition-colors"
                      @click="closePublishModal"
                      :disabled="isPublishing"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      class="flex-1 px-4 py-2 bg-black dark:bg-white text-white dark:text-black rounded-md hover:bg-gray-800 dark:hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      :disabled="isPublishing"
                      @click="handlePublish"
                    >
                      {{ isPublishing ? 'Publishing...' : (isEditMode ? 'Update' : 'Publish') }}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { motion } from 'motion-v'
  import {
    getJobBoardPosts,
    getSinglePost,
    delSinglePost,
    createPost,
    editPost,
    collectPost,
    delCollectPost,
  } from '~/api/billboard'
  import type { Talent } from '~/api/types'
  import RefreshBtn from '~/assets/image/refresh-btn.png'
  import { useRoute } from 'vue-router'

  const { currentUser } = useFirebaseAuth()
  const { $emitter, $colorMode } = useNuxtApp()
  const router = useRouter()
  const route = useRoute()
  const isSpinning = ref(true)
  const refreshKey = ref(0)
  const isRefreshing = ref(false)
  const data = ref<any[]>([])
  const offset = ref(0)
  const limit = 6
  const selectedPost = ref<any>(null)
  const isLoadingPost = ref(false)
  const selectedCategory = ref('')
  const isMoreMenuOpen = ref(false)
  const showDeleteConfirm = ref(false)
  const postToDelete = ref<any>(null)
  const showErrorToast = ref(false)
  const errorMessage = ref('')
  const isDeleting = ref(false)
  const showPublishModal = ref(false)
  const isPublishing = ref(false)
  const tagInput = ref('')
  const showSuccessToast = ref(false)
  const successMessage = ref('')
  const isEditMode = ref(false)
  const editingPost = ref<any>(null)
  const isDark = ref(false)
  onMounted(() => {
    isDark.value = document.documentElement.classList.contains('dark')
    const observer = new MutationObserver(() => {
      isDark.value = document.documentElement.classList.contains('dark')
    })
    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] })
  })

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (days === 0) return 'Today'
    if (days === 1) return 'Yesterday'
    if (days < 7) return `${days}d ago`
    if (days < 30) return `${Math.floor(days / 7)}w ago`
    return date.toLocaleDateString()
  }

  const formatTimeAgo = (dateString: string) => {
    if (!dateString) return ''
    
    // 创建日期对象
    let date: Date
    const now = new Date()
    
    // 处理后端返回的时间格式：2025-06-05T16:42:15（无时区信息，实际是UTC时间）
    if (dateString.includes('T') && !dateString.includes('Z') && !dateString.includes('+') && !dateString.includes('-', 10)) {
      // 格式如：2025-06-05T16:42:15，假设是 UTC 时间，添加 Z
      date = new Date(dateString + 'Z')
    } else if (!dateString.includes('T') && !dateString.includes('Z') && !dateString.includes('+')) {
      // 格式如：2025-06-05 16:42:15，假设是 UTC 时间
      date = new Date(dateString + ' UTC')
    } else {
      // 已经包含时区信息，直接使用
      date = new Date(dateString)
    }
    
    const diff = now.getTime() - date.getTime()
    
    // 如果时间差为负数（未来时间），可能是时区问题，调整为 0
    if (diff < 0) {
      return 'Just now'
    }
    
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const weeks = Math.floor(days / 7)
    const months = Math.floor(days / 30)
    
    if (minutes < 1) return 'Just now'
    if (minutes < 60) return `${minutes}m ago`
    if (hours < 24) return `${hours}h ago`
    if (days < 7) return `${days}d ago`
    if (weeks < 4) return `${weeks}w ago`
    if (months < 12) return `${months}mo ago`
    
    const years = Math.floor(months / 12)
    return `${years}y ago`
  }

  const handleCategoryChange = async () => {
    offset.value = 0 // 重置 offset
    await handleFetchJobs()
  }

  const handleRefresh = async () => {
    offset.value = 0 // 重置 offset
    await handleFetchJobs()
  }

  const handleSinglePost = async (postid: string) => {
    try {
      isLoadingPost.value = true
      const result = await getSinglePost(postid)
      if (result.data) {
        selectedPost.value = result.data
      }
    } catch (error) {
      console.error('Error fetching post details:', error)
    } finally {
      isLoadingPost.value = false
    }
  }

  const handleCardClick = async (post: any) => {
    selectedPost.value = post // 先显示基本信息
    await handleSinglePost(post.id) // 然后获取详细信息
  }

  const handleFetchJobs = async () => {
    try {
      isSpinning.value = true
      const res = await getJobBoardPosts(
        {
          limit,
          offset: offset.value,
          post_type: selectedCategory.value || undefined, // 添加 post_type 参数
        },
        currentUser.value?.uid ? {
          Userid: currentUser.value.uid,
        } : {}
      )
      console.log('handleFetchJobs', res)
      if (res.data?.posts) {
        data.value = res.data.posts
        // 更新 offset 为下一页的起始位置
        offset.value += limit
      }
    } catch (error) {
      console.error('Error fetching talents:', error)
    } finally {
      refreshKey.value++
      isSpinning.value = false
    }
  }

  // 页面加载时获取数据，无论是否登录
  onMounted(() => {
    handleFetchJobs()
  })

  // 当用户登录状态改变时，重新获取数据以更新收藏状态等
  watch(
    () => currentUser.value?.uid,
    () => {
      console.log('User authentication changed, refetching data...')
      handleFetchJobs()
    }
  )

  // 点击外部关闭菜单
  const handleClickOutside = (event: MouseEvent) => {
    const target = event.target as HTMLElement
    if (!target.closest('.more-menu')) {
      isMoreMenuOpen.value = false
    }
  }

  onMounted(() => {
    document.addEventListener('click', handleClickOutside)
  })

  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
  })

  // 解析 salary_range 为 salary_type、salary_amount、salary_amount_max 和 salary_currency
  const parseSalaryRange = (salaryRange: string) => {
    if (!salaryRange) return { salary_type: '', salary_amount: '', salary_amount_max: '', salary_currency: 'USD' }
    
    if (salaryRange.toLowerCase() === 'negotiable') {
      return { salary_type: 'negotiable', salary_amount: '', salary_amount_max: '', salary_currency: 'USD' }
    }
    
    // 新格式：匹配范围格式如 "2000~3000 USD/month", "5000~8000 EUR/day"
    const newRangeMatch = salaryRange.match(/(\d+(?:,\d{3})*(?:\.\d{2})?)[~-](\d+(?:,\d{3})*(?:\.\d{2})?) ([A-Z]{3})\/(day|month|year)/i)
    if (newRangeMatch) {
      const minAmount = newRangeMatch[1].replace(/,/g, '')
      const maxAmount = newRangeMatch[2].replace(/,/g, '')
      const currency = newRangeMatch[3].toUpperCase()
      const type = newRangeMatch[4].toLowerCase()
      return { salary_type: type, salary_amount: minAmount, salary_amount_max: maxAmount, salary_currency: currency }
    }
    
    // 新格式：匹配单一格式如 "5000 USD/month", "100 EUR/day"
    const newSingleMatch = salaryRange.match(/(\d+(?:,\d{3})*(?:\.\d{2})?) ([A-Z]{3})\/(day|month|year)/i)
    if (newSingleMatch) {
      const amount = newSingleMatch[1].replace(/,/g, '')
      const currency = newSingleMatch[2].toUpperCase()
      const type = newSingleMatch[3].toLowerCase()
      return { salary_type: type, salary_amount: amount, salary_amount_max: '', salary_currency: currency }
    }
    
    // 旧格式：匹配范围格式如 "$2000~3000/month", "￥5000~8000/day"
    const rangeMatch = salaryRange.match(/([￥$€£¥])(\d+(?:,\d{3})*(?:\.\d{2})?)[~-](\d+(?:,\d{3})*(?:\.\d{2})?)\/(day|month|year)/i)
    if (rangeMatch) {
      const currencySymbol = rangeMatch[1]
      const minAmount = rangeMatch[2].replace(/,/g, '')
      const maxAmount = rangeMatch[3].replace(/,/g, '')
      const type = rangeMatch[4].toLowerCase()
      // 将符号转换为货币代码
      const currencyMap: { [key: string]: string } = { '$': 'USD', '€': 'EUR', '£': 'GBP', '¥': 'JPY', '￥': 'CNY' }
      const currency = currencyMap[currencySymbol] || 'USD'
      return { salary_type: type, salary_amount: minAmount, salary_amount_max: maxAmount, salary_currency: currency }
    }
    
    // 旧格式：匹配单一格式如 "$5000/month", "￥5000/month"
    const singleMatch = salaryRange.match(/([￥$€£¥])(\d+(?:,\d{3})*(?:\.\d{2})?)\/(day|month|year)/i)
    if (singleMatch) {
      const currencySymbol = singleMatch[1]
      const amount = singleMatch[2].replace(/,/g, '')
      const type = singleMatch[3].toLowerCase()
      const currencyMap: { [key: string]: string } = { '$': 'USD', '€': 'EUR', '£': 'GBP', '¥': 'JPY', '￥': 'CNY' }
      const currency = currencyMap[currencySymbol] || 'USD'
      return { salary_type: type, salary_amount: amount, salary_amount_max: '', salary_currency: currency }
    }
    
    // 旧格式：如果只是数字格式如 "$5000", "￥5000"
    const numberMatch = salaryRange.match(/([￥$€£¥])(\d+(?:,\d{3})*(?:\.\d{2})?)/i)
    if (numberMatch) {
      const currencySymbol = numberMatch[1]
      const amount = numberMatch[2].replace(/,/g, '')
      const currencyMap: { [key: string]: string } = { '$': 'USD', '€': 'EUR', '£': 'GBP', '¥': 'JPY', '￥': 'CNY' }
      const currency = currencyMap[currencySymbol] || 'USD'
      return { salary_type: 'yearly', salary_amount: amount, salary_amount_max: '', salary_currency: currency }
    }
    
    return { salary_type: '', salary_amount: '', salary_amount_max: '', salary_currency: 'USD' }
  }

  const handleEdit = (post: any) => {
    isMoreMenuOpen.value = false
    editingPost.value = post
    isEditMode.value = true
    showPublishModal.value = true

    // 解析薪资信息
    const { salary_type, salary_amount, salary_amount_max, salary_currency } = parseSalaryRange(post.salary_range || '')

    // 填充表单数据
    publishForm.value = {
      entity_type: post.entity_type || '',
      post_type: post.post_type,
      title: post.title,
      content: post.content,
      company: post.company || '',
      location: post.location || '',
      position: post.position || '',
      salary_range: post.salary_range || '',
      salary_type: salary_type,
      salary_amount: salary_amount,
      salary_amount_max: salary_amount_max,
      salary_currency: salary_currency,
      tags: post.tags || [],
    }
  }

  const handleDelete = (post: any) => {
    isMoreMenuOpen.value = false
    postToDelete.value = post
    showDeleteConfirm.value = true
  }

  const showError = (message: string) => {
    errorMessage.value = message
    showErrorToast.value = true
    setTimeout(() => {
      showErrorToast.value = false
    }, 3000)
  }

  const showSuccess = (message: string) => {
    successMessage.value = message
    showSuccessToast.value = true
    setTimeout(() => {
      showSuccessToast.value = false
    }, 3000)
  }

  // 关闭发布表单的函数
  const closePublishModal = () => {
    showPublishModal.value = false
    // 无论是编辑模式还是新建模式，都保留用户输入
    // 只重置编辑状态
    isEditMode.value = false
    editingPost.value = null
  }

  // 清空表单数据的函数（只在成功发布后调用）
  const clearPublishForm = () => {
    publishForm.value = {
      entity_type: '',
      post_type: '',
      title: '',
      content: '',
      company: '',
      location: '',
      position: '',
      salary_range: '',
      salary_type: '',
      salary_amount: '',
      salary_amount_max: '',
      salary_currency: 'USD',
      tags: [],
    }
    tagInput.value = ''
  }

  const handlePublishClick = () => {
    // 允许所有用户打开发布表单
    showPublishModal.value = true
  }

  const handleMyBulletinClick = () => {
    // 检查用户是否登录
    if (!currentUser.value) {
      $emitter.emit('auth')
      return
    }
    // 已登录用户可以查看个人发布内容
    router.push('/my-bulletin')
  }

  const confirmDelete = async () => {
    if (!postToDelete.value || isDeleting.value) return

    try {
      isDeleting.value = true
      if (!currentUser.value?.uid) {
        showError('User not authenticated')
        return
      }

      // 调用删除API
      const res = await delSinglePost(postToDelete.value.id, {
        Userid: currentUser.value.uid,
      })

      console.log('delSinglePost res', res)

      if (res?.data?.success || res?.code === 200) {
        // 删除成功，关闭模态框并刷新列表
        selectedPost.value = null
        showDeleteConfirm.value = false
        postToDelete.value = null
        await handleFetchJobs()
        showSuccess('Post deleted successfully')
      } else {
        showError(res?.message || 'Failed to delete post')
      }
    } catch (error) {
      showError('An error occurred while deleting the post')
      console.error('Error deleting post:', error)
    } finally {
      isDeleting.value = false
    }
  }

  const publishForm = ref({
    entity_type: '',
    post_type: '',
    title: '',
    content: '',
    company: '',
    location: '',
    position: '',
    salary_range: '',
    salary_type: '',
    salary_amount: '',
    salary_amount_max: '',
    salary_currency: 'USD',
    tags: [] as string[],
  })

  const addTag = () => {
    const tag = tagInput.value.trim()
    if (tag && !publishForm.value.tags.includes(tag)) {
      // 如果标签不是以 # 开头，则添加 #
      const formattedTag = tag.startsWith('#') ? tag : `#${tag}`
      publishForm.value.tags.push(formattedTag)
    }
    tagInput.value = ''
  }

  const removeTag = (tag: string) => {
    publishForm.value.tags = publishForm.value.tags.filter(t => t !== tag)
  }

  const handlePublish = async () => {
    if (isPublishing.value) return

    try {
      isPublishing.value = true
      if (!currentUser.value?.uid) {
        $emitter.emit('auth')
        return
      }

      // 验证必填字段
      if (!publishForm.value.post_type) {
        showError('Please select a category')
        return
      }
      if (!publishForm.value.entity_type) {
        showError('Please select an entity type')
        return
      }

      // 生成 salary_range
      let salaryRange = ''
      if (publishForm.value.salary_type) {
        if (publishForm.value.salary_type === 'negotiable') {
          salaryRange = 'Negotiable'
        } else if (publishForm.value.salary_amount) {
          const minAmount = publishForm.value.salary_amount
          const maxAmount = publishForm.value.salary_amount_max
          const currency = publishForm.value.salary_currency || 'USD'
          
          // 构建薪资字符串
          let salaryStr = ''
          if (maxAmount && maxAmount !== minAmount) {
            // 有范围：2000~3000 USD/month
            salaryStr = `${minAmount}~${maxAmount} ${currency}`
          } else {
            // 单一金额：3000 USD/month
            salaryStr = `${minAmount} ${currency}`
          }
          
          switch (publishForm.value.salary_type) {
            case 'daily':
              salaryRange = `${salaryStr}/day`
              break
            case 'monthly':
              salaryRange = `${salaryStr}/month`
              break
            case 'yearly':
              salaryRange = `${salaryStr}/year`
              break
            default:
              salaryRange = salaryStr
          }
        }
      }

      // 准备请求数据
      const postData = {
        entity_type: publishForm.value.entity_type,
        post_type: publishForm.value.post_type,
        title: publishForm.value.title,
        content: publishForm.value.content,
        location: publishForm.value.location,
        company: publishForm.value.company,
        position: publishForm.value.position,
        salary_range: salaryRange,
        tags: publishForm.value.tags,
        display_name: currentUser.value.displayName || currentUser.value.email || currentUser.value.uid,
      }

      // 创建请求头
      const headers = {
        Userid: currentUser.value.uid,
      }

      let res
      if (isEditMode.value && editingPost.value) {
        // 编辑模式
        res = await editPost(editingPost.value.id, postData, headers)
        if (res?.data?.success || res?.code === 200) {
          showSuccess('Post updated successfully')
        } else {
          showError(res?.message || 'Failed to update post')
        }
      } else {
        // 创建模式
        res = await createPost(postData, headers)
        if (res?.data?.success || res?.code === 200) {
          showSuccess('Post published successfully')
        } else {
          showError(res?.message || 'Failed to publish post')
        }
      }

      // 发布成功，关闭模态框并清空表单
      showPublishModal.value = false
      clearPublishForm() // 成功发布后清空表单
      // 重置编辑状态
      isEditMode.value = false
      editingPost.value = null

      // 添加延时，确保数据已更新到后端
      setTimeout(async () => {
        // 重置 offset 并刷新列表
        offset.value = 0
        await handleFetchJobs()
      }, 1000)
    } catch (error) {
      showError('An error occurred while saving the post')
      console.error('Error saving post:', error)
    } finally {
      isPublishing.value = false
    }
  }

  const handleCollect = async (post: any) => {
    if (!currentUser.value?.uid) {
      $emitter.emit('auth')
      return
    }

    try {
      const wasCollected = post.is_collected
      let res
      
      if (wasCollected) {
        // 如果已收藏，则取消收藏
        res = await delCollectPost(post.id, {
          Userid: currentUser.value.uid,
        })
        console.log('cancel post', res)
      } else {
        // 如果未收藏，则添加收藏
        res = await collectPost(
          post.id,
          { notes: '这个职位适合自己' },
          {
            Userid: currentUser.value.uid,
          }
        )
      }

      if (res?.data?.success || res?.code === 200) {
        // 更新帖子的收藏状态
        post.is_collected = !post.is_collected
        // 直接更新收藏数
        post.bookmark_count = (post.bookmark_count || 0) + (post.is_collected ? 1 : -1)
        showSuccess(post.is_collected ? 'Post collected' : 'Post uncollected')
      } else {
        showError(res?.message || `Failed to ${wasCollected ? 'uncollect' : 'collect'} post`)
      }
    } catch (error) {
      console.error('Error handling collect action:', error)
      const wasCollected = post.is_collected
      showError(
        `An error occurred while ${wasCollected ? 'uncollecting' : 'collecting'} the post`
      )
    }
  }

  const autoResizeTextarea = (event: Event) => {
    const textarea = event.target as HTMLTextAreaElement
    // 重置高度，以便正确计算新高度
    textarea.style.height = 'auto'
    // 设置新高度
    textarea.style.height = `${textarea.scrollHeight}px`
  }

  const autoResizeTitle = (event: Event) => {
    const textarea = event.target as HTMLTextAreaElement
    // 重置高度，以便正确计算新高度
    textarea.style.height = 'auto'
    // 设置新高度，但限制最大高度
    const newHeight = Math.min(textarea.scrollHeight, 120) // 最大约3行
    textarea.style.height = `${newHeight}px`
  }
  import { ref, computed } from 'vue'

  const textareaRef = ref<HTMLTextAreaElement | null>(null)
  const inputValue = ref('')
  const isFocused = ref(false)

  // 控制显示逻辑
  const showFakePlaceholder = computed(() => {
    return !publishForm.value.content && !isFocused.value
  })

  // 输入处理
  const handleInput = (e: Event) => {
    const target = e.target as HTMLInputElement
    publishForm.value.content = target.value
  }

  // 失焦处理
  const handleBlur = () => {
    isFocused.value = false
  }

  // 点击 placeholder 聚焦输入框
  const handlePlaceholderClick = () => {
    textareaRef.value?.focus()
  }

  // 获取最低薪资输入框的placeholder
  const getMinSalaryPlaceholder = () => {
    switch (publishForm.value.salary_type) {
      case 'daily':
        return 'Min daily salary'
      case 'monthly':
        return 'Min monthly salary'
      case 'yearly':
        return 'Min yearly salary'
      default:
        return 'Min amount'
    }
  }

  // 获取最高薪资输入框的placeholder
  const getMaxSalaryPlaceholder = () => {
    switch (publishForm.value.salary_type) {
      case 'daily':
        return 'Max daily salary'
      case 'monthly':
        return 'Max monthly salary'
      case 'yearly':
        return 'Max yearly salary'
      default:
        return 'Max amount'
    }
  }

  // 提取文本中的链接
  function extractLinks(text: string) {
    if (!text) return { emails: [], urls: [] }

    // 邮箱正则
    const emailRegex = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g
    // URL 正则（支持 http(s) 和 www 开头）
    const urlRegex = /((https?:\/\/|www\.)[^\s]+)/g

    const emails = [...text.matchAll(emailRegex)].map(match => match[1])
    const urls = [...text.matchAll(urlRegex)].map(match => {
      const url = match[1]
      return url.startsWith('http') ? url : 'http://' + url
    })

    return { emails, urls }
  }

  // 生成短链接显示文本
  function generateShortLink(url: string, maxLength: number = 30) {
    try {
      const urlObj = new URL(url.startsWith('http') ? url : 'http://' + url)
      const domain = urlObj.hostname.replace('www.', '')
      const path = urlObj.pathname + urlObj.search + urlObj.hash
      
      if (domain.length + path.length <= maxLength) {
        return domain + path
      }
      
      const availablePathLength = maxLength - domain.length - 3 // 3 for "..."
      if (availablePathLength > 0) {
        return domain + path.substring(0, availablePathLength) + '...'
      }
      
      return domain.length <= maxLength ? domain : domain.substring(0, maxLength - 3) + '...'
    } catch {
      return url.length <= maxLength ? url : url.substring(0, maxLength - 3) + '...'
    }
  }

  // 处理文本的方法：匹配邮箱和网址并返回 HTML 链接（显示为短链接）
  function linkify(text: string) {
    if (!text) return ''

    // 邮箱正则
    const emailRegex = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g

    // URL 正则（支持 http(s) 和 www 开头）
    const urlRegex = /((https?:\/\/|www\.)[^\s]+)/g

    return (
      text
        // 邮箱链接
        .replace(
          emailRegex,
          '<a href="mailto:$1" target="_blank" class="text-blue-600 dark:text-blue-400 underline">$1</a>'
        )
        // URL 链接 - 显示为短链接
        .replace(urlRegex, match => {
          const url = match.startsWith('http') ? match : 'http://' + match
          const shortLink = generateShortLink(match)
          return `<a href="${url}" target="_blank" class="text-blue-600 dark:text-blue-400 underline" title="${url}">${shortLink}</a>`
        })
    )
  }

  // 格式化数字，超过1K时显示为K单位，超过1M时显示为M单位
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(0) + 'K'
    }
    return num.toString()
  }

  // 格式化薪资显示，将 - 替换为 ~ 并应用数字格式化
  function formatSalaryDisplay(salaryRange: string) {
    if (!salaryRange) return ''
    
    // 先替换分隔符
    let formatted = salaryRange.replace(/-/g, '~')
    
    // 匹配薪资数字并格式化
    // 匹配格式如: "50000~80000 USD/month", "$50000~80000/month", "50000 USD/month"
    formatted = formatted.replace(/(\d+(?:,\d{3})*)/g, (match) => {
      const num = parseInt(match.replace(/,/g, ''))
      return formatNumber(num)
    })
    
    return formatted
  }
</script>

<style scoped>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-5 {
    display: -webkit-box;
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  /* 隐藏数字输入框的spinner */
  .no-spinner::-webkit-outer-spin-button,
  .no-spinner::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  .no-spinner[type=number] {
    -moz-appearance: textfield;
  }

  .animate-spin {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .whitespace-pre-wrap {
    white-space: pre-wrap;
  }

  select {
    background-image: none;
  }

  select::-ms-expand {
    display: none;
  }

  .more-menu {
    position: relative;
    z-index: 20;
  }

  @keyframes fade-in {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in {
    animation: fade-in 0.3s ease-out;
  }

  .textarea-wrapper {
    position: relative;
    width: 300px;
  }

  textarea {
    width: 100%;
    padding: 12px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 4px;
    resize: vertical;
  }
  .dark textarea {
    background-color: #1f2937;
    border: 1px solid #4b5563 !important;
    color: #ffffff;
  }

  .fake-placeholder {
    position: absolute;
    top: 12px; /* 调整到textarea内部的padding位置 */
    left: 18px;
    color: #9CA3AF; /* 使用与浏览器默认placeholder相似的颜色 */
    font-size: 26px; /* 与第一个输入框字体大小一致 */
    font-style: normal;
    font-weight: 400;
    line-height: 28px; /* 175% */
    pointer-events: none; /* 允许穿透点击 */
    user-select: none; /* 禁止文字选择 */
    z-index: 1; /* 确保在textarea之上 */

    /* 对齐 textarea 的行高 */
    line-height: 1.5;
  }

  select.placeholder {
    color: #a2a9b4;
  }

  select:not(.placeholder) {
    color: #000;
  }

  /* 深色模式下选中的值 */
  .dark select:not(.placeholder) {
    color: #fff;
  }

  /* 选择框选项的颜色 */
  select option {
    color: #a2a9b4;
  }

  /* 深色模式下选择框选项的颜色 */
  .dark select option {
    color: #6B7280;
  }

  .large-text {
    font-size: 18px;
    font-weight: 500;
    margin-right: 8px;
    margin-bottom: 5px;
  }

  .small-text {
    display: block;
    font-size: 12px;
    opacity: 0.8;
    margin-top: 5px;
    margin-bottom: 5px;
  }

  .discover-btn {
    background-image: url('~/assets/image/discover.png');
    background-size: 100%;
    width: 46px !important;
    height: 46px !important;
  }

  .dark .discover-btn {
    background-image: url('~/assets/image/discoverdark.png');
    background-size: 100%;
    width: 40px !important;
    height: 40px !important;
  }

  .mybulletin-btn {
    background-image: url('~/assets/image/mybulletin.png');
    background-size: 100%;
    width: 40px !important;
    height: 40px !important;
  }

  .dark .mybulletin-btn {
    background-image: url('~/assets/image/mybulletindark.png');
    background-size: 100%;
  }

  /* 底层纯白色背景 */
  .bg-white-base {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: #FFFFFF;
    z-index: 5;
    pointer-events: none;
  }

  /* 渐变背景层 */
  .bg-gradient {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(180deg, #FFFFFF 0%, #E9E1DE 26.57%);
    z-index: 8;
    pointer-events: none;
  }

  /* 背景图片定位 */
  .bg-grid {
    position: fixed;
    top: -100px;
    left: 0;
    width: 100vw;
    height: calc(100vh + 100px);
    background-image: url('/image/bgGrid.png');
    background-size: 100% auto;
    background-position: 0 0;
    background-repeat: repeat-x;
    z-index: 15;
    pointer-events: none;
  }

  /* 自定义背景覆盖层 */
  .custom-bg-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(233, 225, 222, 0) 26.57%);
    z-index: 10;
    pointer-events: none;
  }



  /* 装饰性背景元素容器 */
  .decorative-bg-container {
    position: absolute;
    top: 0;
    left: -60px;
    width: 100%;
    height: 100%;
    z-index: 16;
    pointer-events: none;
  }

  /* 装饰性背景元素 */
  .bg-square {
    position: absolute;
    top: 120px;
    left: 378.55px;
    width: 1091.4541015625px;
    height: 226px;
    background-image: url('/image/bgsquare.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 2;
    pointer-events: none;
  }

  .bg-vector1 {
    position: absolute;
    top: 140px;
    left: 325px;
    width: 534px;
    height: 398px;
    background-image: url('/image/bgVector1.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.4;
    z-index: 1;
    pointer-events: none;
  }

  .bg-vector2 {
    position: absolute;
    top: 52px;
    left: 926px;
    width: 534px;
    height: 398px;
    background-image: url('/image/bgVector2.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.4;
    z-index: 1;
    pointer-events: none;
  }

  /* 深色模式背景图片 - 使用深色版本的square图片 */
  .dark .bg-square {
    background-image: url('/image/bgsquare-dark.png');
  }

  /* 深色模式隐藏网格背景 */
  .dark .bg-grid {
    display: none;
  }

  /* 深色模式下bgvector的透明度 */
  .dark .bg-vector1,
  .dark .bg-vector2 {
    opacity: 0.03;
  }

  /* 确保主内容在背景之上 */
  .min-h-screen {
    z-index: 20;
  }

  /* 侧边栏样式 */
  .sidebar-menu {
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.6) 0%, rgba(255, 255, 255, 0) 100%);
    border: none;
    backdrop-filter: blur(34px);
  }

  /* 深色模式侧边栏样式 */
  .dark .sidebar-menu {
    background: linear-gradient(180deg, rgba(48, 50, 54, 0.5) 0%, rgba(48, 50, 54, 0) 100%);
    border: none;
    backdrop-filter: blur(34px);
  }

  /* 深色模式卡片背景 */
  .dark .bg-white {
    background-color: #2A2C30 !important;
  }

  /* 深色模式第一个标签背景（post_type标签） */
  .dark .bg-primary-50\/20 {
    background-color: #54565A !important;
    color: #FFFFFF !important;
  }

  /* 深色模式下的假placeholder颜色 */
  .dark .fake-placeholder {
    color: #6B7280; /* 深色模式下的placeholder颜色 */
  }

  /* 选中按钮样式 */
  .selected-button {
    width: 210px !important;
    height: 62px !important;
    border-radius: 10px !important;
    transform: translateX(10px);
    background: linear-gradient(90deg, #FFEBE3 0%, rgba(255, 255, 255, 0) 100%) !important;
  }

  /* 确保按钮内容大小不受影响 */
  .selected-button .discover-btn {
    width: 46px !important;
    height: 46px !important;
  }

  .dark .selected-button .discover-btn {
    width: 40px !important;
    height: 40px !important;
  }

  .selected-button .mybulletin-btn {
    width: 40px !important;
    height: 40px !important;
  }

  .selected-button span {
    font-size: 14px !important;
  }

  /* 深色模式选中按钮样式 */
  .dark .selected-button {
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 100%) !important;
  }

  /* Enhanced form styles */
  .publish-form-input:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(203, 124, 93, 0.15);
  }

  .publish-form-select:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(203, 124, 93, 0.15);
  }

  /* Custom scrollbar for modal */
  .modal-scroll::-webkit-scrollbar {
    width: 6px;
  }

  .modal-scroll::-webkit-scrollbar-track {
    background: transparent;
  }

  .modal-scroll::-webkit-scrollbar-thumb {
    background: rgba(203, 124, 93, 0.3);
    border-radius: 3px;
  }

  .modal-scroll::-webkit-scrollbar-thumb:hover {
    background: rgba(203, 124, 93, 0.5);
  }

  /* Dark mode scrollbar */
  .dark .modal-scroll::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
  }

  .dark .modal-scroll::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.7);
  }

  /* Tag animation */
  .tag-enter-active {
    transition: all 0.3s ease;
  }

  .tag-enter-from {
    opacity: 0;
    transform: scale(0.8);
  }

  /* Button hover effects */
  .publish-button {
    background: linear-gradient(135deg, #CB7C5D 0%, #B86A4F 100%);
    box-shadow: 0 4px 15px rgba(203, 124, 93, 0.3);
  }

  .publish-button:hover {
    background: linear-gradient(135deg, #B86A4F 0%, #A55A41 100%);
    box-shadow: 0 6px 20px rgba(203, 124, 93, 0.4);
  }

  /* Form section spacing */
  .form-section {
    padding: 1.5rem;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.5);
    border: 1px solid rgba(203, 124, 93, 0.1);
  }

  .dark .form-section {
    background: rgba(42, 44, 48, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* 确保深色模式下所有输入框都有边框 */
  .dark input,
  .dark select,
  .dark textarea {
    border: 1px solid #4b5563 !important;
    background-color: #1f2937 !important;
    color: #ffffff !important;
  }

  .dark input:focus,
  .dark select:focus,
  .dark textarea:focus {
    border-color: #6b7280 !important;
  }

  /* 覆盖publish表单中textarea的背景色 */
  .dark .bg-gray-50 {
    background-color: #2d2f33 !important;
  }

  /* 货币选择器分段控制器样式 */
  .currency-selector {
    display: flex;
    background-color: #f3f4f6;
    border-radius: 8px;
    padding: 2px;
    position: relative;
    overflow: hidden;
  }

  .dark .currency-selector {
    background-color: #54565A;
  }

  .currency-radio {
    display: none;
  }

  .currency-option {
    flex: 1;
    text-align: center;
    padding: 8px 12px;
    font-size: 14px;
    font-weight: 500;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 6px;
    position: relative;
    z-index: 2;
  }

  .dark .currency-option {
    color: #6b7280;
  }

  .currency-radio:checked + .currency-option {
    background-color: #ffffff;
    color: #1f2937;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .dark .currency-radio:checked + .currency-option {
    background-color: #ffffff;
    color: #1f2937;
    box-shadow: none;
  }

  .currency-option:hover {
    color: #374151;
  }

  .dark .currency-option:hover {
    color: #9ca3af;
  }

  /* 自定义0.5px边框 */
  .border-0\.5 {
    border-width: 0.5px;
  }
</style>

<style>
/* 全局样式 - 深色模式背景覆盖层 */
.dark .custom-bg-overlay {
  background: linear-gradient(180deg, rgba(15, 15, 15, 0) 0%, rgba(26, 26, 26, 0) 26.57%) !important;
}

/* 深色模式底层背景 */
.dark .bg-white-base {
  background-color: #0F0F0F !important;
}

/* 深色模式渐变背景 */
.dark .bg-gradient {
  background: linear-gradient(180deg, #0F0F0F 0%, #1A1A1A 26.57%) !important;
}
</style>
