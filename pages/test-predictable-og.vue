<template>
  <div class="container mx-auto p-8">
    <h1 class="text-3xl font-bold mb-6">可预测OG图片URL测试</h1>
    
    <div class="space-y-4">
      <div>
        <label class="block text-sm font-medium mb-2">GitHub用户名:</label>
        <input 
          v-model="testUsername" 
          type="text" 
          placeholder="输入GitHub用户名" 
          class="border rounded px-3 py-2 w-64"
        />
        <button 
          @click="testPredictableUrl" 
          class="ml-2 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          生成可预测URL
        </button>
      </div>

      <div v-if="predictableUrl" class="mt-6">
        <h2 class="text-xl font-semibold mb-2">可预测的OG图片URL:</h2>
        <div class="bg-gray-100 p-4 rounded">
          <p class="font-mono text-sm break-all">{{ predictableUrl }}</p>
          <div class="mt-2">
            <a :href="predictableUrl" target="_blank" class="text-blue-500 underline mr-4">
              在新窗口中打开
            </a>
            <button 
              @click="checkImageExists" 
              :disabled="isChecking"
              class="bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600 disabled:opacity-50"
            >
              {{ isChecking ? '检查中...' : '检查图片是否存在' }}
            </button>
          </div>
        </div>
      </div>

      <div v-if="imageStatus !== null" class="mt-4">
        <div :class="imageStatus ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'" class="p-3 rounded">
          {{ imageStatus ? '✅ 图片存在' : '❌ 图片不存在' }}
        </div>
      </div>

      <div v-if="predictableUrl && imageStatus" class="mt-6">
        <h2 class="text-xl font-semibold mb-2">图片预览:</h2>
        <img :src="predictableUrl" alt="Predictable OG Image" class="border rounded max-w-md" />
      </div>

      <div class="mt-8 bg-blue-50 p-4 rounded">
        <h2 class="text-xl font-semibold mb-2">工作原理:</h2>
        <ol class="list-decimal list-inside space-y-1 text-sm">
          <li>输入GitHub用户名（如：octocat）</li>
          <li>生成固定格式的文件名：<code>github-{username}-latest.png</code></li>
          <li>构建可预测的S3 URL：<code>https://dinq-share-og.s3.us-east-2.amazonaws.com/shares/{filename}</code></li>
          <li>这个URL可以在服务端渲染时直接使用，无需等待图片生成</li>
          <li>如果图片不存在，S3会返回404，但社交爬虫会在图片生成后重新抓取</li>
        </ol>
      </div>

      <div class="mt-6 bg-yellow-50 p-4 rounded">
        <h2 class="text-xl font-semibold mb-2">测试步骤:</h2>
        <ol class="list-decimal list-inside space-y-1 text-sm">
          <li>输入一个GitHub用户名</li>
          <li>点击"生成可预测URL"</li>
          <li>检查图片是否存在</li>
          <li>如果不存在，去GitHub分析页面生成图片</li>
          <li>回来再次检查，应该能看到图片</li>
        </ol>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getPredictableOgImageUrl, checkOgImageExists } from '~/utils'

const testUsername = ref('octocat')
const predictableUrl = ref('')
const imageStatus = ref<boolean | null>(null)
const isChecking = ref(false)

const testPredictableUrl = () => {
  if (!testUsername.value.trim()) {
    alert('请输入GitHub用户名')
    return
  }

  predictableUrl.value = getPredictableOgImageUrl(testUsername.value.trim())
  imageStatus.value = null
}

const checkImageExists = async () => {
  if (!predictableUrl.value) return

  isChecking.value = true
  try {
    imageStatus.value = await checkOgImageExists(testUsername.value.trim())
  } catch (error) {
    console.error('检查图片失败:', error)
    imageStatus.value = false
  } finally {
    isChecking.value = false
  }
}

// 设置页面meta
useSeoMeta({
  title: '可预测OG图片URL测试 - DINQ',
  description: '测试可预测的OG图片URL生成功能',
})
</script>
