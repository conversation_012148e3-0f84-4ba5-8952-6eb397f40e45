<template>
  <div class="landing-page">
    <!-- 第一页：Hero Section -->
    <section id="hero" class="landing-section hero-section">
      <div class="section-container">
        <div class="hero-content">
          <!-- 左侧：大 Slogan -->
          <div class="hero-left">
            <h1 class="hero-title">
              <!-- 预留大标题位置 -->
              A<br />
              TALENT<br />
              FINDER<span class="highlight">.</span>
            </h1>
          </div>
          
          <!-- 右侧：副标题和按钮 -->
          <div class="hero-right">
            <div class="hero-right-content">
              <p class="hero-subtitle">
                <!-- 预留副标题位置 -->
                Talent finder that knows you best. Dinq agents automatically discover, analyze, and acquire top AI talent, serving AI startups, large corporations, and Fortune 500 companies.
              </p>
              <NuxtLink to="/demo" class="demo-cta-btn">
                Request a Demo
              </NuxtLink>
            </div>
          </div>
        </div>
        
        <!-- 底部：Logo 跑马灯 -->
        <div class="sponsors-section">
          <LogoMarquee />
        </div>
      </div>
    </section>

    <!-- 第二页：主要功能展示 -->
    <section id="features" class="landing-section features-section">
      <div class="section-container">
        <div class="features-layout">
          <!-- 上半部分：标题和打字机效果区域 -->
          <div class="features-header-area">
            <motion.div
              :initial="{ opacity: 0, y: 30 }"
              :whileInView="{ opacity: 1, y: 0 }"
              :transition="{ duration: 0.8, ease: 'easeOut' }"
              :viewport="{ once: true }"
              class="analysis-title"
            >
              Talent Analysis
            </motion.div>
            <motion.div
              :initial="{ opacity: 0, y: 20 }"
              :whileInView="{ opacity: 1, y: 0 }"
              :transition="{ duration: 0.8, ease: 'easeOut', delay: 0.2 }"
              :viewport="{ once: true }"
              class="typed-text-container"
            >
              <TypedText
                :strings="[
                  'Know Your AI Peers',
                  'LLM',
                  'Text-to-Image',
                  'Text-to-Video',
                  'Text-to-3D',
                  'RAG',
                  'Reinforcement Learning',
                  'Vision Language Model',
                  'Security & Safety',
                  'Agent',
                  'Information Retrieval',
                ]"
                :typeSpeed="100"
                :backSpeed="30"
                :loop="true"
              />
            </motion.div>
          </div>
          
          <!-- 中间部分：交互控制区域 -->
          <div class="features-controls-area">
            <motion.div
              :initial="{ opacity: 0, y: 30 }"
              :whileInView="{ opacity: 1, y: 0 }"
              :transition="{ duration: 0.8, ease: 'easeOut', delay: 0.4 }"
              :viewport="{ once: true }"
              class="toggle-wrapper"
            >
              <div class="homepage-toggle-container">
                <!-- 背景容器 -->
                <div class="homepage-toggle-background">
                  <!-- 滑动指示器 -->
                  <div
                    class="homepage-toggle-slider"
                    :class="{ 'slide-to-github': selected === 'github' }"
                  ></div>

                  <!-- 选项按钮 -->
                  <div
                    class="homepage-toggle-option"
                    :class="{ 'active': selected === 'scholar' }"
                    @click="selectOption('scholar')"
                  >
                    Scholar
                  </div>
                  <div
                    class="homepage-toggle-option"
                    :class="{ 'active': selected === 'github' }"
                    @click="selectOption('github')"
                  >
                    Github
                  </div>
                </div>
              </div>
            </motion.div>
            
            <motion.div
              :initial="{ opacity: 0, y: 20 }"
              :whileInView="{ opacity: 1, y: 0 }"
              :transition="{ duration: 0.8, ease: 'easeOut', delay: 0.6 }"
              :viewport="{ once: true }"
              class="search-wrapper"
            >
              <SearchBar :type="selected" />
            </motion.div>
          </div>
          
          <!-- 下半部分：产品截图展示区域 -->
          <motion.div
            :initial="{ opacity: 0, y: 40 }"
            :whileInView="{ opacity: 1, y: 0 }"
            :transition="{ duration: 1, ease: 'easeOut', delay: 0.8 }"
            :viewport="{ once: true }"
            class="features-showcase-area"
          >
            <div class="screenshot-container">
              <motion.img
                :initial="{ opacity: 0, x: -30 }"
                :whileInView="{ opacity: 1, x: 0 }"
                :transition="{ duration: 0.8, ease: 'easeOut', delay: 1.0 }"
                :viewport="{ once: true }"
                src="/image/homeshotl.png"
                alt="Left screenshot"
                class="screenshot-left"
              />
              <motion.img
                :initial="{ opacity: 0, y: 30 }"
                :whileInView="{ opacity: 1, y: 0 }"
                :transition="{ duration: 0.8, ease: 'easeOut', delay: 1.1 }"
                :viewport="{ once: true }"
                src="/image/homeshotm.png"
                alt="Middle screenshot"
                class="screenshot-middle"
              />
              <motion.img
                :initial="{ opacity: 0, x: 30 }"
                :whileInView="{ opacity: 1, x: 0 }"
                :transition="{ duration: 0.8, ease: 'easeOut', delay: 1.2 }"
                :viewport="{ once: true }"
                src="/image/homeshotr.png"
                alt="Right screenshot"
                class="screenshot-right"
              />
            </div>
          </motion.div>
        </div>
      </div>
    </section>

    <!-- 第三页：功能介绍 1 -->
    <section id="feature-1" class="landing-section feature-detail-section">
      <div class="section-container">
        <motion.div
          :initial="{ opacity: 0, y: 50 }"
          :whileInView="{ opacity: 1, y: 0 }"
          :transition="{ duration: 0.8, ease: 'easeOut' }"
          :viewport="{ once: true, margin: '-100px' }"
          class="feature-detail-content"
        >
          <!-- 左侧：标题和介绍 -->
          <motion.div
            :initial="{ opacity: 0, x: -50 }"
            :whileInView="{ opacity: 1, x: 0 }"
            :transition="{ duration: 0.8, ease: 'easeOut', delay: 0.2 }"
            :viewport="{ once: true }"
            class="feature-left"
          >
            <h2 class="feature-title feature-3-title">
              Unlock Top AI Talent, Globally:
            </h2>
            <p class="feature-description feature-3-description">
              DINQ's AI-powered agent rapidly and accurately identifies elite AI talent from premier global sources.
            </p>
            <NuxtLink to="/talent-pool" class="feature-cta-btn feature-3-btn">
              Get Started
              <span class="arrow-right">›</span>
            </NuxtLink>
          </motion.div>
          
          <!-- 右侧：动画/视频 -->
          <motion.div
            :initial="{ opacity: 0, x: 50 }"
            :whileInView="{ opacity: 1, x: 0 }"
            :transition="{ duration: 0.8, ease: 'easeOut', delay: 0.4 }"
            :viewport="{ once: true }"
            class="feature-right"
          >
            <div class="feature-media">
              <img src="/image/p3.gif" alt="AI Talent Discovery Animation" class="feature-gif" />
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>

    <!-- 第四页：功能介绍 2 -->
    <section id="feature-2" class="landing-section feature-detail-section">
      <div class="section-container">
        <motion.div
          :initial="{ opacity: 0, y: 50 }"
          :whileInView="{ opacity: 1, y: 0 }"
          :transition="{ duration: 0.8, ease: 'easeOut' }"
          :viewport="{ once: true, margin: '-100px' }"
          class="feature-detail-content"
        >
          <!-- 左侧：动画/视频 -->
          <motion.div
            :initial="{ opacity: 0, x: -50 }"
            :whileInView="{ opacity: 1, x: 0 }"
            :transition="{ duration: 0.8, ease: 'easeOut', delay: 0.2 }"
            :viewport="{ once: true }"
            class="feature-right"
          >
            <div class="feature-media">
              <img src="/image/p4.gif" alt="Deep Candidate Intelligence Animation" class="feature-gif feature-gif-left" />
            </div>
          </motion.div>
          
          <!-- 右侧：标题和介绍 -->
          <motion.div
            :initial="{ opacity: 0, x: 50 }"
            :whileInView="{ opacity: 1, x: 0 }"
            :transition="{ duration: 0.8, ease: 'easeOut', delay: 0.4 }"
            :viewport="{ once: true }"
            class="feature-left feature-left-right"
          >
            <h2 class="feature-title feature-3-title">
              Deep Candidate Intelligence:
            </h2>
            <p class="feature-description feature-3-description">
              DINQ enables a profound understanding of candidates by facilitating detailed analysis at any specified depth, revealing critical attributes and potential.
            </p>
            <button class="feature-cta-btn feature-3-btn">
              Coming Soon
              <span class="arrow-right">›</span>
            </button>
          </motion.div>
        </motion.div>
      </div>
    </section>

    <!-- 第五页：功能介绍 3 -->
    <section id="feature-3" class="landing-section feature-detail-section">
      <div class="section-container">
        <motion.div
          :initial="{ opacity: 0, y: 50 }"
          :whileInView="{ opacity: 1, y: 0 }"
          :transition="{ duration: 0.8, ease: 'easeOut' }"
          :viewport="{ once: true, margin: '-100px' }"
          class="feature-detail-content"
        >
          <!-- 左侧：标题和介绍 -->
          <motion.div
            :initial="{ opacity: 0, x: -50 }"
            :whileInView="{ opacity: 1, x: 0 }"
            :transition="{ duration: 0.8, ease: 'easeOut', delay: 0.2 }"
            :viewport="{ once: true }"
            class="feature-left"
          >
            <h2 class="feature-title feature-3-title">
              DINQ-as-a-Service (DaaS):
            </h2>
            <p class="feature-description feature-3-description">
              Whether you're using an off-the-shelf ATS, run a boutique recruitment firm, or are a large enterprise, DINQ's service model integrates seamlessly to boost your qualified candidate pipeline, reduce time-to-hire, and maximize your recruitment ROI.
            </p>
            <NuxtLink to="/analysis" class="feature-cta-btn feature-3-btn">
              Get Started
              <span class="arrow-right">›</span>
            </NuxtLink>
          </motion.div>
          
          <!-- 右侧：动画/视频 -->
          <motion.div
            :initial="{ opacity: 0, x: 50 }"
            :whileInView="{ opacity: 1, x: 0 }"
            :transition="{ duration: 0.8, ease: 'easeOut', delay: 0.4 }"
            :viewport="{ once: true }"
            class="feature-right"
          >
            <div class="feature-media">
              <img src="/image/p5.gif" alt="DINQ-as-a-Service Animation" class="feature-gif" />
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>

    <!-- 第六页：More targeted, less clutter -->
    <section id="more-targeted" class="landing-section more-targeted-section">
      <div class="section-container">
        <motion.div
          :initial="{ opacity: 0, y: 50 }"
          :whileInView="{ opacity: 1, y: 0 }"
          :transition="{ duration: 0.8, ease: 'easeOut' }"
          :viewport="{ once: true, margin: '-100px' }"
          class="more-targeted-content"
        >
          <!-- 标题文字 -->
          <motion.h2
            :initial="{ opacity: 0, y: 30 }"
            :whileInView="{ opacity: 1, y: 0 }"
            :transition="{ duration: 0.8, ease: 'easeOut', delay: 0.2 }"
            :viewport="{ once: true }"
            class="more-targeted-title"
          >
            More targeted, less clutter.
          </motion.h2>
          
          <!-- 图片区域 -->
          <motion.div
            :initial="{ opacity: 0, y: 40 }"
            :whileInView="{ opacity: 1, y: 0 }"
            :transition="{ duration: 0.8, ease: 'easeOut', delay: 0.4 }"
            :viewport="{ once: true }"
            class="more-targeted-images"
          >
            <!-- 桌面端和平板端显示单个home-icons图片 -->
            <div class="drawing-container desktop-tablet-icons">
              <img src="/image/home-icons.png" alt="Home Icons" class="home-icons-img" />
              
              <!-- 画线动画 -->
              <motion.svg 
                class="drawing-line" 
                viewBox="0 0 940 2" 
                xmlns="http://www.w3.org/2000/svg" 
                preserveAspectRatio="none"
                :initial="{ clipPath: 'inset(0 200% 0 0)' }"
                :whileInView="{ clipPath: 'inset(0 0% 0 0)' }"
                :transition="{ 
                  duration: isMobile ? 2.5 : 2.7, 
                  ease: 'linear', 
                  delay: -0.7
                }"
                :viewport="{ once: true }"
              >
                <path 
                  d="M469.54 1.75683C315.015 1.06138 149.596 3.10526 0 1.03483C228.46 -1.21609 479.78 1.02422 716.36 0.254446C786.207 0.254446 856.237 0.254446 926.284 0.254446C932.286 0.286299 941.753 0.0633326 940 1.0136C792.671 3.15304 624.527 1.03484 469.54 1.75683Z"
                  fill="#676767"
                  stroke="none"
                />
              </motion.svg>
            </div>
            
            <!-- 移动端显示六个logo的两行布局 -->
            <div class="mobile-logos-container">
              <div class="logos-grid">
                <!-- 第一行 -->
                <div class="logos-row">
                  <img src="/image/llogo1.png" alt="Logo 1" class="mobile-logo" />
                  <img src="/image/llogo2.png" alt="Logo 2" class="mobile-logo" />
                  <img src="/image/llogo3.png" alt="Logo 3" class="mobile-logo" />
                  
                  <!-- 第一行的划线动画 -->
                  <motion.svg 
                    class="row-drawing-line first-row-line" 
                    viewBox="0 0 280 2" 
                    xmlns="http://www.w3.org/2000/svg" 
                    preserveAspectRatio="none"
                    :initial="{ clipPath: 'inset(0 200% 0 0)' }"
                    :whileInView="{ clipPath: 'inset(0 0% 0 0)' }"
                    :transition="{ 
                      duration: 2.0, 
                      ease: 'linear', 
                      delay: -0.5
                    }"
                    :viewport="{ once: true }"
                  >
                    <path 
                      d="M0 1L280 1"
                      stroke="rgba(103, 103, 103, 0.6)"
                      stroke-width="2"
                      fill="none"
                    />
                  </motion.svg>
                </div>
                
                <!-- 第二行 -->
                <div class="logos-row">
                  <img src="/image/llogo4.png" alt="Logo 4" class="mobile-logo" />
                  <img src="/image/llogo5.png" alt="Logo 5" class="mobile-logo" />
                  <img src="/image/llogo6.png" alt="Logo 6" class="mobile-logo" />
                  
                  <!-- 第二行的划线动画 -->
                  <motion.svg 
                    class="row-drawing-line second-row-line" 
                    viewBox="0 0 280 2" 
                    xmlns="http://www.w3.org/2000/svg" 
                    preserveAspectRatio="none"
                    :initial="{ clipPath: 'inset(0 200% 0 0)' }"
                    :whileInView="{ clipPath: 'inset(0 0% 0 0)' }"
                    :transition="{ 
                      duration: 2.0, 
                      ease: 'linear', 
                      delay: 0.3
                    }"
                    :viewport="{ once: true }"
                  >
                    <path 
                      d="M0 1L280 1"
                      stroke="rgba(103, 103, 103, 0.6)"
                      stroke-width="2"
                      fill="none"
                    />
                  </motion.svg>
                </div>
              </div>
            </div>
            
            <!-- 铅笔动画 - 移到drawing-container外面 -->
            <motion.img
              :initial="{ x: 0, y: -40, opacity: 0 }"
              :whileInView="{ x: 990, y: -40, opacity: 1 }"
              :transition="{ 
                duration: 1.2, 
                ease: 'easeInOut', 
                delay: 0.8,
                x: { duration: 1.2, ease: 'linear' }
              }"
              :viewport="{ once: true }"
              src="/image/pencil.png" 
              alt="Pencil" 
              class="pencil-img animated-pencil-outside desktop-pencil" 
            />
            
            <!-- 平板端铅笔 -->
            <motion.img
              :initial="{ x: 0, y: -35, opacity: 0 }"
              :whileInView="{ x: 990, y: -35, opacity: 1 }"
              :transition="{ 
                duration: isMobile ? 1 : 1.2, 
                ease: 'easeInOut', 
                delay: 0.8,
                x: { duration: isMobile ? 1 : 1.2, ease: 'linear' }
              }"
              :viewport="{ once: true }"
              src="/image/pencil.png" 
              alt="Pencil" 
              class="pencil-img animated-pencil-outside tablet-pencil" 
            />
            
            <!-- 移动端铅笔 -->
            <motion.img
              :initial="{ x: 0, y: -30, opacity: 0 }"
              :whileInView="{ x: 990, y: -30, opacity: 1 }"
              :transition="{ 
                duration: isMobile ? 0.8 : 1.2, 
                ease: 'easeInOut', 
                delay: 0.8,
                x: { duration: isMobile ? 0.8 : 1.2, ease: 'linear' }
              }"
              :viewport="{ once: true }"
              src="/image/pencil.png" 
              alt="Pencil" 
              class="pencil-img animated-pencil-outside mobile-pencil" 
            />
          </motion.div>
        </motion.div>
      </div>
    </section>

    <!-- 第七页：用户评价 -->
    <section id="testimonials" class="landing-section testimonials-section">
      <div class="section-container">
        <div class="testimonial-content">
          <!-- 左侧：用户照片 -->
          <motion.div
            :initial="{ opacity: 0, x: -50 }"
            :whileInView="{ opacity: 1, x: 0 }"
            :transition="{ duration: 0.8, ease: 'easeOut' }"
            :viewport="{ once: true, margin: '-100px' }"
            class="testimonial-left"
          >
            <div class="user-photo">
              <img src="/image/hr.jpg" alt="User Photo" class="user-photo-img" />
            </div>
          </motion.div>
          
          <!-- 右侧：评价内容 -->
          <motion.div
            :initial="{ opacity: 0, x: 50 }"
            :whileInView="{ opacity: 1, x: 0 }"
            :transition="{ duration: 0.8, ease: 'easeOut', delay: 0.2 }"
            :viewport="{ once: true, margin: '-100px' }"
            class="testimonial-right"
          >
            <div class="testimonial-text">
              <motion.img
                :initial="{ opacity: 0, y: -20 }"
                :whileInView="{ opacity: 1, y: 0 }"
                :transition="{ duration: 0.6, ease: 'easeOut', delay: 0.4 }"
                :viewport="{ once: true }"
                src="/image/hr-logo.png" 
                alt="Company Logo" 
                class="testimonial-logo"
              />
              <motion.blockquote
                :initial="{ opacity: 0, y: 30 }"
                :whileInView="{ opacity: 1, y: 0 }"
                :transition="{ duration: 0.8, ease: 'easeOut', delay: 0.6 }"
                :viewport="{ once: true }"
                class="testimonial-quote"
              >
                In the dynamic realm of AI-powered hiring, each step propels us toward unlocking vast potential. While embracing ongoing learning, we're not just finding new ways to identify talent—we're actively shaping the future of recruitment.
              </motion.blockquote>
              <motion.div
                :initial="{ opacity: 0, y: 20 }"
                :whileInView="{ opacity: 1, y: 0 }"
                :transition="{ duration: 0.6, ease: 'easeOut', delay: 0.8 }"
                :viewport="{ once: true }"
                class="testimonial-author"
              >
                <div class="author-name">Katelyn Lyu</div>
                <div class="author-title">Consultant</div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>

    <!-- 第七页开始：长页面内容 -->
    <section id="demo" class="landing-section long-content-section">
      <div class="section-container">
        <!-- Unlock AI pioneers 卡片 -->
        <motion.div
          :initial="{ opacity: 0, y: 50 }"
          :whileInView="{ opacity: 1, y: 0 }"
          :transition="{ duration: 0.8, ease: 'easeOut' }"
          :viewport="{ once: true, margin: '-100px' }"
          class="unlock-bg"
        >
          <motion.div
            :initial="{ opacity: 0, y: 30 }"
            :whileInView="{ opacity: 1, y: 0 }"
            :transition="{ duration: 0.8, ease: 'easeOut', delay: 0.2 }"
            :viewport="{ once: true }"
            class="unlock-title text-center flex flex-col items-center justify-center mx-auto"
            style="line-height: 1.3"
          >
            <span>Unlock AI pioneers</span>
            <span>for your firm</span>
          </motion.div>
          <motion.div
            :initial="{ opacity: 0, y: 20 }"
            :whileInView="{ opacity: 1, y: 0 }"
            :transition="{ duration: 0.6, ease: 'easeOut', delay: 0.4 }"
            :viewport="{ once: true }"
            class="unlock-description mt-4 mb-6"
          >
            Top AI devs for Big tech, AI startup, solution service providers and academia.
          </motion.div>
          <motion.button
            :initial="{ opacity: 0, scale: 0.9 }"
            :whileInView="{ opacity: 1, scale: 1 }"
            :transition="{ duration: 0.6, ease: 'easeOut', delay: 0.6 }"
            :viewport="{ once: true }"
            class="demo-button rounded-full bg-black text-white hover:scale-105 transition-all"
            @click="$router.push('/demo')"
          >
            Request a Demo
          </motion.button>
        </motion.div>

        <!-- FAQ 部分 -->
        <motion.div
          :initial="{ opacity: 0, y: 40 }"
          :whileInView="{ opacity: 1, y: 0 }"
          :transition="{ duration: 0.8, ease: 'easeOut', delay: 0.3 }"
          :viewport="{ once: true, margin: '-50px' }"
        >
          <LandingFaqAccordion />
        </motion.div>
      </div>
    </section>
    
    <!-- 页脚内容 - 移出section-container，独立成为全宽区域 -->
    <footer class="landing-footer">
      <div class="footer-main-content">
        <div class="footer-top">
          <div class="footer-logo">
            <nuxt-link to="/">
              <img
                src="/image/newlogo1.png"
                width="95"
                height="42"
                alt="DINQ logo"
              />
            </nuxt-link>
          </div>
          
          <div class="footer-right">
            <div class="footer-links">
              <a href="/terms" class="footer-link">
                <span>Terms & Conditions</span>
              </a>
              <a href="/privacy" class="footer-link">
                <span>Privacy Policy</span>
              </a>
            </div>
            <div class="footer-social">
              <a href="https://x.com/dinq_io" target="_blank">
                <button class="media-btn border-none !bg-twitter">
                  <div class="i-proicons:x-twitter wh-6"></div>
                </button>
              </a>
              <a href="https://discord.gg/JyQwmYUTM6" target="_blank">
                <button class="discord-btn">
                  <div class="text-base"></div>
                </button>
              </a>
            </div>
          </div>
        </div>
        
        <div class="footer-divider"></div>
        
        <div class="footer-copyright">
          Copyright© 2025 DINQ Inc. All Rights Reserved
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { motion } from 'motion-v'
import LandingFaqAccordion from '~/components/LandingFaqAccordion.vue'
import LogoMarquee from '~/components/LogoMarquee/index.vue'
import TypedText from '~/components/TypedText/index.vue'
import SearchBar from '~/components/SearchBar/index.vue'

// 设置页面元数据
definePageMeta({
  layout: 'landing'
})

// SEO 配置 - TDK 和 OG 标签
useSeoMeta({
  // 基本 TDK
  title: 'DINQ | AI Talent Engine for Discovering Top Researchers & Developers',
  description: 'DINQ is an AI-native talent discovery platform that leverages GitHub, arXiv, and Scholar data to help HRs and headhunters find, assess, and connect with world-class AI talent.',
  keywords: 'AI talent search, AI recruitment platform, AI hiring, AI researchers, AI developers, GitHub talent profiling, AI headhunting, semantic candidate search, arXiv profiles, AI job matching, top AI engineers, machine learning hiring, technical recruiting, AI career tracking, DINQ',

  // Open Graph 标签
  ogTitle: 'DINQ | AI Talent Engine for Discovering Top Researchers & Developers',
  ogDescription: 'DINQ is an AI-native talent discovery platform that leverages GitHub, arXiv, and Scholar data to help HRs and headhunters find, assess, and connect with world-class AI talent.',
  ogImage: 'https://dinq.io/image/og.png',
  ogUrl: 'https://dinq.io',
  ogType: 'website',
  ogSiteName: 'DINQ',

  // Twitter Card 标签
  twitterCard: 'summary_large_image',
  twitterSite: '@dinq_io',
  twitterCreator: '@dinq_io',
  twitterTitle: 'DINQ | AI Talent Engine for Discovering Top Researchers & Developers',
  twitterDescription: 'DINQ is an AI-native talent discovery platform that leverages GitHub, arXiv, and Scholar data to help HRs and headhunters find, assess, and connect with world-class AI talent.',
  twitterImage: 'https://dinq.io/image/og.png',
  twitterImageAlt: 'DINQ - AI Talent Engine for Discovering Top Researchers & Developers',

  // 其他重要的 meta 标签
  robots: 'index, follow',
  author: 'DINQ Inc.',
  themeColor: '#CB7C5D',
})

// 添加额外的 Twitter Card meta 标签（使用 name 属性，符合 Twitter 官方规范）
useHead({
  meta: [
    { name: 'twitter:domain', content: 'dinq.io' },
    { name: 'twitter:url', content: 'https://dinq.io' }
  ]
})

// 添加选择框的状态和逻辑
const selected = ref('scholar') // 默认选中 Scholar

// 切换选项的方法
const selectOption = (option: string) => {
  selected.value = option
}

// 检测移动端
const isMobile = ref(false)

onMounted(() => {
  // 检测是否为移动端
  const checkMobile = () => {
    isMobile.value = window.innerWidth <= 768
  }
  
  checkMobile()
  window.addEventListener('resize', checkMobile)
  
  onUnmounted(() => {
    window.removeEventListener('resize', checkMobile)
  })
})


</script>

<style scoped>
/* 基础样式 */
.landing-page {
  scroll-behavior: smooth;
  font-family: 'Alexandria', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  height: 100vh;
  overflow-y: scroll;
  overflow-x: hidden; /* 防止水平滚动 */
  /* 确保主页不受全局深色模式影响 */
  background: #FFFFFF;
  color: #000000;
  width: 100%; /* 确保宽度不超出视窗 */
  max-width: 100vw; /* 防止宽度超出视窗 */
  box-sizing: border-box; /* 确保padding和border包含在宽度内 */
}

/* 通用 Section 样式 */
.landing-section {
  min-height: 100vh; /* 占满整个视窗 */
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow-x: hidden; /* 防止水平滚动 */
}

.section-container {
  max-width: 1440px; /* 统一使用1440px最大宽度，与导航栏保持一致 */
  width: 100%;
  padding: 0 24px;
  margin: 0 auto;
  box-sizing: border-box; /* 确保padding包含在宽度内 */
}

/* 第一页：Hero Section */
.hero-section {
  background: #FFFFFF;
  position: relative;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0; /* 背景图片接顶 */
  right: 0; /* 对齐屏幕右边 */
  width: auto; /* 宽度自适应 */
  height: 95vh; /* 占满整个视窗高度 */
  aspect-ratio: 895/711; /* 使用SVG的原始宽高比 */
  background-image: url('/image/bkhome.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: top right;
  opacity: 0.6;
  z-index: 1;
}

.hero-section .section-container {
  /* 移除特殊的max-width设置，使用全局统一的1440px */
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: end;
  margin-bottom: 80px;
  position: relative;
  z-index: 2;
}

.hero-right {
  display: flex;
  align-items: flex-end;
}

.hero-right-content {
  display: flex;
  flex-direction: column;
  gap: 63px;
  transform: translateY(-11px);
}

.hero-title {
  font-family: 'Alexandria', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-size: 140px;
  font-weight: 600;
  line-height: 100%;
  color: #000000;
  margin-bottom: 0;
}

.highlight {
  color: #CB7C5D;
}

.hero-subtitle {
  font-family: 'Alexandria', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-weight: 300;
  font-size: 24px;
  line-height: 140%;
  letter-spacing: 2%;
  color: #000000;
  margin-bottom: 0;
  max-width: 600px;
}

.demo-cta-btn {
  width: 233px;
  height: 54px;
  background: #000000;
  border: none;
  border-radius: 30px;
  color: #FFFFFF;
  font-family: 'Alexandria', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  align-self: flex-start;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32px;
}

.demo-cta-btn:hover {
  background: #333333;
  transform: translateY(-2px);
}



/* Logo 跑马灯区域 */
.sponsors-section {
  position: absolute;
  bottom: 20px; /* 跑马灯往下移动，减少bottom值 */
  left: 0;
  right: 0;
  text-align: center;
}

/* 第二页：功能展示 */
.features-section {
  background: #FAFAFA;
}

/* 新的布局结构 - 紧凑版本 */
.features-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 90vh;
  justify-content: center;
  align-items: center;
  text-align: center;
  gap: 20px;
  padding: 60px 0;
}

/* 标题和打字机效果区域 */
.features-header-area {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

/* 交互控制区域 */
.features-controls-area {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.toggle-wrapper {
  display: flex;
  justify-content: center;
}

.search-wrapper {
  width: 100%;
  max-width: 760px; /* 桌面端搜索框宽度调整为 760px */
}

/* 产品截图展示区域 */
.features-showcase-area {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 300px; /* 与screenshot-container保持一致 */
}

.features-header {
  margin-bottom: 60px;
}

.section-title {
  font-size: 3rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 20px;
}

.section-subtitle {
  font-size: 1.25rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
}

.demo-search {
  margin-bottom: 60px;
}

.search-container {
  max-width: 600px;
  margin: 0 auto;
  position: relative;
}

.demo-search-input {
  width: 100%;
  padding: 20px 60px 20px 24px;
  border: 2px solid #e0e0e0;
  border-radius: 50px;
  font-size: 1.1rem;
  outline: none;
  transition: border-color 0.3s ease;
}

.demo-search-input:focus {
  border-color: #CB7C5D;
}

.demo-search-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 44px;
  height: 44px;
  background: #CB7C5D;
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
}

.demo-search-btn:hover {
  background: #B86A4D;
}

.screenshot-container {
  position: relative;
  width: 100%;
  max-width: 1200px;
  height: 300px; /* 调整为300px */
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 20px;
}

.screenshot-middle {
  width: 60%;
  max-width: 800px;
  height: auto;
  position: relative;
  z-index: 1;
}

.screenshot-left {
  position: absolute;
  width: 35%;
  max-width: 400px;
  height: auto;
  left: 1%;
  top: 30%;
  transform: translateY(-50%);
  z-index: 2;
}

.screenshot-right {
  position: absolute;
  width: 35%;
  max-width: 400px;
  height: auto;
  right: 5%;
  top: 15%;
  transform: translateY(-50%);
  z-index: 2;
}

/* 第三、四、五页：功能详情 */
.feature-detail-section:nth-child(3) {
  background: #222222;
}

.feature-detail-section:nth-child(4) {
  background: #F3F3F3;
}

.feature-detail-section:nth-child(5) {
  background: #222222;
}

.feature-detail-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0;
  align-items: stretch;
  width: 1200px;
  height: 540px;
  margin: 0 auto;
}

.feature-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 24px;
}

.feature-description {
  font-size: 1.2rem;
  line-height: 1.6;
  color: #666;
  margin-bottom: 32px;
}

.feature-cta-btn {
  padding: 14px 28px;
  background: transparent;
  border: 2px solid #CB7C5D;
  border-radius: 25px;
  color: #CB7C5D;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.feature-cta-btn:hover {
  background: #CB7C5D;
  color: white;
}

/* 第三页特定样式 */
.feature-3-title {
  font-family: 'Alexandria', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-weight: 600;
  font-size: 48px;
  line-height: 130%;
  color: #FFFFFF;
}

.feature-3-description {
  font-family: 'Alexandria', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-weight: 400;
  font-size: 20px;
  line-height: 130%;
  color: #F2F2F2;
}

.feature-3-btn {
  background: transparent;
  border: none;
  color: #AFAFAF;
  font-family: 'Alexandria', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 130%;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0;
}

.feature-3-btn:hover {
  background: transparent;
  color: #FFFFFF;
}

.arrow-right {
  font-size: 18px;
  transition: transform 0.3s ease;
}

.feature-3-btn:hover .arrow-right {
  transform: translateX(4px);
}

.feature-left {
  background-color: #2F2F2F;
  padding: 40px 70px 40px 40px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  border-radius: 12px 0 0 12px;
}

.feature-right {
  height: 100%;
}

.feature-media {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-gif {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0 12px 12px 0;
}

.feature-gif-left {
  border-radius: 12px 0 0 12px;
}

.feature-left-right {
  border-radius: 0 12px 12px 0;
  padding: 40px 40px 40px 80px;
}

.media-placeholder {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 1.2rem;
  font-weight: 500;
}

/* 第六页：More targeted, less clutter */
.more-targeted-section {
  background: #FFFFFF;
  height: 242px !important;
  min-height: 242px !important;
  max-height: 242px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.more-targeted-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 32px;
}

.more-targeted-title {
  font-family: 'Alexandria', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-weight: 600;
  font-size: 36px;
  line-height: 130%;
  color: #000000;
  text-align: center;
  margin: 0;
}

.more-targeted-images {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: visible; /* 允许铅笔超出容器 */
}

/* 默认桌面端显示单个图标，隐藏移动端logo布局 */
.desktop-tablet-icons {
  display: block;
}

.mobile-logos-container {
  display: none;
}

.drawing-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 940px; /* 940px total width */
  overflow: hidden;
}

.home-icons-img {
  width: 898px;
  height: 42px;
  object-fit: contain;
  position: relative;
  z-index: 1;
}

.drawing-line {
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  width: 940px;
  height: 2px;
  z-index: 2;
  pointer-events: none;
}



.pencil-img {
  width: 58px;
  height: 48px;
  object-fit: contain;
  position: absolute;
  top: 50%;
  transform: translateY(-50%) translateY(24px); /* Move pencil down so bottom aligns with line */
  z-index: 3;
}

.animated-pencil {
  /* 初始位置在左边外面 */
  left: -58px;
  transform: translateY(-50%) translateY(24px) translateX(0);
}

.animated-pencil-outside {
  /* 相对于more-targeted-images容器定位 */
  position: absolute;
  left: -58px;
  top: 50%;
  z-index: 3;
}

/* 默认显示桌面端铅笔 */
.desktop-pencil {
  display: block;
}

.tablet-pencil,
.mobile-pencil {
  display: none;
}



/* 第七页：用户评价 */
.testimonials-section {
  background: #F6EFEC;
  color: white;
}

.testimonial-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 80px;
  align-items: center;
}

.user-photo {
  width: 500px;
  height: 500px;
  margin: 0 auto;
  overflow: hidden;
}

.user-photo-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center top;
}

.testimonial-logo {
  width: 150px;
  height: auto;
  margin-bottom: 24px;
}

.testimonial-quote {
  font-family: 'Alexandria', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-weight: 600;
  font-size: 32px;
  line-height: 42px;
  letter-spacing: -1%;
  color: #1D1E25;
  margin-bottom: 32px;
  font-style: normal;
}

.author-name {
  font-family: 'Alexandria', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-weight: 500;
  font-size: 24px;
  line-height: 32px;
  color: #1D1E25;
  margin-bottom: 8px;
}

.author-title {
  font-family: 'Alexandria', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 20px;
  color: #7E8492;
}

/* 第七页：长内容 */
.long-content-section {
  min-height: auto;
  padding: 80px 0 30px 0; /* 顶部80px，底部30px，与页脚margin-top保持协调 */
  background: #FFFFFF;
}

/* Unlock AI pioneers 卡片样式 */
.unlock-bg {
  background: #EFEFEF;
  margin-bottom: 30px;
  border-radius: 20px;
  height: 315px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 4px 4px 0px #00000010;
  position: relative;
  overflow: hidden;
}

.unlock-bg::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 315px;
  height: 315px;
  background-image: url('/image/faql.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: left center;
  opacity: 1;
}

.unlock-bg::after {
  content: '';
  position: absolute;
  right: 6%;
  bottom: 8%;
  height: 75%;
  width: auto;
  aspect-ratio: 1;
  background-image: url('/image/faqr.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 1;
}

.unlock-title {
  font-family: 'Alexandria', sans-serif;
  font-weight: 600;
  font-size: 48px;
  color: #1D1D1D;
  position: relative;
  z-index: 2;
}

.unlock-description {
  font-family: 'Alexandria', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #000000;
  text-align: center;
  position: relative;
  z-index: 2;
}

.demo-button {
  width: 199px;
  height: 56px;
  font-weight: 700;
  font-size: 16px;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  position: relative;
  z-index: 2;
}

/* 工具类 */
.mt-4 {
  margin-top: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.px-7 {
  padding-left: 1.75rem;
  padding-right: 1.75rem;
}

.rounded-full {
  border-radius: 9999px;
}

.font-medium {
  font-weight: 500;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-white {
  color: rgb(255 255 255);
}

.bg-black {
  background-color: rgb(0 0 0);
}

.hover\:bg-gray-800:hover {
  background-color: rgb(31 41 55);
}

.hover\:scale-105:hover {
  transform: scale(1.05);
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.text-center {
  text-align: center;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .unlock-bg {
    height: 280px;
    margin: 0 16px 30px 16px;
  }
  
  .unlock-bg::before {
    width: 200px;
    height: 200px;
    background-position: left top;
  }
  
  .unlock-bg::after {
    height: 50%;
    width: auto;
  }
  
  .unlock-title {
    font-size: 32px;
  }
  
  .unlock-description {
    font-size: 14px;
    padding: 0 20px;
  }
}

@media (max-width: 640px) {
  .unlock-bg {
    height: 240px;
  }
  
  .unlock-bg::before {
    width: 150px;
    height: 150px;
    background-position: left top;
  }
  
  .unlock-bg::after {
    height: 30%;
    width: auto;
  }
  
  .unlock-title {
    font-size: 28px;
  }
}

/* 页脚 */
.landing-footer {
  margin-top: 30px; /* mt-7.5 = 1.875rem = 30px */
  padding: 30px 0;
  width: 100%; /* 确保页脚横跨整个屏幕 */
  position: relative;
  left: 0;
  right: 0;
}

.footer-main-content {
  /* 移除最大宽度限制，让页脚内容横跨整个屏幕 */
  width: 100%;
  padding: 0 24px;
  box-sizing: border-box;
}

.footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.footer-right {
  display: flex;
  align-items: center;
  gap: 80px; /* gap-20 = 5rem = 80px */
}

.footer-links {
  display: flex;
  align-items: center;
  gap: 46px; /* gap-[2.88rem] = 46px */
}

.footer-link {
  font-family: Poppins, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 150%;
  color: #283646;
  text-decoration: none;
  text-transform: capitalize;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: #2563eb;
}

.footer-social {
  display: flex;
  align-items: center;
  gap: 22px; /* gap-5.5 = 1.375rem = 22px */
}

.media-btn {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  color: white;
}

.discord-btn {
  border-radius: 50%;
  width: 42px;
  height: 42px;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  background-color: #ffffff;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
}

.discord-btn:hover {
  background-color: #f3f4f6;
  color: #CB7C5D;
}

.text-base {
  background-image: url('~/assets/image/footthemedark.png');
  background-size: 100%;
  width: 42px;
  height: 42px;
  display: inline-block;
}

.footer-divider {
  width: 100%;
  height: 1px;
  background-color: #E0E6EE;
  margin: 30px 0 20px 0;
}

.footer-copyright {
  text-align: center;
  font-family: Poppins, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 150%;
  color: #283646;
  text-transform: capitalize;
}

/* Improved homepage toggle styles */
.homepage-toggle-container {
  position: relative;
  width: 296px;
  height: 50px;
  margin: 0 auto;
}

.homepage-toggle-background {
  position: relative;
  width: 100%;
  height: 100%;
  background: #FFFFFF99;
  border-radius: 25px;
  border: 1px solid #000000;
  backdrop-filter: blur(34px);
  padding: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.homepage-toggle-slider {
  position: absolute;
  top: 4px;
  left: 4px;
  width: calc(50% - 6px);
  height: calc(100% - 8px);
  background-color: #000000;
  border-radius: 21px;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

.homepage-toggle-slider.slide-to-github {
  transform: translateX(calc(100% + 4px));
}

.homepage-toggle-option {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-weight: 600;
  font-size: 16px;
  color: #000000;
  border-radius: 21px;
  transition: color 0.3s ease;
  position: relative;
  z-index: 2;
}

.homepage-toggle-option.active {
  color: #FFFFFF;
}

.homepage-toggle-option:hover:not(.active) {
  color: #333333;
}

.terms-text {
  font-family: Aeonik;
  font-weight: 400;
  font-size: 14px;
  line-height: 160%;
  letter-spacing: 0%;
  color: rgba(67, 74, 79, 0.6);
}

.analysis-title {
  font-family: 'Alexandria', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-weight: 600;
  font-size: 70px;
  line-height: 130%;
  letter-spacing: 0%;
  text-align: center;
  color: #000000;
}

.typed-text-container {
  font-family: 'Alexandria', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-weight: 600;
  font-size: 40px;
  line-height: 130%;
  letter-spacing: 0%;
  color: #000000;
  height: 73px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.text-primary-100 {
  color: #CB7C5D;
}

.h-\[73px\] {
  height: 73px;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-10 {
  margin-bottom: 2.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
  
  /* 移动端第一页背景图片 */
  .hero-section::before {
    background-image: url('/image/mobliebg.png');
    background-position: top right;
    opacity: 1; /* 不透明 */
    height: 60vh; /* 移动端占屏幕高度的一半 */
  }
  
  .section-container {
    max-width: 100%; /* 移动端使用100%宽度 */
    padding: 0 20px; /* 统一使用20px padding */
  }
  
  .hero-content,
  .feature-detail-content,
  .testimonial-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }
  
  .hero-content {
    text-align: center; /* 容器居中对齐 */
    display: flex;
    flex-direction: column;
    align-items: center; /* 子元素居中 */
  }
  
  .hero-left,
  .hero-right {
    text-align: left; /* 文本内容左对齐 */
    width: 100%; /* 占满容器宽度 */
    max-width: 90%; /* 限制最大宽度，实现居中效果 */
  }
  
  .hero-right {
    align-items: flex-start;
  }
  
  .feature-detail-content {
    text-align: center; /* feature section保持居中 */
  }
  
  .testimonial-content {
    text-align: left; /* testimonial section文字左对齐 */
    padding: 0 20px; /* 添加左右内距 */
  }
  
  /* 移动端testimonial样式优化 */
  .testimonial-text {
    text-align: left; /* 确保文字左对齐 */
    max-width: 400px; /* 与头像图片同宽 */
    margin: 0 auto; /* 居中对齐，与头像图片对齐 */
    padding-left: 0; /* 确保左边界对齐 */
  }
  
  .testimonial-logo {
    width: 120px; /* 移动端缩小logo */
    margin-bottom: 20px;
  }
  
  .testimonial-quote {
    font-size: 1rem; /* 统一使用1rem */
    line-height: 1.8; /* 增加行距 */
    margin-bottom: 24px;
    text-align: left; /* 确保引用文字左对齐 */
  }
  
  .author-name {
    font-size: 1rem; /* 统一使用1rem */
    line-height: 1.6; /* 增加行距 */
    margin-bottom: 6px;
    text-align: left;
  }
  
  .author-title {
    font-size: 1rem; /* 统一使用1rem */
    line-height: 1.5; /* 增加行距 */
    text-align: left;
  }
  
  .user-photo {
    width: 100%; /* 移动端照片宽度调整 */
    max-width: 400px;
    height: 400px; /* 高度和最大宽度相同，形成正方形 */
    margin: 0 auto 0px auto; /* 照片居中，减少底部间距 */
  }
  
  .hero-title {
    font-size: 6.3rem; /* 100.8px = 6.3rem (基于16px根字体) */
    line-height: 1.15; /* 移动端标题行距调大一点 */
    margin-top: 3rem; /* 移动端标题距离顶部增加间距 */
  }
  
  .hero-subtitle {
    font-size: 1.25rem; /* 移动端副标题20px */
    max-width: 100%; /* 移动端副标题宽度限制为100% */
  }
  
  .demo-cta-btn {
    margin-top: -1.5rem; /* 移动端按钮往上移动 */
    align-self: center; /* 移动端按钮居中 */
  }
  
  .sponsors-section {
    position: static; /* 移动端改为静态定位 */
    margin-top: -3rem; /* 确保与上方按钮的最小间距 */
    padding-bottom: 0.1rem; /* 底部留一些空间 */
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .feature-title {
    font-size: 2rem;
  }
  
  .content-blocks {
    grid-template-columns: 1fr;
  }
  
  /* 移动端页脚重新布局 */
  .footer-main-content {
    position: relative;
    /* 移动端也移除最大宽度限制 */
    padding: 0 20px; /* 统一使用20px padding */
  }
  
  /* 顶部：logo左，社交媒体右 */
  .footer-top {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 60px; /* 增加间距为链接区域留出空间 */
  }
  
  .footer-logo {
    flex-shrink: 0;
  }
  
  .footer-right {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    gap: 16px;
  }
  
  /* 将links从footer-right中提取出来，绝对定位到中间 */
  .footer-right .footer-links {
    position: absolute;
    top: 72px; /* footer-top的高度 + margin */
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: 0;
    gap: 0;
    padding: 0 25px; /* 添加左右边距，与页面边距对齐 */
    box-sizing: border-box;
  }
  
  /* 移动端分割线 */
  .footer-divider {
    display: block;
    width: 100%;
    height: 1px;
    background-color: rgba(0, 0, 0, 0.1);
    border: none;
    margin: 80px 0 20px 0; /* 增加上方间距到50px，下方保持20px */
  }
  
  /* 底部：版权信息居中 */
  .footer-copyright {
    text-align: center;
    opacity: 0.7;
    color: #666;
    margin-top: 0; /* 移除原有margin，由分割线控制间距 */
  }
  
  .analysis-title {
    font-size: 48px;
  }
  
  .typed-text-container {
    font-size: 28px;
  }
  
  .homepage-toggle-container {
    width: 260px;
    height: 44px;
  }

  .homepage-toggle-background {
    border-radius: 22px;
    padding: 3px;
    gap: 3px;
  }

  .homepage-toggle-slider {
    top: 3px;
    left: 3px;
    width: calc(50% - 4.5px);
    height: calc(100% - 6px);
    border-radius: 19px;
  }

  .homepage-toggle-slider.slide-to-github {
    transform: translateX(calc(100% + 3px));
  }

  .homepage-toggle-option {
    font-size: 14px;
    border-radius: 19px;
  }
  
  /* More targeted section 响应式 */
  .more-targeted-title {
    font-size: 28px;
  }
  
  .drawing-container {
    width: 90%;
    max-width: 940px; /* 940px max width for tablet */
  }
  
  .home-icons-img {
    width: 100%;
    max-width: 600px;
  }
  
  .drawing-line {
    width: 100%;
    max-width: 940px;
    left: 0; /* Full width alignment */
  }
  
  .pencil-img {
    width: 48px;
    height: 40px;
    transform: translateY(-50%) translateY(20px); /* Bottom align for smaller pencil */
  }
  
  .animated-pencil {
    left: -48px;
    transform: translateY(-50%) translateY(20px) translateX(0);
  }
  
  .animated-pencil-outside {
    left: -48px;
  }
  
  /* 平板端显示平板铅笔 */
  .desktop-pencil {
    display: none;
  }
  
  .tablet-pencil {
    display: block;
  }
  
  .mobile-pencil {
    display: none;
  }
}

/* 移动端响应式 */
@media (max-width: 480px) {
  .footer-main-content {
    padding: 0 16px; /* 小屏幕进一步减少padding */
  }
  
  .footer-links {
    gap: 20px; /* 减小间距，从原来的46px减到20px */
    flex-wrap: nowrap; /* 强制不换行 */
  }
  
  .footer-link {
    font-size: 14px; /* 减小字体，从16px减到14px */
    white-space: nowrap; /* 防止单个链接内部换行 */
  }
  
  .footer-right {
    gap: 16px;
  }
  
  .footer-social {
    gap: 16px;
  }
}

/* 针对更窄的屏幕 */
@media (max-width: 400px) {
  .footer-links {
    gap: 16px; /* 进一步减小间距到16px */
  }
  
  .footer-link {
    font-size: 13px; /* 进一步减小字体到13px */
  }
}

/* 针对极窄屏幕 */
@media (max-width: 350px) {
  .footer-links {
    gap: 12px; /* 最小间距12px */
  }
  
  .footer-link {
    font-size: 12px; /* 最小字体12px */
  }
}

/* 响应式调整 - 紧凑布局 */
@media (max-width: 768px) {
  .features-layout {
    min-height: 80vh;
    gap: 16px;
    padding: 40px 0;
  }
  
  .features-header-area {
    gap: 12px;
  }
  
  .features-controls-area {
    gap: 12px;
  }

  .search-wrapper {
    max-width: 100%; /* 平板端搜索框占满宽度 */
    padding: 0 20px; /* 左右留有边距 */
  }
  
  .features-showcase-area {
    height: 350px; /* 增加高度以容纳更大的图像 */
  }
  
  .screenshot-container {
    height: 350px; /* 对应增加容器高度 */
    padding: 0 10px;
  }

  .screenshot-middle {
    width: 75%; /* 增大中间图片 */
  }

  .screenshot-left,
  .screenshot-right {
    width: 42%; /* 增大左右图片 */
  }
  
  .screenshot-left {
    left: 5%; /* 调整位置以适应更大的图片 */
  }
  
  .screenshot-right {
    right: 5%; /* 调整位置以适应更大的图片 */
  }
}

@media (max-width: 480px) {
  .features-layout {
    min-height: 70vh;
    gap: 12px;
    padding: 30px 0;
  }
  
  .features-header-area {
    gap: 8px;
  }
  
  .features-controls-area {
    gap: 10px;
  }

  .search-wrapper {
    max-width: 100%; /* 手机端搜索框占满宽度 */
    padding: 0 12px; /* 减少左右边距以最大化搜索框宽度 */
  }
  
  .features-showcase-area {
    height: 280px; /* 手机端适当增大图像区域 */
  }
  
  .screenshot-container {
    height: 280px; /* 对应增加容器高度 */
    padding: 0 5px;
  }

  .screenshot-middle {
    width: 85%; /* 手机端进一步增大中间图片 */
  }

  .screenshot-left,
  .screenshot-right {
    width: 48%; /* 增大左右图片 */
  }
  
  .screenshot-left {
    left: 2%; /* 调整位置以适应更大的图片 */
  }
  
  .screenshot-right {
    right: 2%; /* 调整位置以适应更大的图片 */
  }
  
  /* More targeted section 移动端响应式 */
  .more-targeted-section {
    height: 242px !important;
    min-height: 242px !important;
    max-height: 242px !important;
  }
  
  .more-targeted-title {
    font-size: 24px;
  }
  
  .drawing-container {
    width: 95%;
    max-width: 940px; /* 940px max width for mobile */
  }
  
  .home-icons-img {
    width: 100%;
    max-width: 400px;
  }
  
  .drawing-line {
    width: 100%;
    max-width: 940px;
    left: 0; /* Full width alignment */
  }
  
  .pencil-img {
    width: 40px;
    height: 32px;
    transform: translateY(-50%) translateY(16px); /* Bottom align for smallest pencil */
  }
  
  .animated-pencil {
    left: -40px;
    transform: translateY(-50%) translateY(16px) translateX(0);
  }
  
  .animated-pencil-outside {
    left: -40px;
  }
  
  /* 移动端显示移动铅笔 */
  .desktop-pencil,
  .tablet-pencil {
    display: none;
  }
  
  .mobile-pencil {
    display: block;
  }
}

/* 第二页搜索框和第三四五页移动端响应式 */
@media (max-width: 768px) {
  /* 第二页搜索框和图像优化 */
  .features-layout {
    min-height: 80vh;
    gap: 16px;
    padding: 40px 0;
  }
  
  .features-header-area {
    gap: 12px;
  }
  
  .features-controls-area {
    gap: 12px;
  }

  .search-wrapper {
    max-width: 100%; /* 平板端搜索框占满宽度 */
    padding: 0 20px; /* 左右留有边距 */
  }
  
  .features-showcase-area {
    height: 350px; /* 增加高度以容纳更大的图像 */
  }
  
  .screenshot-container {
    height: 350px; /* 对应增加容器高度 */
    padding: 0 10px;
  }

  .screenshot-middle {
    width: 75%; /* 增大中间图片 */
  }

  .screenshot-left,
  .screenshot-right {
    width: 42%; /* 增大左右图片 */
  }
  
  .screenshot-left {
    left: 5%; /* 调整位置以适应更大的图片 */
  }
  
  .screenshot-right {
    right: 5%; /* 调整位置以适应更大的图片 */
  }
  
  /* 第三四五页上下结构 */
  .feature-detail-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: auto;
    gap: 0;
    margin: 0;
    padding: 0 16px;
  }
  
  .feature-left {
    background-color: #2F2F2F;
    padding: 24px 16px;
    border-radius: 0 0 12px 12px;
    order: 2; /* 文字在下 */
    text-align: left; /* 文字左对齐 */
    height: 350px; /* 与gif区域相同高度 */
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  .feature-left-right {
    background-color: #2F2F2F;
    padding: 24px 16px;
    border-radius: 0 0 12px 12px;
    order: 2; /* 文字在下 */
    text-align: left; /* 文字左对齐 */
    height: 350px; /* 与gif区域相同高度 */
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  .feature-right {
    height: 350px; /* 平板端gif区域高度 */
    order: 1; /* gif在上 */
  }
  
  .feature-media {
    height: 100%;
    border-radius: 12px 12px 0 0;
    overflow: hidden;
  }
  
  .feature-gif,
  .feature-gif-left {
    border-radius: 12px 12px 0 0;
    height: 100%;
    object-fit: cover;
  }
  
  .feature-3-title {
    font-size: 32px;
    text-align: left;
  }
  
  .feature-3-description {
    font-size: 16px;
    text-align: left;
  }
  
  .feature-3-btn {
    justify-content: flex-start;
  }
}

@media (max-width: 480px) {
  /* 第二页搜索框和图像优化 */
  .features-layout {
    min-height: 70vh;
    gap: 12px;
    padding: 30px 0;
  }
  
  .features-header-area {
    gap: 8px;
  }
  
  .features-controls-area {
    gap: 10px;
  }

  .search-wrapper {
    max-width: 100%; /* 手机端搜索框占满宽度 */
    padding: 0 12px; /* 减少左右边距以最大化搜索框宽度 */
  }
  
  .features-showcase-area {
    height: 280px; /* 手机端适当增大图像区域 */
  }
  
  .screenshot-container {
    height: 280px; /* 对应增加容器高度 */
    padding: 0 5px;
  }

  .screenshot-middle {
    width: 85%; /* 手机端进一步增大中间图片 */
  }

  .screenshot-left,
  .screenshot-right {
    width: 48%; /* 增大左右图片 */
  }
  
  .screenshot-left {
    left: 2%; /* 调整位置以适应更大的图片 */
  }
  
  .screenshot-right {
    right: 2%; /* 调整位置以适应更大的图片 */
  }
  
  /* 第三四五页手机端优化 */
  .feature-detail-content {
    padding: 0 12px;
  }
  
  .feature-left,
  .feature-left-right {
    padding: 20px 12px;
    height: 280px; /* 与手机端gif区域相同高度 */
  }
  
  .feature-right {
    height: 280px; /* 手机端gif区域高度 */
  }
  
  .feature-3-title {
    font-size: 28px;
  }
  
  .feature-3-description {
    font-size: 14px;
  }
  
  /* More targeted section 移动端响应式 */
  .more-targeted-section {
    height: 300px !important; /* 调整高度以适应紧凑的两行logo */
    min-height: 300px !important;
    max-height: 300px !important;
  }
  
  .more-targeted-title {
    font-size: 24px;
  }
  
  /* 移动端隐藏桌面端图标，显示移动端logo布局 */
  .desktop-tablet-icons {
    display: none;
  }
  
  .mobile-logos-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    width: 100%;
    max-width: 320px;
    margin: 0 auto;
  }
  
  .logos-grid {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-bottom: 8px;
  }
  
  .logos-row {
    display: flex;
    justify-content: center;
    gap: 16px;
    position: relative;
  }
  
  .mobile-logo {
    width: 80px;
    height: 80px;
    object-fit: contain;
    border-radius: 8px;
  }
  
  .row-drawing-line {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 280px;
    height: 2px;
    z-index: 2;
    pointer-events: none;
  }
  
  .first-row-line {
    /* 第一行划线样式 */
  }
  
  .second-row-line {
    /* 第二行划线样式 */
  }
  
  .drawing-container {
    width: 95%;
    max-width: 940px; /* 940px max width for mobile */
  }
  
  .home-icons-img {
    width: 100%;
    max-width: 400px;
  }
  
  .drawing-line {
    width: 100%;
    max-width: 940px;
    left: 0; /* Full width alignment */
  }
  
  .pencil-img {
    width: 40px;
    height: 32px;
    transform: translateY(-50%) translateY(16px); /* Bottom align for smallest pencil */
  }
  
  .animated-pencil {
    left: -40px;
    transform: translateY(-50%) translateY(16px) translateX(0);
  }
  
  .animated-pencil-outside {
    left: -40px;
  }
  
  /* 移动端隐藏所有铅笔动画 */
  .desktop-pencil,
  .tablet-pencil,
  .mobile-pencil {
    display: none;
  }
}

/* 500px以下屏幕的特殊处理 */
@media (max-width: 500px) {
  /* 第一页大标题减小20% */
  .hero-title {
    font-size: 5.04rem; /* 6.3rem * 0.8 = 5.04rem */
  }
  
  /* 第二页搜索框变窄 */
  .search-wrapper {
    max-width: 90% !important; /* 缩小到90%宽度 */
    padding: 0 8px !important; /* 减小左右边距 */
  }
  
  /* 强制搜索框内部元素也变窄 */
  .search-wrapper :deep(.custom-input) {
    max-width: 100% !important;
    width: 100% !important;
  }
}

/* 400px以下屏幕进一步缩小标题 */
@media (max-width: 400px) {
  .hero-title {
    font-size: 4.5rem; /* 进一步缩小到4.5rem */
  }
}

/* 平板端调整 */
@media (max-width: 1024px) and (min-width: 769px) {
  .section-container {
    max-width: 1200px; /* 平板端使用1200px最大宽度 */
    padding: 0 24px; /* 平板端保持24px padding */
  }
  
  .hero-content {
    gap: 60px; /* 平板端减少gap */
  }
  
  .hero-title {
    font-size: 120px; /* 平板端稍微减小标题 */
  }
  
  .screenshot-container {
    max-width: 1000px; /* 平板端调整截图容器宽度 */
  }
}

/* 小屏幕移动端优化 */
@media (max-width: 480px) {
  .section-container {
    padding: 0 16px; /* 小屏幕进一步减少padding */
  }
}
</style> 