<template>
  <div class="px-30 pt-7.5 h-full">
    <template v-if="loading">
      <!-- 骨架屏 -->
      <div class="space-y-7.5 flex flex-col items-center relative">
        <Loading :visible="loading" :data="thinking" @update:visible="router.replace('/analysis')" />
        <!-- Profile Card 骨架屏 -->
        <div class="bg-white dark:bg-[#141415] rounded-2xl p-7.5 relative animate-pulse w-1/2">
          <!-- 头像和基本信息 -->
          <div class="flex items-start gap-7.5">
            <div class="w-25 h-25 rounded-full bg-gray-200/30 dark:bg-gray-600/30"></div>
            <div class="flex-1 space-y-4">
              <div class="h-8 bg-gray-200/30 dark:bg-gray-600/30 rounded-lg w-1/3"></div>
              <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/4"></div>
              <div class="flex flex-wrap gap-2.5">
                <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-24"></div>
                <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-28"></div>
                <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-20"></div>
              </div>
            </div>
            <div class="flex gap-3">
              <div class="w-10 h-10 rounded-full bg-gray-200/30 dark:bg-gray-600/30"></div>
              <div class="w-10 h-10 rounded-full bg-gray-200/30 dark:bg-gray-600/30"></div>
            </div>
          </div>

          <!-- 统计数据 -->
          <div class="mt-7.5 grid grid-cols-4 gap-7.5">
            <div class="space-y-2.5">
              <div class="h-7 bg-gray-200/30 dark:bg-gray-600/30 rounded-lg w-3/4"></div>
              <div class="h-4 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/2"></div>
            </div>
            <div class="space-y-2.5">
              <div class="h-7 bg-gray-200/30 dark:bg-gray-600/30 rounded-lg w-3/4"></div>
              <div class="h-4 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/2"></div>
            </div>
            <div class="space-y-2.5">
              <div class="h-7 bg-gray-200/30 dark:bg-gray-600/30 rounded-lg w-3/4"></div>
              <div class="h-4 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/2"></div>
            </div>
            <div class="space-y-2.5">
              <div class="h-7 bg-gray-200/30 dark:bg-gray-600/30 rounded-lg w-3/4"></div>
              <div class="h-4 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/2"></div>
            </div>
          </div>
        </div>

        <!-- Most Cited Papers 骨架屏 -->
        <div class="w-full bg-white dark:bg-[#141415] rounded-2xl p-7.5 animate-pulse min-h-80">
          <div class="h-10 bg-gray-200/30 dark:bg-gray-600/30 rounded-lg w-1/3 mx-auto mb-7.5"></div>
          <div class="flex justify-center gap-7.5 h-[calc(100%-4.5rem)]">
            <div class="w-[45%] p-7.5 rounded-2xl bg-gray-200/10 dark:bg-gray-600/10 flex flex-col">
              <div class="space-y-4">
                <div class="h-7 bg-gray-200/30 dark:bg-gray-600/30 rounded-lg w-3/4"></div>
                <div class="flex items-center justify-between">
                  <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/4"></div>
                  <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/6"></div>
                </div>
              </div>
              <div class="mt-auto space-y-4">
                <div class="h-20 bg-gray-200/20 dark:bg-gray-600/20 rounded-lg w-full"></div>
                <div class="flex items-center justify-between">
                  <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/3"></div>
                  <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/4"></div>
                </div>
              </div>
            </div>
            <div class="w-[45%] p-7.5 rounded-2xl bg-gray-200/10 dark:bg-gray-600/10 flex flex-col">
              <div class="space-y-4">
                <div class="h-7 bg-gray-200/30 dark:bg-gray-600/30 rounded-lg w-3/4"></div>
                <div class="flex items-center justify-between">
                  <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/4"></div>
                  <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/6"></div>
                </div>
              </div>
              <div class="mt-auto space-y-4">
                <div class="h-20 bg-gray-200/20 dark:bg-gray-600/20 rounded-lg w-full"></div>
                <div class="flex items-center justify-between">
                  <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/3"></div>
                  <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/4"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template v-else-if="reportData">
      <div class="space-y-5" v-if="reportData && !loading">
        <ProfileCard
          :profile="reportData.researcherProfile.researcherInfo"
          :allInfo="reportData.researcherProfile"
          @share-click="showPopup = true"
        />

        <Teleport to="body">
          <ShareCard
            :show="showPopup"
            :user="shareCardUser"
            :income="shareCardIncome"
            :is-dark="isDark"
            :stats="shareCardStats"
            :role-model="shareCardRoleModel"
            :top-tier-papers="reportData.researcherProfile.dataBlocks.publicationInsight.topTierPapers"
            :conferenceDistribution="
              reportData.researcherProfile.dataBlocks.publicationInsight.conferenceDistribution
            "
            @close="showPopup = false"
            v-if="shareCardUser && shareCardStats && shareCardRoleModel"
          />
        </Teleport>

        <!-- 隐藏的分享卡片用于 OG 图片生成 -->
        <div
          v-if="reportData && shareCardUser && shareCardStats && shareCardRoleModel && !ogImageGenerated"
          ref="hiddenShareCardRef"
          :style="{
            position: 'fixed',
            top: '0',
            left: '0',
            transform: 'translateX(-100%)',
            zIndex: '-1',
            pointerEvents: 'none'
          }"
        >
          <ShareCard
            :show="true"
            :user="shareCardUser"
            :income="shareCardIncome"
            :is-dark="isDark"
            :stats="shareCardStats"
            :role-model="shareCardRoleModel"
            :top-tier-papers="reportData.researcherProfile.dataBlocks.publicationInsight.topTierPapers"
            :conferenceDistribution="
              reportData.researcherProfile.dataBlocks.publicationInsight.conferenceDistribution
            "
            data-card-id="share-card"
            @close="() => {}"
          />
        </div>

        <motion.div
          :initial="{ opacity: 0, y: 10 }"
          :animate="{ opacity: 1, y: 0 }"
          class="bg-[#FBF8F7] dark:bg-[#141415] f-cer pt-7.5 pb-[40px] flex-col wanna-compare-card"
          style="border-radius: 15px;"
        >
          <div class="clash-semibold text-8 text-[#25324B] dark:text-white max-w-547px text-center">
            Wanna compare
            <span class="text-primary-100">
              {{ reportData.researcherProfile.researcherInfo.name }}
            </span>
            with other Al researchers?
          </div>
          <div class="text-base text-[#515865] dark:text-[#7A7A7A] mt-4">
            Just input their name or Google Scholar link
          </div>
          <motion.div
            :initial="{ opacity: 0, y: 10 }"
            :animate="{ opacity: 1, y: 0 }"
            :transition="{ duration: 0.3, ease: 'easeInOut', delay: 0.2 }"
            class="f-cer mt-7.5"
          >
            <div
              class="custom-input border rounded-full bg-white border-black min-h-16 w-195 p-1 flex items-center justify-between gap-4 pl-7.5"
            >
              <SearchInput ref="compareInputRef" @enter-search="handleCompare" />
              <ActionButton :imgSrc="stars" buttonText="Compare" @click="handleCompare" />
            </div>
            <!-- <div
              class="border rounded-full bg-white border-black min-h-16 w-190 p-1 flex items-center justify-between gap-4 pl-7.5 shadow"
            >
              <SearchInput ref="compareInputRef" @enter-search="handleCompare" />
              <ActionButton :imgSrc="stars" buttonText="Compare" @click="handleCompare" />
            </div> -->
          </motion.div>
        </motion.div>
        <div class="grid grid-cols-2 gap-7.5">
          <ReportCard
            icon="document"
            :title="reportData.researcherProfile.dataBlocks.publicationStats.blockTitle"
            :x="-50"
            :card-id="'publication-stats'"
            @share-popup="showPopup = true"
          >
            <SegmentTable class="mt-2.5" :items="papersItems" />
            <BarLineChart
              class="mt-2"
              :yearlyPapers="reportData.researcherProfile.dataBlocks.publicationStats.yearlyPapers"
              :yearlyCitations="
                reportData.researcherProfile.dataBlocks.publicationStats.yearlyCitations
              "
            />
          </ReportCard>
          <ReportCard
            icon="pie-chart"
            :title="reportData.researcherProfile.dataBlocks.publicationInsight.blockTitle"
            :card-id="'publication-insight'"
            @share-popup="showPopup = true"
          >
            <div
              class="bg-special-blush dark:bg-[#222222] px-3 mb-5 py-2.5 text-#666 dark:text-[#C6C6C6] text-13px fx-cer gap-2"
              style="border-radius: 4px;"
            >
              <SvgIcon name="wrapper" />
              <span>Only analyze 500 most influential academic paper.</span>
            </div>
            <SegmentTable :items="insightsItems" />
            <DonutChart
              :conferenceDistribution="
                reportData.researcherProfile.dataBlocks.publicationInsight.conferenceDistribution
              "
              :topTierPapers="reportData.researcherProfile.dataBlocks.publicationInsight.topTierPapers"
            />
          </ReportCard>
          <ReportCard
            icon="settings"
            :title="reportData.researcherProfile.dataBlocks.roleModel.blockTitle"
            :x="-50"
            :card-id="'role-model'"
            @share-popup="showPopup = true"
          >
            <div class="flex flex-col h-full space-y-4">
              <div class="fx-cer gap-4 w-full">
                <Avatar
                  :src="reportData.researcherProfile.dataBlocks.roleModel.photoUrl"
                  :size="70"
                  v-if="reportData.researcherProfile.dataBlocks.roleModel.photoUrl"
                />
                <div class="flex flex-col gap-1 flex-1">
                  <div class="text-2xl font-bold fx-cer justify-between">
                    <span>{{ reportData.researcherProfile.dataBlocks.roleModel.name }}</span>
                    <div class="fx-cer gap-4">
                      <a
                        class="media-btn wh-8"
                        target="_blank"
                        :href="reportData.researcherProfile.researcherInfo.twitter"
                        v-if="reportData.researcherProfile.researcherInfo.twitter"
                      >
                        <div class="i-proicons:x-twitter wh-6"></div>
                      </a>
                      <a
                        class="media-btn wh-8"
                        target="_blank"
                        :href="reportData.researcherProfile.researcherInfo.github"
                        v-if="reportData.researcherProfile.researcherInfo.github"
                      >
                        <div class="i-ri:github-fill wh-6"></div>
                      </a>
                      <a
                        class="media-btn wh-8"
                        target="_blank"
                        :href="reportData.researcherProfile.researcherInfo.scholar"
                        v-if="reportData.researcherProfile.researcherInfo.scholar"
                      >
                        <div class="i-solar:square-academic-cap-bold wh-6"></div>
                      </a>
                    </div>
                  </div>
                  <div
                    class="fx-cer gap-1.5 text-sm text-[#7C7C7C] dark:text-[#7A7A7A]"
                    v-if="
                      reportData.researcherProfile.dataBlocks.roleModel.position ||
                      reportData.researcherProfile.dataBlocks.roleModel.institution
                    "
                  >
                    <SvgIcon name="verified" />
                    <span
                      >{{ reportData.researcherProfile.dataBlocks.roleModel.position }}({{
                        reportData.researcherProfile.dataBlocks.roleModel.institution
                      }})</span
                    >
                  </div>
                </div>
              </div>
              <div
                class="bg-special-blush dark:bg-[#222222] text-gray-2100 dark:text-[#C6C6C6] py-2 px-3 rounded fx-cer gap-2 text-5"
                v-if="reportData.researcherProfile.dataBlocks.roleModel.achievement"
              >
                <SvgIcon name="medal" />
                <span class="text-sm">{{
                  reportData.researcherProfile.dataBlocks.roleModel.achievement
                }}</span>
              </div>
              <div
                class="bg-special-softPeach dark:bg-[#222222] leading-6 text-gray-2100 dark:text-[#C6C6C6] text-13px p-4 rounded"
                v-if="reportData.researcherProfile.dataBlocks.roleModel.similarityReason"
              >
                {{ reportData.researcherProfile.dataBlocks.roleModel.similarityReason }}
              </div>
            </div>
          </ReportCard>
          <ReportCard
            icon="coorperation"
            :title="reportData.researcherProfile.dataBlocks.closestCollaborator.blockTitle"
            :card-id="'closest-collaborator'"
            @share-popup="showPopup = true"
          >
            <!-- 有有效协作者数据时显示完整信息 -->
            <div class="flex flex-col h-full" v-if="hasValidCollaborator">
              <div class="fx-cer gap-4">
                <Avatar
                  :src="reportData.researcherProfile.dataBlocks.closestCollaborator.avatar || '/image/avator.png'"
                  :size="70"
                />
                <div class="flex flex-col gap-2.5">
                  <div class="text-2xl font-bold">
                    {{ reportData.researcherProfile.dataBlocks.closestCollaborator.fullName }}
                  </div>
                  <div class="fx-cer gap-1.5 text-sm text-[#7C7C7C] dark:text-[#7A7A7A]" v-if="reportData.researcherProfile.dataBlocks.closestCollaborator.affiliation && reportData.researcherProfile.dataBlocks.closestCollaborator.affiliation !== 'N/A'">
                    <SvgIcon name="verified" />
                    <span>{{ reportData.researcherProfile.dataBlocks.closestCollaborator.affiliation }}</span>
                  </div>
                  <div class="fx-cer gap-1.5 text-sm text-gray-900">
                    <span
                      >Number of co-authored papers:
                      {{
                        reportData.researcherProfile.dataBlocks.closestCollaborator.coauthoredPapers
                      }}</span
                    >
                  </div>
                </div>
              </div>
              <div
                class="bg-special-softPeach dark:bg-[#222222] text-sm p-4 rounded border-l-4px border-primary-100 dark:border-[#654D43] mt-5 mb-4"
                v-if="reportData.researcherProfile.dataBlocks.closestCollaborator.bestCoauthoredPaper.title !== 'N/A'"
              >
                <div class="font-medium text-gray-1600 mb-3" v-if="reportData.researcherProfile.dataBlocks.closestCollaborator.bestCoauthoredPaper.fullVenue !== 'N/A'">
                  {{
                    reportData.researcherProfile.dataBlocks.closestCollaborator.bestCoauthoredPaper
                      .fullVenue
                  }}
                </div>
                <div class="border-t border-[#F2E8E4] dark:border-[#F2E8E41A] my-4" v-if="reportData.researcherProfile.dataBlocks.closestCollaborator.bestCoauthoredPaper.fullVenue !== 'N/A'"></div>
                <div class="font-medium text-gray-2200">
                  {{
                    reportData.researcherProfile.dataBlocks.closestCollaborator.bestCoauthoredPaper
                      .title
                  }}
                  <span v-if="reportData.researcherProfile.dataBlocks.closestCollaborator.bestCoauthoredPaper.year">
                    {{ reportData.researcherProfile.dataBlocks.closestCollaborator.bestCoauthoredPaper.year }}
                  </span>
                </div>
                <div class="flex justify-end gap-4 text-sm text-black dark:text-white mt-4" v-if="reportData.researcherProfile.dataBlocks.closestCollaborator.bestCoauthoredPaper.citations > 0">
                  <div class="fx-cer gap-2">
                    <SvgIcon name="quot" />
                    <span
                      >Citations :
                      {{
                        reportData.researcherProfile.dataBlocks.closestCollaborator
                          .bestCoauthoredPaper.citations.toLocaleString()
                      }}</span
                    >
                  </div>
                </div>
              </div>
              <div
                class="bg-special-softPeach dark:bg-[#222222] leading-6 text-gray-2100 text-13px p-4 rounded"
                v-if="
                  reportData.researcherProfile.dataBlocks.closestCollaborator.connectionAnalysis
                "
              >
                {{ reportData.researcherProfile.dataBlocks.closestCollaborator.connectionAnalysis }}
              </div>
            </div>
            
            <!-- 没有有效协作者数据时显示友好提示 -->
            <div class="flex flex-col h-full items-center justify-center text-center" v-else>
              <div class="mb-4">
                <Avatar
                  src="/image/avator.png"
                  :size="70"
                />
              </div>
              <div class="text-lg font-medium text-gray-600 dark:text-gray-400 mb-2">
                No Frequent Collaborator Found
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-500 max-w-xs">
                This researcher appears to work independently or has not published enough collaborative work to identify a closest collaborator.
              </div>
            </div>
          </ReportCard>
          <ReportCard
            icon="wallet"
            :title="reportData.researcherProfile.dataBlocks.estimatedSalary.blockTitle"
            :x="-50"
            :card-id="'estimated-salary'"
            @share-popup="showPopup = true"
          >
            <div class="flex flex-col justify-between h-full">
              <div>
                <div class="text-center text-black dark:text-white text-11 font-bold" style="font-family: 'Poppins'; font-weight: 600; font-size: 44px;">
                  ${{
                    formatSalaryDisplay(
                      reportData.researcherProfile.dataBlocks.estimatedSalary.earningsPerYearUSD
                    ) || 0
                  }}
                </div>
                <div class="text-center text-gray-900 text-sm mt-2 mb-4">Eamnings Per Year</div>
              </div>
              <div class="bg-special-softPeach dark:bg-[#222222] p-4 flex-1" style="border-radius: 4px;">
                <div class="text-gray-1000 text-sm">
                  {{ reportData.researcherProfile.dataBlocks.estimatedSalary.reasoning }}
                </div>
              </div>
            </div>
          </ReportCard>
          <ReportCard
            icon="analytics"
            :title="reportData.researcherProfile.dataBlocks.researcherCharacter.blockTitle"
            :card-id="'researcher-character'"
            @share-popup="showPopup = true"
          >
            <div class="flex flex-col h-full">
              <div class="space-y-5 mb-5">
                <div class="space-y-2.5">
                  <div class="flex items-center justify-between">
                    <div class="fx-cer gap-1.5 text-sm text-gray-2000">
                      <SvgIcon name="react" />
                      <span class="text-xs">Theoretical Research</span>
                    </div>
                    <div class="fx-cer gap-1.5 text-sm text-gray-2000">
                      <span class="text-xs">Applied Research</span>
                      <SvgIcon name="completed-task" />
                    </div>
                  </div>
                  <Progress
                    :percentage="
                      reportData.researcherProfile.dataBlocks.researcherCharacter.theoryVsPractice *
                      10
                    "
                  />
                </div>
                <div class="space-y-2.5">
                  <div class="flex items-center justify-between">
                    <div class="fx-cer gap-1.5 text-sm text-gray-2000">
                      <SvgIcon name="book" />
                      <span class="text-xs">Academic Depth</span>
                    </div>
                    <div class="fx-cer gap-1.5 text-sm text-gray-2000">
                      <span class="text-xs">Academic Breadth</span>
                      <img 
                        :src="isDark ? '/image/AcademicBreadth-dark.svg' : '/image/AcademicBreadth-light.svg'" 
                        alt="Academic Breadth" 
                        class="w-3.5 h-3.5"
                      />
                    </div>
                  </div>
                  <Progress
                    :percentage="
                      reportData.researcherProfile.dataBlocks.researcherCharacter.depthVsBreadth *
                      10
                    "
                  />
                </div>
                <div class="space-y-2.5">
                  <div class="flex items-center justify-between">
                    <div class="fx-cer gap-1.5 text-sm text-gray-2000">
                      <SvgIcon name="flag" />
                      <span class="text-xs">Independent Research</span>
                    </div>
                    <div class="fx-cer gap-1.5 text-sm text-gray-2000">
                      <span class="text-xs">Team Collaboration</span>
                      <SvgIcon name="team" />
                    </div>
                  </div>
                  <Progress
                    :percentage="
                      reportData.researcherProfile.dataBlocks.researcherCharacter.soloVsTeamwork *
                      10
                    "
                  />
                </div>
              </div>
              <div
                class="flex-1 bg-primary-100 bg-special-softPeach dark:bg-[#222222] p-4 text-sm text-gray-2100 leading-6"
                style="border-radius: 4px;"
                v-if="reportData.researcherProfile.dataBlocks.researcherCharacter.justification"
              >
                {{ reportData.researcherProfile.dataBlocks.researcherCharacter.justification }}
              </div>
            </div>
          </ReportCard>
          <!-- News  -->
          <ReportCard
            icon="contract"
            :title="reportData.researcherProfile.dataBlocks.representativePaper.blockTitle"
            :card-id="'representative-paper'"
            :x="-50"
            @share-popup="showPopup = true"
          >
            <div class="flex flex-col h-full gap-4">
              <div
                class="border-l-4px border-primary-100 dark:border-[#654D43] bg-special-softPeach dark:bg-[#222222] p-4 rounded"
              >
                <div class="fx-cer gap-4 text-limit-2">
                  <Avatar :src="reportData.researcherProfile.researcherInfo.avatar" :size="50" />
                  <span class="font-bold text-2xl text-limit-2"
                    >{{ reportData.researcherProfile.dataBlocks.representativePaper.venue }}
                    <!-- {{ reportData.researcherProfile.dataBlocks.representativePaper.year }} -->
                  </span>
                </div>
                <div class="bg-gray-1900 h-1px my-4"></div>
                <div>
                  <div class="font-bold text-lg mb-4">
                    {{ reportData.researcherProfile.dataBlocks.representativePaper.title }}
                  </div>
                  <div
                    class="flex justify-end gap-4 text-sm text-black dark:text-white"
                    v-if="reportData.researcherProfile.dataBlocks.representativePaper.citations"
                  >
                    <div class="fx-cer gap-2 dark:text-white">
                      <SvgIcon name="quot" />
                      <span
                        >Citations :
                        {{
                          reportData.researcherProfile.dataBlocks.representativePaper.citations.toLocaleString()
                        }}</span
                      >
                    </div>
                    <div
                      class="fx-cer gap-2 dark:text-white"
                      v-if="
                        reportData.researcherProfile.dataBlocks.representativePaper.authorPosition
                      "
                    >
                      <SvgIcon name="author" />
                      <span
                        >Author Position :
                        {{
                          formatAuthorPosition(reportData.researcherProfile.dataBlocks.representativePaper.authorPosition)
                        }}</span
                      >
                    </div>
                  </div>
                </div>
              </div>
              <!-- <div class="flex-1 border-primary-100 bg-special-softPeach p-4 rounded" v-if="reportData.researcherProfile.dataBlocks.representativePaper.venue">
                <div class="text-gray-1800 text-sm">
                  {{ reportData.researcherProfile.dataBlocks.representativePaper.venue }}
                </div>
              </div> -->
              <div
                class="flex-1 flex flex-col bg-[#FAF2EF] dark:bg-[#292929] p-6"
                style="border-radius: 4px;"
                v-if="
                  reportData.researcherProfile.dataBlocks?.representativePaper?.paper_news?.news ||
                  ''
                "
              >
                <!-- News Title and Date -->
                <div class="flex flex-col gap-2 mb-4">
                  <a
                    :href="reportData.researcherProfile.dataBlocks.representativePaper.paper_news.url"
                    target="_blank"
                    class="text-[#1E1F25] dark:text-[#C6C6C6] w-full hover:text-primary-100 transition-colors cursor-pointer"
                    style="font-family: Poppins; font-weight: 700; font-size: 16px; line-height: 24px; letter-spacing: 0%;"
                  >
                    {{
                      reportData.researcherProfile.dataBlocks.representativePaper.paper_news.news
                    }}
                  </a>
                  <span class="text-sm text-gray-500 text-right">{{
                    reportData.researcherProfile.dataBlocks.representativePaper.paper_news.date ||
                    ''
                  }}</span>
                </div>

                <!-- News Content -->
                <p class="text-[#666260] dark:text-[#C6C6C6] leading-relaxed flex-1" style="font-family: Poppins; font-weight: 400; font-size: 14px; line-height: 20px; letter-spacing: 0%;">
                  {{
                    reportData.researcherProfile.dataBlocks.representativePaper.paper_news
                      .description || ''
                  }}
                </p>
              </div>
            </div>
          </ReportCard>
          <ReportCard
            icon="mail"
            :title="reportData.researcherProfile.dataBlocks.criticalReview.blockTitle"
            :card-id="'critical-review'"
            @share-popup="showPopup = true"
          >
            <div
              class="h-full bg-special-softPeach dark:bg-[#222222] p-4 text-15px text-gray-1000 leading-7"
              style="border-radius: 4px;"
            >
              {{ reportData.researcherProfile.dataBlocks.criticalReview.evaluation }}
            </div>
          </ReportCard>
        </div>
        <motion.div
          class="technical bg-gray-100 dark:bg-gray-800 rounded-4!"
          :initial="{ opacity: 0 }"
          :in-view="{ opacity: 1 }"
          :transition="{ duration: 1, delay: 0.5 }"
          :in-view-options="{ once: true }"
        >
          <div class="text-42px font-bold pl-20 whitespace-nowrap max-w-100">I'm Researcher</div>
          <div class="max-w-200 pl-10 pr-25 text-[#433D3A] dark:text-[#FAF9F5]" style="font-family: Poppins; font-weight: 400; font-size: 16px; line-height: 30px; letter-spacing: 0px;">
            DINQ delivers accurate and timely comprehensive information on AI researchers, assisting headhunters and recruiters in gaining a deeper understanding of AI talent, and provides seamless API and code integration solutions.
          </div>
        </motion.div>
        <motion.div
          class="flex flex-col items-center justify-center"
          :initial="{ opacity: 0, y: 10 }"
          :in-view="{ opacity: 1, y: 0 }"
          :transition="{ duration: 1, delay: 0.5 }"
          :in-view-options="{ once: true }"
        >
          <div
            class="font-semibold text-14 max-w-155 text-center clash-semibold mt-9 mb-7.5 text-black dark:text-white"
          >
            Try with other<br />
            AI researchers profile
          </div>
          <motion.div
            :initial="{ opacity: 0, y: 10 }"
            :animate="{ opacity: 1, y: 0 }"
            :transition="{ duration: 0.3, ease: 'easeInOut', delay: 0.2 }"
            class="f-cer mt-7.5 mb-7"
          >
            <div
              class="custom-input border rounded-full bg-white border-black min-h-16 w-195 p-1 flex items-center justify-between gap-4 pl-7.5"
            >
              <SearchInput ref="searchInputRef" @enter-search="handleSearch" />
              <ActionButton @click="handleSearch" :imgSrc="magicWand" buttonText="Analyze" />
            </div>
          </motion.div>
          <!-- <div
            class="border rounded-full bg-white border-black min-h-16 w-190 p-1 flex items-center justify-between gap-4 pl-7.5"
          >
            <SearchInput ref="searchInputRef" @enter-search="handleSearch" />
            <ActionButton @click="handleSearch" :imgSrc="magicWand" buttonText="Analyze" />
          </div> -->
          <div class="text-sm text-center text-neutral-100 dark:text-white mt-7.5 mb-10">
            By clicking "Analyze," you agree to our
            <a href="/terms" target="_blank" class="text-primary-100 hover:underline"
              >Terms of Service</a
            >
          </div>
        </motion.div>
      </div>
    </template>
    <InviteCodeModal
      v-if="showInviteModal"
      :error="inviteError"
      :loading="inviteLoading"
      @close="showInviteModal = false"
      @submit="handleSubmitActivationCode"
      @waiting-list="onShowWaitingListModal"
    />
    <WaitingListModal
      v-if="showWaitingListModal"
      @close="showWaitingListModal = false"
      @back="onBackToInviteCode"
    />
    <div
      v-if="inviteSuccess"
      class="fixed inset-0 z-50 flex items-center justify-center bg-black/40"
    >
      <div
        class="bg-white dark:bg-[#1a1a1b] rounded-2xl shadow-xl p-8 pt-7 pb-7 w-full max-w-96 relative text-center"
      >
        <div class="text-lg font-semibold text-black dark:text-white mb-6">
          Enter Invite Code
          <button
            class="absolute top-6 right-6 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors text-xl"
            @click="inviteSuccess = false"
          >
            <svg
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 5L5 15M5 5L15 15"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
              />
            </svg>
          </button>
        </div>
        <div class="flex justify-center mb-4">
          <div class="bg-green-500 rounded-full w-12 h-12 flex items-center justify-center mx-auto">
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
              <circle cx="16" cy="16" r="16" fill="none" />
              <path
                d="M10 17.5L14 21.5L22 13.5"
                stroke="#fff"
                stroke-width="2.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
        </div>
        <div class="text-xl font-bold text-black dark:text-white mb-2">Verification Successful</div>
        <div class="text-base text-gray-700 dark:text-gray-300 mb-7">
          Congratulations! Your invitation code has been verified successfully
        </div>
        <button
          class="w-full h-12 bg-black dark:bg-white text-white dark:text-black rounded-lg font-semibold text-base transition hover:bg-gray-900 dark:hover:bg-gray-200"
          @click="goHome"
        >
          Ok
        </button>
      </div>
    </div>
  </div>
  <NotFound
    :visible="!loading && !reportData && !isFetching"
    @update:visible="router.replace('/analysis')"
  />
</template>

<script setup lang="ts">
  import { useRoute } from 'vue-router'
  import { motion } from 'motion-v'
  import stars from '@/assets/image/stars.png'
  import magicWand from '@/assets/image/magic-wand.png'
  import InviteCodeModal from '@/components/InviteCodeModal.vue'
  import WaitingListModal from '@/components/WaitingListModal.vue'
  import { ref, computed, onMounted, watch, nextTick } from 'vue'
  import { submitActivationCode } from '~/api'
  import { getPredictableScholarOgImageUrl, extractScholarId, uploadFileToS3, checkScholarOgImageExists } from '~/utils'
  import html2canvas from 'html2canvas-pro'

  definePageMeta({
    middleware: 'auth',
  })

  // 从 URL 获取查询参数用于初始 SEO
  const route = useRoute()
  const query = route.query.query as string

  // 设置初始 SEO meta（在服务端渲染时生效）
  if (query) {
    const initialTitle = `${query} - Scholar Analysis | DINQ`
    const initialDescription = `View ${query}'s academic profile, research insights, and scholarly contributions on DINQ.`

    // 尝试提取 Scholar ID 用于可预测的 OG 图片 URL
    const possibleScholarId = extractScholarId(query)
    const predictableOgImageUrl = possibleScholarId
      ? getPredictableScholarOgImageUrl(possibleScholarId)
      : 'https://dinq.io/og-image.png'

    useSeoMeta({
      title: initialTitle,
      description: initialDescription,
      keywords: `${query}, Scholar Analysis, Academic Profile, Research Insights, Academic Publications, Citation Analysis`,

      // Open Graph - 优先使用可预测的 OG 图片 URL
      ogTitle: initialTitle,
      ogDescription: initialDescription,
      ogType: 'website',
      ogImage: predictableOgImageUrl,

      // Twitter Card
      twitterCard: 'summary_large_image',
      twitterTitle: initialTitle,
      twitterDescription: initialDescription,
      twitterImage: predictableOgImageUrl,
    })

    useHead({
      title: initialTitle,
      meta: [
        {
          name: 'robots',
          content: 'index, follow'
        },
        {
          name: 'author',
          content: 'DINQ'
        }
      ]
    })
  }

  const searchInputRef = ref()
  const compareInputRef = ref()
  const { currentUser } = useFirebaseAuth()
  const router = useRouter()
  const papersItems = ref<{ label: string; value: any }[]>([])
  const insightsItems = ref<{ label: string; value: any }[]>([])
  const isFetching = ref(false)
  const showInviteModal = ref(false)
  const inviteError = ref('')
  const inviteLoading = ref(false)
  const inviteSuccess = ref(false)
  const showWaitingListModal = ref(false)
  const showPopup = ref(false)
  const isDark = ref(false)
  onMounted(() => {
    isDark.value = document.documentElement.classList.contains('dark')
    const observer = new MutationObserver(() => {
      isDark.value = document.documentElement.classList.contains('dark')
    })
    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] })
  })

  // 移除硬编码数据，改为计算属性
  const shareCardUser = computed(() => {
    if (!reportData.value?.researcherProfile?.researcherInfo) return {
      name: '',
      avatar: '/image/avator.png',
      role: 'Researcher',
      papers: 0,
      citations: 0,
    }
    const info = reportData.value.researcherProfile.researcherInfo
    return {
      name: info.name || '',
      avatar: info.avatar || '/image/avator.png',
      role: info.affiliation || 'Researcher',
      papers: reportData.value.researcherProfile.dataBlocks.publicationStats?.totalPapers || 0,
      citations: info.totalCitations || 0,
    }
  })

  const shareCardStats = computed(() => {
    if (!reportData.value?.researcherProfile?.dataBlocks) return {
      firstAuthor: 0,
      total: 0,
      citation: 0,
    }
    const blocks = reportData.value.researcherProfile.dataBlocks
    return {
      firstAuthor: blocks.publicationInsight?.firstAuthorPapers || 0,
      total: blocks.publicationStats?.totalPapers || 0,
      citation: blocks.publicationInsight?.firstAuthorCitations || 0,
    }
  })

  const shareCardRoleModel = computed(() => {
    if (!reportData.value?.researcherProfile?.dataBlocks?.roleModel) return {
      name: 'Unknown',
      avatar: '/image/avator.png',
      title: 'Researcher',
      achievement: '',
    }
    const roleModel = reportData.value.researcherProfile.dataBlocks.roleModel
    return {
      name: roleModel.name || 'Unknown',
      avatar: roleModel.photoUrl || '/image/avator.png',
      title: roleModel.position || 'Researcher',
      achievement: roleModel.achievement || '',
    }
  })

  const shareCardIncome = computed(() => {
    return reportData.value?.researcherProfile?.dataBlocks?.estimatedSalary?.earningsPerYearUSD || 0
  })

  // 格式化数字，超过1K时显示为K单位，超过1M时显示为M单位
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(0) + 'K'
    }
    return num.toString()
  }

  // 格式化薪资显示，将 - 替换为 ~ 并应用数字格式化
  const formatSalaryDisplay = (salaryRange: string | number): string => {
    if (!salaryRange) return ''
    
    // 如果是数字，直接格式化
    if (typeof salaryRange === 'number') {
      return formatNumber(salaryRange)
    }
    
    // 先替换分隔符
    let formatted = salaryRange.replace(/-/g, '~')
    
    // 匹配薪资数字并格式化
    // 匹配格式如: "50000~80000 USD/month", "$50000~80000/month", "50000 USD/month"
    formatted = formatted.replace(/(\d+(?:,\d{3})*)/g, (match) => {
      const num = parseInt(match.replace(/,/g, ''))
      return formatNumber(num)
    })
    
    return formatted
  }

  // 格式化作者位置显示
  const formatAuthorPosition = (position: number | null | undefined): string => {
    if (position === null || position === undefined) return ''
    
    // 如果是-1或者不是1、2、3，显示3+
    if (position === -1 || ![1, 2, 3].includes(position)) {
      return '3+'
    }
    
    return position.toString()
  }

  // 检查是否有有效的协作者数据
  const hasValidCollaborator = computed(() => {
    if (!reportData.value?.researcherProfile?.dataBlocks?.closestCollaborator) return false
    
    const collaborator = reportData.value.researcherProfile.dataBlocks.closestCollaborator
    
    // 检查是否是"No frequent collaborator found"或其他无效数据
    return collaborator.fullName !== "No frequent collaborator found" &&
           collaborator.fullName !== "N/A" &&
           collaborator.coauthoredPapers > 0
  })

  // 移除硬编码数据
  // const user = {
  //   name: 'Daiheng Gao',
  //   avatar: '/avatar.jpg',
  //   role: 'OpenAI-Researcher',
  //   papers: 1177,
  //   citations: 1177,
  // }

  // const stats = {
  //   firstAuthor: 99,
  //   total: 177,
  //   citation: 37,
  // }

  // const roleModel = {
  //   name: 'Marc Andreessen',
  //   avatar: '/marc.jpg',
  //   title: 'OpenAI-Researcher',
  //   achievement: 'Image transformer (2018)',
  // }

  // debug
  const initData = ref({
    researcherProfile: {
      researcherInfo: {
        name: 'Daiheng Gao',
        abbreviatedName: 'D Gao',
        affiliation: 'Independent researcher',
        email: null,
        avatar: 'https://scholar.googleusercontent.com/citations?view_op=view_photo&user=example&citpid=1',
        description: 'Daiheng Gao is an independent researcher specializing in AI Safety and Image/Video Generation. His work focuses on developing safe and reliable AI systems with particular expertise in generative models and computer vision applications.',
        researchFields: ['AI Safety', 'Image/Video Generation'],
        totalCitations: 693,
        citations5y: 687,
        hIndex: 9,
        hIndex5y: 9,
        yearlyCitations: {
          '2020': 8,
          '2021': 60,
          '2022': 98,
          '2023': 188,
          '2024': 265,
          '2025': 67,
        },
      },
      dataBlocks: {
        publicationStats: {
          blockTitle: 'Papers',
          totalPapers: 22,
          totalCitations: 693,
          hIndex: 9,
          yearlyCitations: {
            '2020': 8,
            '2021': 60,
            '2022': 98,
            '2023': 188,
            '2024': 265,
            '2025': 67,
          },
          yearlyPapers: {
            '2018': 1,
            '2019': 2,
            '2020': 1,
            '2021': 3,
            '2022': 4,
            '2023': 4,
            '2024': 3,
            '2025': 3,
          },
        },
        publicationInsight: {
          blockTitle: 'Insight',
          totalPapers: 22,
          topTierPapers: 9,
          firstAuthorPapers: 10,
          firstAuthorCitations: 71,
          totalCoauthors: 81,
          lastAuthorPapers: 1,
          conferenceDistribution: {
            CVPR: 2,
            ICCV: 1,
            'ACM MM': 3,
            ICASSP: 1,
            NeurIPS: 1,
            Others: 10,
          },
        },
        roleModel: {
          blockTitle: 'Role Model',
          found: true,
          name: 'Ian Goodfellow',
          institution: 'DeepMind',
          position: 'Research Scientist',
          photoUrl:
            'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTU27asjcoMNOa87t7UHD8l0wRi1jrRlYYi7w&s',
          achievement: 'Generative Adversarial Net 2014',
          similarityReason:
            "Ian Goodfellow is a leading expert in the field of AI Safety and has made significant contributions to the field of image/video generation through his work on Generative Adversarial Networks (GANs), which aligns with Daiheng Gao's research interests. Goodfellow's influential paper on GANs has been highly cited, demonstrating the impact of his work, similar to Gao's most cited paper in the same domain.",
        },
        closestCollaborator: {
          blockTitle: 'Closest Collaborator',
          fullName: 'Bang Zheng',
          affiliation:
            'London School of Hygiene & Tropical Medicine; University of Edinburgh; Imperial College London',
          researchInterests: [],
          scholarId: 'uaQdhE4AAAAJ',
          coauthoredPapers: 9,
          bestCoauthoredPaper: {
            title:
              'Multi-view consistent generative adversarial networks for 3d-aware image synthesis',
            year: 2022,
            venue: 'Long Venue Name',
            fullVenue:
              'Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern …, 2022',
            citations: 55,
          },
          connectionAnalysis: null,
        },
        estimatedSalary: {
          blockTitle: 'Estimated Salary',
          earningsPerYearUSD: 215500,
          levelEquivalency: {
            us: 'Google L6',
            cn: '阿里 P8',
          },
          reasoning:
            '基于学者的h指数(9)、总引用数(693)、发表论文数量(22)以及顶级会议/期刊论文数量(9)综合评估得出。',
        },
        researcherCharacter: {
          blockTitle: 'Researcher Character',
          depthVsBreadth: 6,
          theoryVsPractice: 4,
          soloVsTeamwork: 3,
          justification: null,
        },
        representativePaper: {
          blockTitle: 'Representative Paper',
          title:
            'Multi-view consistent generative adversarial networks for 3d-aware image synthesis',
          year: 2022,
          venue: 'Long Venue Name',
          fullVenue:
            'Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern …, 2022',
          citations: 55,
          authorPosition: null,
          paper_news: {
            news: 'Scikit-learn: Machine Learning in Python - DataScienceToday',
            date: '2025-04-11',
            description:
              'A news and editorial feature discussing the ongoing impact and popularity of the scikit-learn library. Highlights its wide adoption in scientific computing and industry, its streamlined dependencies, and its integration with the Python scientific stack. The article outlines the features and ecosystem benefits of scikit-learn for both academia and practical applications.',
            url: 'https://datasciencetoday.net/index.php/en-us/machine-learning/109-ml-sup/132-scikit-learn-machine-learning-python',
          },
        },
        criticalReview: {
          blockTitle: 'Roast',
          evaluation:
            "Daiheng Gao's profile is a mixed bag. High h-index, yet only 1 last-author paper? Significant top-tier publications, but heavy reliance on Arxiv suggests a penchant for preprints over peer review—time to step up the game.",
        },
      },
      configInfo: {
        comment: 'Placeholder for bottom/page configuration data',
      },
    },
  })
  const reportData = ref()
  const { thinking, reportDataInfo, connect, loading, limitInfo } = useEventStream()

  // OG 图片相关状态
  const ogImageUrl = ref('')
  const ogImageGenerated = ref(false)
  const hiddenShareCardRef = ref()

  // 动态 SEO 元数据更新（数据加载后的增强版本）
  const updateScholarSeoMeta = (data: any) => {
    const researcherInfo = data.researcherProfile?.researcherInfo
    if (!researcherInfo) return

    const scholarName = researcherInfo.name || 'Scholar'
    const affiliation = researcherInfo.affiliation || ''
    const researchFields = researcherInfo.researchFields?.join(', ') || ''
    const totalCitations = researcherInfo.totalCitations || 0
    const hIndex = researcherInfo.hIndex || 0

    const title = `${scholarName} - Scholar Analysis | DINQ`
    const description = `${scholarName}${affiliation ? ` from ${affiliation}` : ''} has ${totalCitations} citations and h-index of ${hIndex}. Research areas: ${researchFields || 'Various fields'}.`

    // 构建关键词
    const keywords = [
      scholarName,
      'Scholar Analysis',
      'Academic Profile',
      'Research Insights',
      'Citation Analysis',
      'Academic Publications',
      affiliation,
      ...researcherInfo.researchFields || []
    ].filter(Boolean).join(', ')

    useSeoMeta({
      title,
      description: description.slice(0, 160), // 限制描述长度
      keywords,

      // Open Graph
      ogTitle: title,
      ogDescription: description.slice(0, 160),
      ogImage: ogImageUrl.value || researcherInfo.avatar || 'https://dinq.io/og-image.png',

      // Twitter Card
      twitterCard: 'summary_large_image',
      twitterTitle: title,
      twitterDescription: description.slice(0, 160),
      twitterImage: ogImageUrl.value || researcherInfo.avatar || 'https://dinq.io/og-image.png',

      // 额外的 meta 标签
      author: scholarName,
    })

    // 设置页面标题
    useHead({
      title,
      meta: [
        {
          name: 'robots',
          content: 'index, follow'
        },
        {
          name: 'author',
          content: scholarName
        }
      ]
    })
  }

  // 动态更新 OG 图片 meta 标签
  const updateSeoMetaWithOgImage = (ogImageUrl: string) => {
    if (!reportData.value) return

    const researcherInfo = reportData.value.researcherProfile?.researcherInfo
    if (!researcherInfo) return

    const scholarName = researcherInfo.name || 'Scholar'
    const currentDomain = import.meta.client ? window.location.origin : 'https://dinq.io'
    const pageUrl = `${currentDomain}/report?query=${encodeURIComponent(query)}`

    useSeoMeta({
      ogImage: ogImageUrl,
      twitterImage: ogImageUrl,
      ogUrl: pageUrl,
    })

    useHead({
      meta: [
        { property: 'og:image', content: ogImageUrl, key: 'og:image' },
        { name: 'twitter:image', content: ogImageUrl, key: 'twitter:image' },
        { property: 'og:url', content: pageUrl, key: 'og:url' }
      ]
    })

    if (import.meta.client) {
      nextTick(() => {
        let ogImageMeta = document.querySelector('meta[property="og:image"]') as HTMLMetaElement
        if (!ogImageMeta) {
          ogImageMeta = document.createElement('meta')
          ogImageMeta.setAttribute('property', 'og:image')
          document.head.appendChild(ogImageMeta)
        }
        ogImageMeta.setAttribute('content', ogImageUrl)

        let twitterImageMeta = document.querySelector('meta[name="twitter:image"]') as HTMLMetaElement
        if (!twitterImageMeta) {
          twitterImageMeta = document.createElement('meta')
          twitterImageMeta.setAttribute('name', 'twitter:image')
          document.head.appendChild(twitterImageMeta)
        }
        twitterImageMeta.setAttribute('content', ogImageUrl)

        console.log('已强制更新 Scholar OG 图片 meta 标签:', ogImageUrl)
      })
    }
  }

  // Scholar 图片处理逻辑（参考 GitHub 实现）
  const handleScholarImageProcessing = (clonedDoc: Document, isDarkMode: boolean) => {
    const clonedElement = clonedDoc.querySelector('[data-card-id="share-card"]')
    if (!clonedElement) return

    // 0. 移除外层遮罩层，只保留卡片内容
    const parentElement = clonedElement.parentElement
    if (parentElement && parentElement.classList.contains('fixed')) {
      // 如果父元素是遮罩层，将卡片移到body下
      clonedDoc.body.appendChild(clonedElement)
      parentElement.remove()
    }

    // 1. 替换操作按钮为版权信息和二维码
    const buttonContainer = clonedElement.querySelector('[data-action-buttons]')
    if (buttonContainer) {
      const bottomRightContainer = clonedDoc.createElement('div')
      bottomRightContainer.style.cssText = 'position: absolute; bottom: 24px; right: 16px; display: flex; align-items: center; gap: 12px; z-index: 10;'

      // 创建版权信息
      const copyrightDiv = clonedDoc.createElement('div')
      copyrightDiv.textContent = 'Copyright @ 2025 DINQ Inc. All rights reserved'
      copyrightDiv.style.cssText = `font-size: 14px; color: ${isDarkMode ? '#A0A0A0' : '#666'}; font-family: 'Poppins', sans-serif;`

      // 创建二维码元素
      const qrCode = clonedDoc.createElement('img')
      qrCode.src = '/image/qrcode.png'
      qrCode.alt = 'QR Code'
      qrCode.style.cssText = 'width: 60px; height: 60px; flex-shrink: 0;'

      // 将版权信息和二维码添加到右下角容器
      bottomRightContainer.appendChild(copyrightDiv)
      bottomRightContainer.appendChild(qrCode)

      buttonContainer.parentNode?.replaceChild(bottomRightContainer, buttonContainer)
    }

    // 2. 替换 SVG 图标为 PNG 图片（参考 GitHub 实现）
    const svgElements = clonedElement.querySelectorAll('svg')
    svgElements.forEach(svgElement => {
      const iconId = svgElement.id
      const imgElement = clonedDoc.createElement('img')

      // Scholar 特有的图标映射
      const iconMappings: Record<string, { src: string; alt: string; className: string }> = {
        '#icon-research': { src: '/image/share-role.png', alt: 'research', className: 'w-4 h-4' },
        '#icon-insight': { src: '/image/share-overview.png', alt: 'insight', className: 'w-4 h-4' },
        '#icon-salary': { src: '/image/share-salary.png', alt: 'salary', className: 'w-4 h-4' },
        '#icon-medal': { src: '/image/share-medal.png', alt: 'medal', className: 'w-4 h-4' },
        '#icon-verified': { src: '/image/share-ver.png', alt: 'verified', className: 'w-4 h-4' }
      }

      const mapping = iconMappings[iconId]
      if (mapping) {
        imgElement.src = mapping.src
        imgElement.alt = mapping.alt
        imgElement.className = mapping.className
        svgElement.parentNode?.replaceChild(imgElement, svgElement)
      }
    })

    // 3. 确保所有Scholar分享卡片的图标都正确显示
    // 检查并修复可能缺失的图标
    const iconImages = clonedElement.querySelectorAll('img[src*="/image/share-"]')
    iconImages.forEach(img => {
      const imgEl = img as HTMLImageElement
      // 确保图标尺寸正确
      if (imgEl.src.includes('share-')) {
        imgEl.style.width = '16px'
        imgEl.style.height = '16px'
        imgEl.style.flexShrink = '0'
      }
    })

    // 4. 修复头像路径
    const avatarImages = clonedElement.querySelectorAll('img[src*="@/assets/image/avator.png"]')
    avatarImages.forEach(img => {
      const imgEl = img as HTMLImageElement
      imgEl.src = '/image/avator.png'
    })

    // 5. 处理 DonutChart 的 Canvas 元素
    const canvasElements = clonedElement.querySelectorAll('canvas')
    canvasElements.forEach(canvas => {
      try {
        const dataURL = canvas.toDataURL('image/png')
        const img = clonedDoc.createElement('img')
        img.src = dataURL
        img.style.width = canvas.style.width || `${canvas.width}px`
        img.style.height = canvas.style.height || `${canvas.height}px`
        img.style.display = 'block'
        canvas.parentNode?.replaceChild(img, canvas)
      } catch (error) {
        console.warn('Failed to convert canvas to image:', error)
      }
    })

    // 6. 修复毛玻璃效果卡片的背景和样式
    const glassCards = clonedElement.querySelectorAll('[style*="backdrop-filter: blur(34px)"]')
    glassCards.forEach(card => {
      const cardEl = card as HTMLElement
      if (isDarkMode) {
        cardEl.style.backgroundColor = 'rgba(20, 20, 21, 0.85)'
        cardEl.style.backdropFilter = 'none'
        cardEl.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.3)'
      } else {
        cardEl.style.backgroundColor = 'rgba(255, 255, 255, 0.85)'
        cardEl.style.backdropFilter = 'none'
        cardEl.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.1)'
      }
    })

    // 7. 修复深色模式下的边框和文字颜色
    if (isDarkMode) {
      // 修复所有卡片的边框颜色
      const cardElements = clonedElement.querySelectorAll('.border-gray-200')
      cardElements.forEach(card => {
        (card as HTMLElement).style.borderColor = '#27282D'
      })

      // 修复主卡片的边框颜色
      const mainCard = clonedElement.querySelector('[data-card-id="share-card"]')
      if (mainCard) {
        (mainCard as HTMLElement).style.borderColor = '#27282D'
      }

      // 修复卡片标题文字颜色
      const titleElements = clonedElement.querySelectorAll('.text-black')
      titleElements.forEach(title => {
        (title as HTMLElement).style.color = '#FAF9F5'
      })
    }

    // 8. 确保主卡片背景渐变正确显示
    const mainCardDiv = clonedElement.querySelector('[data-card-id="share-card"]')
    if (mainCardDiv) {
      const cardEl = mainCardDiv as HTMLElement
      if (isDarkMode) {
        cardEl.style.background = 'linear-gradient(to bottom, #141415, #141415)'
        cardEl.style.borderColor = '#27282D'
      } else {
        cardEl.style.background = 'linear-gradient(to bottom, #FFFFFF, #F4F2F1)'
        cardEl.style.borderColor = 'transparent'
      }
      // 确保卡片有正确的尺寸和定位
      cardEl.style.position = 'relative'
      cardEl.style.display = 'block'
    }

    // 9. 修复Role Model部分的背景条显示问题
    const achievementElement = clonedElement.querySelector('[data-achievement-bg]')
    if (achievementElement) {
      const achievementEl = achievementElement as HTMLElement
      // 强制设置背景色，确保在截图时正确显示
      if (isDarkMode) {
        achievementEl.style.backgroundColor = '#222222'
        achievementEl.style.color = '#C6C6C6'
      } else {
        achievementEl.style.backgroundColor = '#FDF0EB'
        achievementEl.style.color = '#495160'
      }
      // 确保圆角和其他样式正确显示
      achievementEl.style.borderRadius = '4px'
      achievementEl.style.padding = '12px 8px'
      achievementEl.style.display = 'flex'
      achievementEl.style.alignItems = 'center'
      achievementEl.style.gap = '8px'
    }

    // 10. 修复Scholar分享卡片特有的样式问题
    // 确保所有文本元素在截图时可见
    const textElements = clonedElement.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, div')
    textElements.forEach(element => {
      const el = element as HTMLElement
      if (el.style.color === 'transparent' || el.style.opacity === '0') {
        if (isDarkMode) {
          el.style.color = '#FAF9F5'
        } else {
          el.style.color = '#030229'
        }
        el.style.opacity = '1'
      }
    })
  }

  // 生成 Scholar OG 图片
  const generateScholarOgImage = async (scholarId: string) => {
    if (ogImageGenerated.value || !import.meta.client) return

    try {
      console.log('开始生成 Scholar OG 图片...')

      // 等待 Vue 的响应式更新完成
      await nextTick()

      // 查找分享卡片元素 - 直接查找卡片内容，而不是外层容器
      let shareCardElement = document.querySelector('[data-card-id="share-card"]') as HTMLElement
      if (!shareCardElement) {
        console.warn('未找到分享卡片元素，无法生成 OG 图片')
        return
      }

      // 如果找到的是外层遮罩容器，查找实际的卡片元素
      if (shareCardElement.classList.contains('fixed')) {
        const actualCard = shareCardElement.querySelector('[data-card-id="share-card"]') as HTMLElement
        if (actualCard) {
          shareCardElement = actualCard
        }
      }

      console.log('找到分享卡片元素，尺寸:', {
        width: shareCardElement.offsetWidth,
        height: shareCardElement.offsetHeight,
        scrollWidth: shareCardElement.scrollWidth,
        scrollHeight: shareCardElement.scrollHeight,
        className: shareCardElement.className
      })

      // 等待图片资源加载完成（参考 GitHub 实现）
      const images = shareCardElement.getElementsByTagName('img')
      const imagePromises = [...images].map(img => {
        if (img.complete) return Promise.resolve()
        return new Promise(resolve => {
          img.onload = resolve
          img.onerror = resolve // 即使图片加载失败也继续
        })
      })
      await Promise.all(imagePromises)

      // 等待自定义字体加载完成
      if (document.fonts) {
        await document.fonts.ready
      }

      // 等待图表渲染完成
      await new Promise(resolve => setTimeout(resolve, 1000))

      console.log('所有资源加载完成，开始截图...')

      // 检测当前主题
      const isDarkMode = document.documentElement.classList.contains('dark')

      // 使用 html2canvas 截图，让图片保持动态尺寸（不设置固定width/height）
      const canvas = await html2canvas(shareCardElement, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: isDarkMode ? '#141415' : '#ffffff',
        logging: true, // 开启调试日志
        imageTimeout: 15000,
        foreignObjectRendering: false,
        scrollX: 0,
        scrollY: 0,
        // 在截图时处理图片和样式兼容性
        onclone: (clonedDoc) => handleScholarImageProcessing(clonedDoc, isDarkMode)
      })

      console.log('截图完成，canvas尺寸:', {
        width: canvas.width,
        height: canvas.height
      })

      // 转换为 Blob
      const blob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob) => {
          resolve(blob!)
        }, 'image/png', 1.0)
      })

      // 上传到 S3
      const fileName = `scholar-${scholarId}-latest.png`
      const publicUrl = await uploadFileToS3(blob, 'image/png', fileName)

      console.log('Scholar OG 图片上传成功:', publicUrl)

      // 更新本地状态
      ogImageUrl.value = publicUrl
      ogImageGenerated.value = true

      // 动态更新 meta 标签
      updateSeoMetaWithOgImage(publicUrl)

    } catch (error) {
      console.error('生成 Scholar OG 图片失败:', error)
    }
  }

  // 检查是否已有 Scholar OG 图片
  const checkExistingScholarOgImage = async (scholarId: string) => {
    try {
      const response = await checkScholarOgImageExists(scholarId)
      if (response) {
        const existingUrl = getPredictableScholarOgImageUrl(scholarId)
        ogImageUrl.value = existingUrl
        ogImageGenerated.value = true
        console.log('找到已有的 Scholar OG 图片:', existingUrl)

        if (reportData.value) {
          updateSeoMetaWithOgImage(existingUrl)
        }
      }
    } catch (error) {
      console.log('未找到已有的 Scholar OG 图片，将在分析完成后生成')
    }
  }

  // 激活码调试;
  // showInviteModal.value = true

  watch(limitInfo, data => {
    if (data && data.errorType === 'limit') {
      showInviteModal.value = true
      inviteError.value = ''
      inviteLoading.value = false
    }
  })

  watch(reportDataInfo, data => {
    if (data) {
      fetchReportData(data.jsonUrl)
    }
  })

  function onShowWaitingListModal() {
    showInviteModal.value = false
    showWaitingListModal.value = true
  }
  function onBackToInviteCode() {
    showWaitingListModal.value = false
    showInviteModal.value = true
  }

  const fetchReportData = (url: string) => {
    let processedUrl = url
    loading.value = true
    isFetching.value = true

    // reportData.value = initData.value;

    try {
      if (url.startsWith('http')) {
        const urlObj = new URL(url)
        const currentDomain = window.location.origin
        const isLocalDomain = [
          'localhost',
          '127.0.0.1',
          '0.0.0.0',
          '::1',
          /^192\.168\.\d{1,3}\.\d{1,3}$/,
          /^10\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,
          /^172\.(1[6-9]|2[0-9]|3[0-1])\.\d{1,3}\.\d{1,3}$/,
        ].some(pattern => {
          if (typeof pattern === 'string') {
            return urlObj.hostname === pattern
          } else if (pattern instanceof RegExp) {
            return pattern.test(urlObj.hostname)
          }
          return false
        })

        if (isLocalDomain) {
          processedUrl = `${currentDomain}${urlObj.pathname}${urlObj.search}${urlObj.hash}`
          console.log(`Converted local URL from ${url} to ${processedUrl}`)
        } else {
          console.log('URL is public, using original URL')
        }
      }
    } catch (e) {
      console.warn('URL processing error:', e)
      processedUrl = url
    }

    fetch(processedUrl, {
      headers: {
        userid: currentUser.value?.uid || '',
        'Content-Type': 'application/json',
      },
    })
      .then(response => {
        if (!response.ok) {
          loading.value = false
          isFetching.value = false
          throw new Error(`Failed to fetch report data: ${response.status}`)
        }
        return response.json()
      })
      .then(data => {
        loading.value = false
        isFetching.value = false
        // setReportData(data);
        reportData.value = data
        papersItems.value = convertToLabelValueArray(
          reportData.value.researcherProfile.dataBlocks.publicationStats,
          ['totalPapers', 'totalCitations', 'hIndex']
        )
        insightsItems.value = convertToLabelValueArray(
          reportData.value.researcherProfile.dataBlocks.publicationInsight,
          [
            'totalPapers',
            'topTierPapers',
            'firstAuthorPapers',
            'firstAuthorCitations',
            'totalCoauthors',
            'lastAuthorPapers',
          ]
        )

        // 更新 SEO 元数据
        if (reportData.value) {
          updateScholarSeoMeta(reportData.value)

          // 自动生成 OG 图片
          nextTick(() => {
            const scholarId = reportData.value.researcherProfile?.researcherInfo?.scholarId
            if (scholarId) {
              generateScholarOgImage(scholarId)
            }
          })
        }
      })
      .catch(error => {
        loading.value = false
        isFetching.value = false
        console.error('Error fetching report data:', error)
      })
  }
  const startConnection = () => {
    // debug
    // reportData.value = initData.value
    // loading.value = false
    if (query) {
      connect(query, '/api/stream', { Userid: currentUser.value?.uid || '' })
    }
  }

  const handleCompare = (query?: string) => {
    const searchValue = query || compareInputRef.value?.searchValue
    console.log('handleCompare', searchValue)
    if (!searchValue?.trim()) return

    // 跳转到比较页面，携带researcher1和researcher2参数
    router.push({
      path: '/compare',
      query: {
        researcher1: reportData.value?.researcherProfile?.researcherInfo?.scholarId,
        researcher2: searchValue,
      },
    })
  }
  const handleSearch = (query?: string) => {
    const search = query || searchInputRef.value.searchValue
    if (!search.trim()) return

    // 更新 URL 中的 query 参数
    router.replace({
      path: '/report',
      query: { query: search },
    })

    // 显示加载状态和骨架屏
    loading.value = true
    reportData.value = null

    // 发起新的搜索请求
    connect(search, '/api/stream', { Userid: currentUser.value?.uid || '' })
  }

  // 处理激活码提交
  async function handleSubmitActivationCode(code: string) {
    inviteLoading.value = true
    inviteError.value = ''
    try {
      const res = await submitActivationCode(
        '/api/activation-codes/use',
        { code },
        { headers: { Userid: currentUser.value?.uid || '' } }
      )
      if (res.data?.success) {
        showInviteModal.value = false
        inviteSuccess.value = true
        setTimeout(() => {
          inviteSuccess.value = false
          // startConnection()
        }, 2000)
      } else {
        inviteError.value = 'Invalid invite code. Please check and try again.'
      }
    } catch (e) {
      inviteError.value = 'Invalid invite code. Please check and try again.'
    } finally {
      inviteLoading.value = false
    }
  }

  function onSuccessOk() {
    inviteSuccess.value = false
    // 重新执行比较流程
    handleCompare()
  }

  function goHome() {
    router.replace('/analysis')
  }

  onMounted(() => {
    startConnection()

    // 检查是否已有 Scholar OG 图片
    if (query) {
      const possibleScholarId = extractScholarId(query)
      if (possibleScholarId) {
        checkExistingScholarOgImage(possibleScholarId)
      }
    }
  })
</script>

<style scoped lang="scss">
  .technical {
    background-image: url('~/assets/image/technical.png');
    background-size: 100% 100%;
    background-position: center;
    @apply h-53 bg-no-repeat flex items-center justify-center gap-155px;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
  }

  .dark .technical {
    background-image: url('~/assets/image/technicaldark.png');
    background-size: 100% 100%;
    background-position: center;
    @apply h-53 bg-no-repeat flex items-center justify-center gap-155px;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
  }

  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .success-modal-ok-btn:active {
    background: #222;
  }

  .wanna-compare-card {
    position: relative;
  }

  .wanna-compare-card::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 1px;
    background: linear-gradient(180deg, #FFFFFF 0%, rgba(255, 255, 255, 0.4) 100%);
    border-radius: 15px;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    pointer-events: none;
  }

  .dark .wanna-compare-card {
    border: 1px solid #27282D;
  }

  .dark .wanna-compare-card::before {
    display: none;
  }
</style>
