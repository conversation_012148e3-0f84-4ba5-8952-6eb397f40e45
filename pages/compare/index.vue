<template>
  <div class="px-30 h-full" style="margin-top: 4rem">
    <!-- 激活码弹窗 -->
    <InviteCodeModal
      v-if="showInviteModal"
      :error="inviteError"
      :loading="inviteLoading"
      @close="showInviteModal = false"
      @submit="handleSubmitActivationCode"
      @waiting-list="onShowWaitingListModal"
    />
    <WaitingListModal
      v-if="showWaitingListModal"
      @close="showWaitingListModal = false"
      @back="onBackToInviteCode"
    />
    <div
      v-if="inviteSuccess"
      class="fixed inset-0 z-50 flex items-center justify-center bg-black/40"
    >
      <div
        class="bg-white rounded-2xl shadow-xl p-8 pt-7 pb-7 w-full max-w-96 relative text-center"
      >
        <div class="text-lg font-semibold text-black mb-6">
          Enter Invite Code
          <button
            class="absolute top-6 right-6 text-gray-400 hover:text-gray-600 transition-colors text-xl"
            @click="inviteSuccess = false"
          >
            <svg
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 5L5 15M5 5L15 15"
                stroke="#BDBDBD"
                stroke-width="2"
                stroke-linecap="round"
              />
            </svg>
          </button>
        </div>
        <div class="flex justify-center mb-4">
          <div class="bg-green-500 rounded-full w-12 h-12 flex items-center justify-center mx-auto">
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
              <circle cx="16" cy="16" r="16" fill="none" />
              <path
                d="M10 17.5L14 21.5L22 13.5"
                stroke="#fff"
                stroke-width="2.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
        </div>
        <div class="text-xl font-bold text-black mb-2">Verification Successful</div>
        <div class="text-base text-gray-700 mb-7">
          Congratulations! Your invitation code has been verified successfully
        </div>
        <button
          class="w-full h-12 bg-black text-white rounded-lg font-semibold text-base transition hover:bg-gray-900"
          @click="goHome"
        >
          Ok
        </button>
      </div>
    </div>

    <template v-if="loading || isLoadingJson">
      <!-- 骨架屏组件 -->
      <div class="min-h-[60vh]">
        <!-- PK卡片骨架屏 -->
        <div class="relative">
          <div class="grid grid-cols-2 gap-7.5 mb-7.5">
            <!-- 左侧研究者骨架屏 -->
            <div class="bg-[#9BA3C1]/20 dark:bg-gray-600/20 rounded-2xl p-7.5 relative animate-pulse">
              <div class="absolute -top-5 left-7.5">
                <div class="w-15 h-15 rounded-full bg-gray-200/30 dark:bg-gray-600/30 border-4 border-white dark:border-gray-800"></div>
              </div>
              <div class="mt-7.5">
                <div class="h-8 bg-gray-200/30 dark:bg-gray-600/30 rounded w-3/4 mb-3"></div>
                <div class="h-5 bg-gray-200/25 dark:bg-gray-600/25 rounded w-1/2 mb-4"></div>
                <div class="flex flex-wrap gap-2 mb-4">
                  <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-20"></div>
                  <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-24"></div>
                  <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-16"></div>
                </div>
                <div class="space-y-2">
                  <div class="h-6 bg-gray-200/25 dark:bg-gray-600/25 rounded w-5/6"></div>
                  <div class="h-4 bg-gray-200/20 dark:bg-gray-600/20 rounded w-4/6"></div>
                </div>
              </div>
            </div>

            <!-- 右侧研究者骨架屏 -->
            <div class="bg-[#C6A69B]/20 dark:bg-gray-600/20 rounded-2xl p-7.5 relative animate-pulse">
              <div class="absolute -top-5 left-7.5">
                <div class="w-15 h-15 rounded-full bg-gray-200/30 dark:bg-gray-600/30 border-4 border-white dark:border-gray-800"></div>
              </div>
              <div class="mt-7.5">
                <div class="h-8 bg-gray-200/30 dark:bg-gray-600/30 rounded w-3/4 mb-3"></div>
                <div class="h-5 bg-gray-200/25 dark:bg-gray-600/25 rounded w-1/2 mb-4"></div>
                <div class="flex flex-wrap gap-2 mb-4">
                  <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-20"></div>
                  <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-24"></div>
                  <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-16"></div>
                </div>
                <div class="space-y-2">
                  <div class="h-6 bg-gray-200/25 dark:bg-gray-600/25 rounded w-5/6"></div>
                  <div class="h-4 bg-gray-200/20 dark:bg-gray-600/20 rounded w-4/6"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- VS标志骨架屏 -->
          <div class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 z-10">
            <div class="w-20 h-20 rounded-full bg-gray-200/40 dark:bg-gray-600/40 animate-pulse"></div>
          </div>
        </div>
        <Loading :visible="loading" :data="thinking" @update:visible="router.replace('/analysis')" />

        <!-- 雷达图骨架屏 -->
        <div class="bg-white dark:bg-[#1a1a1b] rounded-2xl p-7.5 mb-7.5">
          <div class="animate-pulse">
            <div class="h-8 bg-gray-100/60 dark:bg-gray-600/60 rounded w-1/3 mx-auto mb-8"></div>
            <div class="h-[300px] bg-gray-100/40 dark:bg-gray-600/40 rounded-lg"></div>
          </div>
        </div>

        <!-- 指标对比骨架屏 -->
        <div class="bg-white dark:bg-[#1a1a1b] rounded-2xl p-7.5 mb-7.5">
          <div class="animate-pulse">
            <div class="h-8 bg-gray-100/60 dark:bg-gray-600/60 rounded w-1/4 mx-auto mb-8"></div>
            <div class="grid grid-cols-2 gap-7.5">
              <div class="space-y-4">
                <div class="h-6 bg-gray-100/50 dark:bg-gray-600/50 rounded w-full"></div>
                <div class="h-6 bg-gray-100/50 dark:bg-gray-600/50 rounded w-5/6"></div>
                <div class="h-6 bg-gray-100/50 dark:bg-gray-600/50 rounded w-4/6"></div>
              </div>
              <div class="space-y-4">
                <div class="h-6 bg-gray-100/50 dark:bg-gray-600/50 rounded w-full"></div>
                <div class="h-6 bg-gray-100/50 dark:bg-gray-600/50 rounded w-5/6"></div>
                <div class="h-6 bg-gray-100/50 dark:bg-gray-600/50 rounded w-4/6"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Roast骨架屏 -->
        <div class="bg-[#FDF7F7] dark:bg-[#222222] rounded-2xl p-7.5 mt-7.5">
          <div class="animate-pulse">
            <div class="h-10 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/4 mx-auto mb-6"></div>
            <div class="space-y-4 max-w-[800px] mx-auto">
              <div class="h-4 bg-gray-200/20 dark:bg-gray-600/20 rounded w-full"></div>
              <div class="h-4 bg-gray-200/20 dark:bg-gray-600/20 rounded w-5/6"></div>
              <div class="h-4 bg-gray-200/20 dark:bg-gray-600/20 rounded w-4/6"></div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template v-else-if="pkData">
      <div class="relative bg-cover bg-center bg-no-repeat rounded-xl compare-page-bg">
        <!-- PK卡片部分 -->
        <div class="grid grid-cols-2 gap-7.5 mb-7.5">
          <!-- 左侧研究者 -->
          <div class="rounded-xl p-5 relative flex flex-col items-center text-center min-h-[320px]">
            <div class="absolute -top-5 left-1/2 -translate-x-1/2">
              <div class="flex items-center gap-2">
                <img
                  :src="pkData.researcher1.avatar"
                  class="w-15 h-15 rounded-full border-4 border-white object-cover"
                />
              </div>
            </div>
            <div class="mt-7.5 w-full flex flex-col justify-between flex-1">
              <div class="flex flex-col">
                <div class="text-white text-xl font-bold">{{ pkData.researcher1.name }}</div>
                <div class="text-white dark:text-[#C6C6C6] mt-1.5 flex items-start justify-center text-sm max-w-[400px] mx-auto">
                  <SvgIcon name="verified" class="text-4 mr-1.5 mt-0.5 flex-shrink-0" />
                  <span class="text-center line-clamp-2">{{ pkData.researcher1.affiliation }}</span>
                </div>
              </div>
              <div class="mt-auto">
                <div class="flex flex-wrap gap-1.5 mb-3 justify-center items-center min-h-[32px]">
                  <div
                    v-for="(field, index) in pkData.researcher1.research_fields.slice(0, 3)"
                    :key="index"
                    class="px-2.5 py-1 bg-[#A1AED2] dark:bg-[#3C4356] rounded-md text-[#262D3F] dark:text-[#FAF9F5] text-xs whitespace-nowrap"
                  >
                    {{ field }}
                  </div>
                </div>
                <div class="text-white dark:text-[#C6C6C6] text-sm">
                  Wanna compare
                  <span class="text-[#364A83] dark:text-[#FDB852] font-bold">{{
                    pkData.researcher1.name
                  }}</span>
                  with other AI researchers?
                </div>
                <div class="text-white dark:text-[#C6C6C6] text-xs mt-1">
                  Just input their name, or Google Scholar link
                </div>
                <motion.div
                  :initial="{ opacity: 0, y: 10 }"
                  :animate="{ opacity: 1, y: 0 }"
                  :transition="{ duration: 0.3, ease: 'easeInOut', delay: 0.2 }"
                  class="f-cer mt-5 mb-4"
                >
                  <div
                    class="custom-input border rounded-full bg-white border-black min-h-14 w-120 p-1 flex items-center justify-between gap-3 pl-6"
                  >
                    <SearchInput ref="searchInputRef1" placeholder="Researcher name" @enter-search="handleLeftCompare" />
                    <ActionButton :imgSrc="stars" buttonText="Compare" @click="handleLeftCompare" />
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
          <!-- 右侧研究者 -->
          <div class="rounded-xl p-5 relative flex flex-col items-center text-center min-h-[320px]">
            <div class="absolute -top-5 left-1/2 -translate-x-1/2">
              <div class="flex items-center gap-2">
                <img
                  :src="pkData.researcher2.avatar"
                  class="w-15 h-15 rounded-full border-4 border-white object-cover"
                />
              </div>
            </div>
            <div class="mt-7.5 w-full flex flex-col justify-between flex-1">
              <div class="flex flex-col">
                <div class="text-white text-xl font-bold">{{ pkData.researcher2.name }}</div>
                <div class="text-white dark:text-[#C6C6C6] mt-1.5 flex items-start justify-center text-sm max-w-[400px] mx-auto">
                  <SvgIcon name="verified-brown" class="text-4 mr-1.5 mt-0.5 flex-shrink-0 text-red-500" />
                  <span class="text-center line-clamp-2">{{ pkData.researcher2.affiliation }}</span>
                </div>
              </div>
              <div class="mt-auto">
                <div class="flex flex-wrap gap-1.5 mb-3 justify-center items-center min-h-[32px]">
                  <div
                    v-for="(field, index) in pkData.researcher2.research_fields.slice(0, 3)"
                    :key="index"
                    class="px-2.5 py-1 bg-[#E7CDC3] dark:bg-[#413834] rounded-md text-[#7F4832] dark:text-[#FAF9F5] text-xs whitespace-nowrap"
                  >
                    {{ field }}
                  </div>
                </div>
                <div class="text-white dark:text-[#C6C6C6] text-sm">
                  Wanna compare
                  <span class="text-[#6D4130] dark:text-[#5765F2] font-bold">{{
                    pkData.researcher2.name
                  }}</span>
                  with other AI researchers?
                </div>
                <div class="text-white dark:text-[#C6C6C6] text-xs mt-1">
                  Just input their name, or Google Scholar link
                </div>
                <motion.div
                  :initial="{ opacity: 0, y: 10 }"
                  :animate="{ opacity: 1, y: 0 }"
                  :transition="{ duration: 0.3, ease: 'easeInOut', delay: 0.2 }"
                  class="f-cer mt-5 mb-4"
                >
                  <div
                    class="custom-input border rounded-full bg-white border-black min-h-14 w-120 p-1 flex items-center justify-between gap-3 pl-6"
                  >
                    <SearchInput ref="searchInputRef2" placeholder="Researcher name" @enter-search="handleRightCompare" />
                    <ActionButton :imgSrc="stars" buttonText="Compare" @click="handleRightCompare" />
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 雷达图部分 -->
      <div class="rounded-2xl p-7.5 mb-7.5">
        <RadarChart :researcher1="pkData.researcher1" :researcher2="pkData.researcher2" size="large" />
      </div>

      <div class="flex items-center justify-center mb-10 flex-col gap-4">
        <div
          class="h-13.5 w-[210px] text-4 font-600 text-white bg-[#CB7C5D] dark:bg-[#654D43] dark:border dark:border-[#866457] fx-cer justify-center rounded-full gap-2 cursor-pointer"
          @click="showCompareCard = true"
          >
          <div class="i-proicons:x-twitter wh-5 font-600" data-v-2dc31878=""></div>
          Share
        </div>
      </div>

      <VSCard
        :show="showPopup"
        :user="user"
        :income="220000"
        :is-dark="isDark"
        :stats="stats"
        :role-model="roleModel"
        :conferenceDistribution="initData.dataBlocks.publicationInsight.conferenceDistribution"
        @close="showPopup = false"
        :insights-items="items"
        :pk-data="pkData"
        />

      <ShareCardCompare
        :show="showCompareCard"
        :is-dark="isDark"
        :researcher1="pkData.researcher1"
        :researcher2="pkData.researcher2"
        @close="showCompareCard = false"
      />
      <!-- 指标对比和代表作部分 -->
      <CompareMetrics :researcher1="pkData.researcher1" :researcher2="pkData.researcher2" />

      <!-- Roast 部分 -->
      <div 
        class="rounded-2xl p-7.5 mt-10 bg-cover bg-center bg-no-repeat border dark:border-[#27282D]" 
        :style="{ 
          backgroundImage: isDark 
            ? 'url(/image/compare-roast-dark.png)' 
            : 'url(/image/compare-roast.png)',
          backgroundSize: 'cover',
          minHeight: '200px'
        }"
      >
        <div class="text-center flex flex-col">
          <i class="text-[56px] font-extrabold text-[#CB7C5D] clash-display mb-5">Roast</i>
          <div 
            class="text-center max-w-[800px] mx-auto text-18px leading-8"
            :class="isDark ? 'text-white' : 'text-[#555658]'"
          >
            {{ pkData.roast }}
          </div>
        </div>
      </div>

      <!-- 比较部分 -->
      <div class="flex flex-col items-center mt-20">
        <div class="text-[56px] clash-semibold font-semibold text-center max-w-[800px] leading-[72.8px]">
          Or compare the profiles<br />
          of two AI researchers
        </div>
        <div class="flex items-center gap-4 mt-10 w-full max-w-[800px]">
          <div class="flex-1 sty_f_r_end">
            <input
              v-model="researcher1Input"
              type="text"
              placeholder="Researcher name"
              class="custom-input w-[280px] h-13.5 px-5 rounded-full border border-black bg-white font-bold placeholder-#7C8493 placeholder:font-normal text-black placeholder-gray-300 min-w-0"
              style="outline: none !important; box-shadow: none !important; border-color: black !important;"
              @keyup.enter="handleCompare"
            />
          </div>
          <div class="text-3xl font-bold italic" style="color: #CB7C5D;">VS</div>
          <div class="flex-1">
            <input
              v-model="researcher2Input"
              type="text"
              placeholder="Researcher name"
              class="custom-input w-[280px] h-13.5 px-5 rounded-full border border-black bg-white font-bold placeholder-#7C8493 placeholder:font-normal text-black placeholder-gray-300 min-w-0"
              style="outline: none !important; box-shadow: none !important; border-color: black !important;"
              @keyup.enter="handleCompare"
            />
          </div>
        </div>
        <button
          @click="handleCompare"
          class="mt-7.5 flex items-center justify-center gap-2 rounded-full bg-black-100 text-white hover:bg-black-100/90 transition-colors w-[210px] h-[54px] dark:border-3 dark:border-white"
        >
          <img src="/image/stars.png" alt="compare" />
          <span class="text-base font-bold">Compare</span>
        </button>
        <div class="text-sm text-gray-500 mt-7.5 mb-20">
          By clicking Compare you agree to our
          <a href="/terms" class="text-primary-100 hover:text-primary-200 underline"
            >terms of service</a
          >
        </div>
      </div>
    </template>
    <template v-else>
      <!-- 兜底展示 -->
      <div class="min-h-[60vh] flex flex-col items-center justify-center">
        <div class="w-20 h-20 rounded-full bg-[#FDF7F7] flex items-center justify-center mb-6">
          <div class="i-carbon:warning-alt text-primary-100 text-3xl"></div>
        </div>
        <div class="text-[32px] font-bold clash-display text-center mb-4">No Data Available</div>
        <div class="text-gray-600 text-center max-w-[500px] mb-10">
          Sorry, we couldn't find the comparison data. Please try comparing other researchers.
        </div>
        <!-- 比较部分 -->
        <div class="w-full max-w-[800px]">
          <div class="text-[42px] font-bold clash-display text-center leading-[1.2] mb-10">
            Compare the profiles<br />
            of two AI researchers
          </div>
          <div class="flex items-center gap-4">
            <div class="flex-1">
              <input
                v-model="researcher1Input"
                type="text"
                placeholder="Researcher name"
                class="w-full h-12 px-5 rounded-full border border-black bg-white font-bold placeholder-#7C8493 placeholder:font-normal text-black placeholder-gray-300"
                style="outline: none !important; box-shadow: none !important; border-color: black !important;"
                @keyup.enter="handleCompare"
              />
            </div>
            <div class="text-3xl font-bold italic" style="color: #CB7C5D;">VS</div>
            <div class="flex-1">
              <input
                v-model="researcher2Input"
                type="text"
                placeholder="Researcher name"
                class="w-full h-12 px-5 rounded-full border border-black bg-white font-bold placeholder-#7C8493 placeholder:font-normal text-black placeholder-gray-300"
                style="outline: none !important; box-shadow: none !important; border-color: black !important;"
                @keyup.enter="handleCompare"
              />
            </div>
          </div>
          <div class="flex justify-center mt-7.5">
            <button
              @click="handleCompare"
              class="flex items-center justify-center gap-2 rounded-full bg-black-100 text-white hover:bg-black-100/90 transition-colors w-[210px] h-[54px] dark:border-3 dark:border-white"
            >
              <img src="/image/stars.png" alt="compare" />
              <span class="text-base font-bold">Compare</span>
            </button>
          </div>
          <div class="text-sm text-gray-500 text-center mt-7.5 mb-20">
            By clicking Compare you agree to our
            <a href="/terms" class="text-primary-100 hover:text-primary-200 underline"
              >terms of service</a
            >
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
  import stars from '@/assets/image/stars.png'
  import { useRoute, useRouter } from 'vue-router'
  import { useEventStream } from '@/composables/useEventStream'
  import { motion } from "motion-v"
  import RadarChart from '@/components/RadarChart.vue'
  import CompareMetrics from '@/components/CompareMetrics.vue'
  import ShareCardCompare from '@/components/ShareCardCompare/index.vue'
  import { ref, onMounted, watch, onUnmounted } from 'vue'
  import InviteCodeModal from '@/components/InviteCodeModal.vue'
  import WaitingListModal from '@/components/WaitingListModal.vue'
  import { submitActivationCode } from '~/api'

  definePageMeta({
    middleware: 'auth',
  })

  // 动态 SEO 元数据
  const updateScholarCompareSeoMeta = (data: any) => {
    const researcher1Name = data.researcher1?.name || 'Researcher 1'
    const researcher2Name = data.researcher2?.name || 'Researcher 2'

    const title = `${researcher1Name} vs ${researcher2Name} - Scholar Comparison | DINQ`
    const description = `Compare researchers ${researcher1Name} and ${researcher2Name}. Analyze their academic achievements, publications, citations, and research impact.`

    // 获取研究领域
    const researcher1Fields = data.researcher1?.research_interests || []
    const researcher2Fields = data.researcher2?.research_interests || []
    const allFields = [...new Set([...researcher1Fields, ...researcher2Fields])]

    const keywords = [
      researcher1Name,
      researcher2Name,
      'Scholar Comparison',
      'Researcher Comparison',
      'Academic Analysis',
      'Research Impact',
      'Citation Analysis',
      ...allFields
    ].join(', ')

    useSeoMeta({
      title,
      description,
      keywords,

      // Open Graph
      ogTitle: title,
      ogDescription: description,
      ogType: 'website',
      ogUrl: `https://dinq.io/compare?researcher1=${encodeURIComponent(researcher1Name)}&researcher2=${encodeURIComponent(researcher2Name)}`,

      // Twitter Card
      twitterCard: 'summary_large_image',
      twitterTitle: title,
      twitterDescription: description,

      // 额外的 meta 标签
      author: 'DINQ',
      'article:tag': allFields.join(', '),
    })

    // 设置页面标题
    useHead({
      title,
      meta: [
        {
          name: 'robots',
          content: 'index, follow'
        },
        {
          name: 'theme-color',
          content: '#CB7C5D'
        }
      ],
      link: [
        {
          rel: 'canonical',
          href: `https://dinq.io/compare?researcher1=${encodeURIComponent(researcher1Name)}&researcher2=${encodeURIComponent(researcher2Name)}`
        }
      ]
    })
  }

  const route = useRoute()
  const router = useRouter()
  const { currentUser } = useFirebaseAuth()

  const { thinking, loading, reportDataInfo, connectWithObj, limitInfo, pkData } = useEventStream()

  const researcher1Input = ref('')
  const researcher2Input = ref('')
  // const pkData = ref<any>(null) // 使用 useEventStream 中的 pkData
  
  // 为顶部搜索框添加独立的ref
  const searchInputRef1 = ref()
  const searchInputRef2 = ref()

  const isAnalyzing = ref(false)
  const analysisProgress = ref(0)
  let progressInterval: NodeJS.Timeout | null = null

  const isLoadingJson = ref(false)

  const showInviteModal = ref(false)
  const inviteError = ref('')
  const inviteLoading = ref(false)
  const inviteSuccess = ref(false)
  const showWaitingListModal = ref(false)
  const showPopup = ref(false)
  const showCompareCard = ref(false)

  const isDark = ref(false)
  onMounted(() => {
    isDark.value = document.documentElement.classList.contains('dark')
    const observer = new MutationObserver(() => {
      isDark.value = document.documentElement.classList.contains('dark')
    })
    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] })
  })

  const items = [
      {
        label: 'Github Stars',
        value: 4328,
      },
      {
        label: 'Github Stars',
        value: 4328,
      },
      {
        label: 'Github Stars',
        value: 4328,
      },
      {
        label: 'Github Stars',
        value: 4328,
      },
      {
        label: 'Github Stars',
        value: 4328,
      },
    ];

  const user = {
    name: 'Daiheng Gao',
    avatar: '/@/assets/image/avator.png',
    role: 'OpenAI-Researcher',
    papers: 1177,
    citations: 1177,
  }

  const stats = {
    firstAuthor: 99,
    total: 177,
    citation: 37,
  }

  const roleModel = {
    name: 'Marc Andreessen',
    avatar: '/marc.jpg',
    title: 'OpenAI-Researcher',
    achievement: 'Image transformer (2018)',
  }

  // demo
  const initData = {
    researcherInfo: {
      name: 'Niki Parmar',
      abbreviatedName: 'N Parmar',
      affiliation: 'Co-Founder at Essential AI',
      email: '',
      researchFields: ['Machine Learning', 'Deep Learning'],
      totalCitations: 199702,
      citations5y: 191401,
      hIndex: 43,
      hIndex5y: 43,
      yearlyCitations: {
        '2018': 1492,
        '2019': 5805,
        '2020': 11524,
        '2021': 20460,
        '2022': 31288,
        '2023': 45964,
        '2024': 60728,
        '2025': 21425,
      },
      scholarId: 'q2YXPSgAAAAJ',
      avatar: 'https://api.dinq.io/images/icon/avatar/0.png',
      description:
        'Niki Parmar, seeking elegant simplicity within complex systems. Transforming data into insight through focused, mindful presence.',
    },
    dataBlocks: {
      publicationStats: {
        blockTitle: 'Papers',
        totalPapers: 95,
        totalCitations: 199702,
        hIndex: 43,
        yearlyCitations: {
          '2018': 1492,
          '2019': 5805,
          '2020': 11524,
          '2021': 20460,
          '2022': 31288,
          '2023': 45964,
          '2024': 60728,
          '2025': 21425,
        },
        yearlyPapers: {
          '2016': 1,
          '2017': 31,
          '2018': 13,
          '2019': 5,
          '2020': 6,
          '2021': 8,
          '2022': 7,
          '2023': 5,
          '2024': 6,
          '2025': 2,
        },
      },
      publicationInsight: {
        blockTitle: 'Insight',
        totalPapers: 95,
        topTierPapers: 34,
        firstAuthorPapers: 2,
        firstAuthorCitations: 2447,
        totalCoauthors: 105,
        lastAuthorPapers: 6,
        conferenceDistribution: {
          CVPR: 3,
          ICML: 3,
          NeurIPS: 12,
          Others: 29,
        },
      },
      roleModel: {
        blockTitle: 'Role Model',
        found: true,
        name: 'Niki Parmar',
        institution: 'Co-Founder at Essential AI',
        position: 'Established Researcher',
        photoUrl: 'https://api.dinq.io/images/icon/avatar/0.png',
        achievement: 'Image transformer (2018)',
        similarityReason:
          'Congrats! You are already your own hero! Your unique research path and contributions have established you as a notable figure in your field.',
        isSelf: true,
      },
      closestCollaborator: {
        blockTitle: 'Closest Collaborator',
        fullName: 'ashish vaswani',
        affiliation: 'Essential AI',
        researchInterests: [],
        scholarId: '',
        coauthoredPapers: 46,
        avatar: 'https://api.dinq.io/images/icon/advisor.png',
        bestCoauthoredPaper: {
          title: 'Attention is all you need',
          year: 2017,
          venue: 'NeurIPS',
          fullVenue: 'NeurIPS 2017',
          citations: 181420,
        },
        connectionAnalysis: null,
      },
      estimatedSalary: {
        blockTitle: 'Estimated Salary',
        earningsPerYearUSD: null,
        levelEquivalency: {
          us: null,
          cn: null,
        },
        reasoning: null,
      },
      researcherCharacter: {
        blockTitle: 'Researcher Character',
        depthVsBreadth: 5,
        theoryVsPractice: 5,
        soloVsTeamwork: 5,
        justification: "Based on the researcher's publication record and citation metrics.",
      },
      representativePaper: {
        blockTitle: 'Representative Paper',
        title: 'Attention is all you need',
        year: 2017,
        venue: 'NeurIPS 2017',
        fullVenue: 'Advances in neural information processing systems 30, 2017',
        citations: 181420,
        authorPosition: 3,
        paper_news: {
          url: 'https://newsletter.theaiedge.io/p/attention-is-all-you-need-the-original',
          date: '2025-02-12',
          news: 'Attention Is All You Need: The Original Transformer Architecture',
          description:
            "A newsletter chapter from 'The AI Edge' examining the original Transformer architecture, including detailed explanations of self-attention mechanisms, multi-head attention layers, and the impact of this groundbreaking paper on modern AI development.",
          is_fallback: false,
        },
      },
      criticalReview: {
        blockTitle: 'Roast',
        evaluation:
          'Niki Parmar is a distinguished researcher in the fields of machine learning and deep learning, with a remarkable h-index of 43, demonstrating significant impact through highly-cited work, particularly the seminal paper "Attention is all you need." This work alone has garnered an astonishing 181,420 citations, underscoring their profound influence on the field.\n\nHowever, the researcher\'s citation growth rate indicates a slight decline, suggesting a potential need to diversify research topics or enhance engagement with emerging trends. Additionally, while their collaborative efforts are commendable, increasing first-author publications could further solidify their leadership in the field.\n\nWith their strong foundation and balanced approach to research, Niki Parmar is well-poised to continue making groundbreaking contributions to AI.',
      },
    },
    configInfo: {
      comment: 'Placeholder for bottom/page configuration data',
    },
  }

  // 静态数据
  const staticData = {
    researcher1: {
      name: 'Xingchao Liu',
      affiliation: 'DeepSeek AI',
      research_fields: [
        'Deep Learning',
        'Natural Language Processing',
        'Computer Vision',
        'Robotics',
      ],
      scholar_id: 'VOTVE0UAAAAJ',
      total_citations: 3867, //first_author_citations
      h_index: 22,
      top_tier_papers: 16,
      first_author_papers: 10,
      first_author_citations: 1956,
      most_cited_paper: {
        year: '2022',
        title: 'Flow straight and fast: Learning to generate and transfer data with rectified flow',
        venue: 'arXiv preprint arXiv:2209.03003, 2022 2022',
        authors: ['X Liu', 'C Gong', 'Q Liu'],
        citations: 857,
      },
      paper_evaluation:
        'This paper is significant for its innovative approach to data generation and transfer using rectified flow, evidenced by its substantial citation count of 857.',
      avatar: 'https://api.dinq.io/images/icon/avatar/6.png',
      evaluation:
        'Xingchao Liu has made impressive strides in Machine Learning research, with a notable H-index of 22 and 3867 total citations. Their focus on top-tier venues like NeurIPS and impactful paper on data generation showcases their commitment to quality. However, to further enhance their profile, increasing first-author and corresponding author papers could elevate their visibility. Diversifying collaboration networks beyond ByteDance Inc. and exploring interdisciplinary research avenues may foster fresh perspectives. With consistent publication output and a solid citation growth rate, Liu shows promise in shaping the future of ML. Encouraging them to maintain this momentum and venture into new research domains could lead to even greater achievements.',
    },
    researcher2: {
      name: 'Qiang Liu',
      affiliation: 'Associate Professor of Computer Science, UT Austin',
      research_fields: [
        'Machine learning',
        'graphical models',
        'approximate inference',
        'reinforcement learning',
        'crowdsourcing',
      ],
      scholar_id: 'XEx1fZkAAAAJ',
      total_citations: 13191,
      h_index: 63,
      top_tier_papers: 37,
      first_author_papers: 20,
      first_author_citations: 4253,
      most_cited_paper: {
        title: 'Stein variational gradient descent: A general purpose Bayesian inference algorithm',
        year: '2016',
        venue: 'NeurIPS 2016',
        citations: 1332,
        authors: ['Q Liu', 'D Wang'],
      },
      paper_evaluation:
        'The paper introduces a groundbreaking Bayesian inference algorithm, significantly influencing computational statistics, evidenced by its 1332 citations since NeurIPS 2016.',
      avatar: 'https://api.dinq.io/images/icon/avatar/9.png',
      evaluation:
        'Qiang Liu has made remarkable contributions to machine learning and Bayesian inference, evident from an impressive H-index of 63 and 13191 total citations. His work on Stein variational gradient descent stands out with 1332 citations. However, there is room for improvement in increasing the citation growth rate, which currently shows a decline. Diversifying publication venues beyond Arxiv and focusing on high-impact journals could enhance visibility. Collaborating with a wider network might also foster new research directions. With continued dedication to innovation and strategic publication strategies, Liu is poised to further elevate his research impact and productivity.',
    },
    roast:
      "Xingchao Liu's impressive strides in Machine Learning are overshadowed by Qiang Liu's towering H-index and citation count, making Liu's achievements seem like a mere foothill compared to Qiang's towering peaks of academic excellence.",
  }

  const setDefaultImage = (event: Event) => {
    const target = event.target as HTMLImageElement
    target.src = '/image/avator.png'
  }

  // 激活码调试;
  // showInviteModal.value = true;

  watch(limitInfo, data => {
    if (data && data.errorType === 'limit') {
      showInviteModal.value = true
      inviteError.value = ''
      inviteLoading.value = false
    }
  })

  // 当组件挂载时，发起比较请求或使用静态数据
  onMounted(() => {
    if (route.query.researcher1 && route.query.researcher2) {
      // pkData.value = staticData
      // loading.value = false
      // 如果有查询参数，尝试从API获取数据
      connectWithObj(route.query, '/api/scholar-pk', { Userid: currentUser.value?.uid || '' })
    } else {
      // 否则使用静态数据, 调试使用;
      // pkData.value = staticData
      // loading.value = false
    }
  })

  // 处理左侧搜索框的比较请求（左侧研究者固定，用户输入右侧研究者）
  const handleLeftCompare = (query?: string) => {
    const newResearcher = query || searchInputRef1.value?.searchValue
    
    if (!newResearcher?.trim() || !pkData.value?.researcher1?.name) return
    
    const researcher1Name = pkData.value.researcher1.name
    console.log('handleLeftCompare', researcher1Name, newResearcher)

    // 更新路由参数：左侧研究者保持不变，右侧研究者使用用户输入
    router.replace({
      path: '/compare',
      query: {
        researcher1: researcher1Name,
        researcher2: newResearcher.trim(),
      },
    })

    // 显示分析中弹窗
    loading.value = true
    isLoadingJson.value = false // 重置 JSON 加载状态
    pkData.value = null

    connectWithObj(
      {
        researcher1: researcher1Name,
        researcher2: newResearcher.trim(),
      },
      '/api/scholar-pk',
      {
        Userid: currentUser.value?.uid || '',
      }
    )
  }

  // 处理右侧搜索框的比较请求（右侧研究者固定，用户输入左侧研究者）
  const handleRightCompare = (query?: string) => {
    const newResearcher = query || searchInputRef2.value?.searchValue
    
    if (!newResearcher?.trim() || !pkData.value?.researcher2?.name) return
    
    const researcher2Name = pkData.value.researcher2.name
    console.log('handleRightCompare', newResearcher, researcher2Name)

    // 更新路由参数：左侧研究者使用用户输入，右侧研究者保持不变
    router.replace({
      path: '/compare',
      query: {
        researcher1: newResearcher.trim(),
        researcher2: researcher2Name,
      },
    })

    // 显示分析中弹窗
    loading.value = true
    isLoadingJson.value = false // 重置 JSON 加载状态
    pkData.value = null

    connectWithObj(
      {
        researcher1: newResearcher.trim(),
        researcher2: researcher2Name,
      },
      '/api/scholar-pk',
      {
        Userid: currentUser.value?.uid || '',
      }
    )
  }

  // 处理底部搜索框的比较请求
  const handleCompare = () => {
    console.log('handleCompare', researcher1Input.value, researcher2Input.value)
    if (!researcher1Input.value || !researcher2Input.value) return

    // 更新路由参数
    router.replace({
      path: '/compare',
      query: {
        researcher1: researcher1Input.value,
        researcher2: researcher2Input.value,
      },
    })

    // 显示分析中弹窗
    loading.value = true
    isLoadingJson.value = false // 重置 JSON 加载状态
    pkData.value = null

    connectWithObj(
      {
        researcher1: researcher1Input.value,
        researcher2: researcher2Input.value,
      },
      '/api/scholar-pk',
      {
        Userid: currentUser.value?.uid || '',
      }
    )
  }

  // 监听pkData变化
  watch(reportDataInfo, newData => {
    if (newData) {
      fetchReportData(newData.jsonUrl)
    }
  })

  const fetchReportData = (url: string) => {
    let processedUrl = url
    isLoadingJson.value = true // 开始加载 JSON 数据

    try {
      if (url.startsWith('http')) {
        const urlObj = new URL(url)
        const currentDomain = process.client ? window.location.origin : 'https://dinq.io'
        const isLocalDomain = [
          'localhost',
          '127.0.0.1',
          '0.0.0.0',
          '::1',
          /^192\.168\.\d{1,3}\.\d{1,3}$/,
          /^10\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,
          /^172\.(1[6-9]|2[0-9]|3[0-1])\.\d{1,3}\.\d{1,3}$/,
        ].some(pattern => {
          if (typeof pattern === 'string') {
            return urlObj.hostname === pattern
          } else if (pattern instanceof RegExp) {
            return pattern.test(urlObj.hostname)
          }
          return false
        })

        if (isLocalDomain && process.client) {
          const currentDomain = window.location.origin
          processedUrl = `${currentDomain}${urlObj.pathname}${urlObj.search}${urlObj.hash}`
          console.log(`Converted local URL from ${url} to ${processedUrl}`)
        } else {
          console.log('URL is public, using original URL')
        }
      }
    } catch (e) {
      console.warn('URL processing error:', e)
      processedUrl = url
    }

    console.log('Fetching report data from:', processedUrl)
    fetch(processedUrl, {
      headers: {
        userid: currentUser.value?.uid || '',
        'Content-Type': 'application/json',
      },
    })
      .then(response => {
        if (!response.ok) {
          throw new Error(`Failed to fetch report data: ${response.status}`)
        }
        return response.json()
      })
      .then(data => {
        console.log('Report data fetched successfully')
        pkData.value = data
      })
      .catch(error => {
        console.error('Error fetching report data:', error)
        // 如果获取 JSON 数据失败，清空 pkData 以显示兜底内容
        pkData.value = null
      })
      .finally(() => {
        isLoadingJson.value = false // 结束加载状态
        loading.value = false // 结束整体加载状态
      })
  }

  // 监听路由变化，重置状态
  watch(
    route,
    () => {
      if (!route.query.researcher1 || !route.query.researcher2) {
        pkData.value = null
        loading.value = false
        isLoadingJson.value = false
      }
    },
    { immediate: true }
  )

  function onShowWaitingListModal() {
    showInviteModal.value = false
    showWaitingListModal.value = true
  }
  function onBackToInviteCode() {
    showWaitingListModal.value = false
    showInviteModal.value = true
  }

  async function handleSubmitActivationCode(code: string) {
    inviteLoading.value = true
    inviteError.value = ''
    try {
      const res = await submitActivationCode(
        '/api/activation-codes/use',
        { code },
        { headers: { Userid: currentUser.value?.uid || '' } }
      )
      if (res.data?.success) {
        showInviteModal.value = false
        inviteSuccess.value = true
        setTimeout(() => {
          inviteSuccess.value = false
        }, 2000)
      } else {
        inviteError.value = 'Invalid invite code. Please check and try again.'
      }
    } catch (e) {
      inviteError.value = 'Invalid invite code. Please check and try again.'
    } finally {
      inviteLoading.value = false
    }
  }

  function goHome() {
    router.replace('/analysis')
  }
</script>

<style scoped>
  .px-30 {
    padding-left: 120px;
    padding-right: 120px;
  }

  .w-15 {
    width: 3.75rem;
  }

  .h-15 {
    height: 3.75rem;
  }

  .clash-display {
    font-family: "Poppins", sans-serif;
  }

  .text-primary-100 {
    color: #c69279;
  }

  .text-15px {
    font-size: 15px;
  }

  /* 输入框聚焦时的样式 */
  input:focus {
    box-shadow: 0 0 0 2px rgba(198, 146, 121, 0.2);
  }

  .i-carbon:warning-alt {
    width: 2rem;
    height: 2rem;
  }

  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.8;
    }
  }

  @keyframes progress {
    0% {
      width: 0%;
    }
    100% {
      width: 100%;
    }
  }

  .animate-progress {
    animation: progress 2s ease-in-out infinite;
  }
  .fw {
    /* "Full Width" 的缩写 */
    width: 60%;
  }

  /* 控制元素靠右 */
  .sty_f_r_end {
    display: flex;
    justify-content: flex-end;
  }

  .compare-page-bg {
    background-image: url('~/assets/image/bgimg.png');
    /* background-size: 100%; */
  }

  .dark .compare-page-bg {
    background-image: url('~/assets/image/bgimgdark_1.png');
  }
</style>
