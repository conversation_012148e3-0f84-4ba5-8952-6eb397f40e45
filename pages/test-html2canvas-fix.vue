<template>
  <div class="container mx-auto p-8">
    <h1 class="text-3xl font-bold mb-6">html2canvas 隐藏元素修复测试</h1>
    
    <div class="space-y-6">
      <!-- 测试按钮 -->
      <div class="flex gap-4">
        <button 
          @click="testOldMethod" 
          class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
        >
          测试旧方法 (会生成白图)
        </button>
        <button 
          @click="testNewMethod" 
          class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
        >
          测试新方法 (应该正常)
        </button>
        <button 
          @click="testVisibleElement" 
          class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          测试可见元素 (对照组)
        </button>
      </div>

      <!-- 结果显示 -->
      <div v-if="results.length > 0" class="space-y-4">
        <h2 class="text-xl font-semibold">测试结果:</h2>
        <div v-for="(result, index) in results" :key="index" class="border rounded p-4">
          <h3 class="font-medium mb-2">{{ result.method }}</h3>
          <div class="flex gap-4">
            <div>
              <p class="text-sm text-gray-600 mb-2">生成的图片:</p>
              <img :src="result.imageUrl" alt="Generated" class="border max-w-xs" />
            </div>
            <div class="flex-1">
              <p class="text-sm"><strong>状态:</strong> {{ result.status }}</p>
              <p class="text-sm"><strong>尺寸:</strong> {{ result.dimensions }}</p>
              <p class="text-sm"><strong>耗时:</strong> {{ result.duration }}ms</p>
              <p v-if="result.error" class="text-sm text-red-600"><strong>错误:</strong> {{ result.error }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 测试用的卡片元素 -->
    
    <!-- 旧方法：使用 opacity: 0 + visibility: hidden -->
    <div 
      ref="oldMethodCard"
      class="old-method-hidden"
      style="position: fixed; left: -9999px; top: -9999px; opacity: 0; visibility: hidden; z-index: -1;"
    >
      <div class="test-card" data-method="old">
        <h2>旧方法测试卡片</h2>
        <p>这个卡片使用了 opacity: 0 和 visibility: hidden</p>
        <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded">
          <p>渐变背景测试</p>
        </div>
      </div>
    </div>

    <!-- 新方法：使用 transform: translateX(-100%) -->
    <div 
      ref="newMethodCard"
      class="new-method-hidden"
      style="position: fixed; top: 0; left: 0; transform: translateX(-100%); z-index: -1; pointer-events: none;"
    >
      <div class="test-card" data-method="new">
        <h2>新方法测试卡片</h2>
        <p>这个卡片使用了 transform: translateX(-100%)</p>
        <div class="bg-gradient-to-r from-green-500 to-blue-600 text-white p-4 rounded">
          <p>渐变背景测试</p>
        </div>
      </div>
    </div>

    <!-- 可见元素对照组 -->
    <div ref="visibleCard" class="mt-8">
      <h2 class="text-lg font-semibold mb-4">可见元素对照组:</h2>
      <div class="test-card" data-method="visible">
        <h2>可见测试卡片</h2>
        <p>这个卡片是完全可见的，用作对照组</p>
        <div class="bg-gradient-to-r from-orange-500 to-red-600 text-white p-4 rounded">
          <p>渐变背景测试</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import html2canvas from 'html2canvas-pro'

const oldMethodCard = ref<HTMLElement>()
const newMethodCard = ref<HTMLElement>()
const visibleCard = ref<HTMLElement>()

const results = ref<Array<{
  method: string
  imageUrl: string
  status: string
  dimensions: string
  duration: number
  error?: string
}>>([])

const captureElement = async (element: HTMLElement, methodName: string) => {
  const startTime = Date.now()
  
  try {
    console.log(`开始截图: ${methodName}`)
    
    // 等待渲染
    await new Promise(resolve => setTimeout(resolve, 100))
    
    const canvas = await html2canvas(element, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: 400,
      height: 300,
      logging: true,
    })
    
    const imageUrl = canvas.toDataURL('image/png')
    const duration = Date.now() - startTime
    
    // 检查是否是白图（简单检测）
    const isWhiteImage = imageUrl.length < 5000 // 白图通常很小
    
    results.value.push({
      method: methodName,
      imageUrl,
      status: isWhiteImage ? '可能是白图' : '正常',
      dimensions: `${canvas.width}x${canvas.height}`,
      duration,
    })
    
    console.log(`${methodName} 完成:`, {
      size: imageUrl.length,
      dimensions: `${canvas.width}x${canvas.height}`,
      duration
    })
    
  } catch (error: any) {
    console.error(`${methodName} 失败:`, error)
    results.value.push({
      method: methodName,
      imageUrl: '',
      status: '失败',
      dimensions: '0x0',
      duration: Date.now() - startTime,
      error: error.message
    })
  }
}

const testOldMethod = () => {
  if (oldMethodCard.value) {
    const cardElement = oldMethodCard.value.querySelector('.test-card') as HTMLElement
    captureElement(cardElement, '旧方法 (opacity: 0 + visibility: hidden)')
  }
}

const testNewMethod = () => {
  if (newMethodCard.value) {
    const cardElement = newMethodCard.value.querySelector('.test-card') as HTMLElement
    captureElement(cardElement, '新方法 (transform: translateX(-100%))')
  }
}

const testVisibleElement = () => {
  if (visibleCard.value) {
    const cardElement = visibleCard.value.querySelector('.test-card') as HTMLElement
    captureElement(cardElement, '可见元素对照组')
  }
}

// 设置页面meta
useSeoMeta({
  title: 'html2canvas 隐藏元素修复测试 - DINQ',
  description: '测试html2canvas对隐藏元素的截图效果',
})
</script>

<style scoped>
.test-card {
  width: 400px;
  height: 300px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0,0,0,0.2);
  font-family: 'Arial', sans-serif;
}

.test-card h2 {
  font-size: 24px;
  margin-bottom: 16px;
  font-weight: bold;
}

.test-card p {
  font-size: 16px;
  margin-bottom: 20px;
  line-height: 1.5;
}
</style>
