<template>
  <div class="container mx-auto p-8">
    <h1 class="text-3xl font-bold mb-6">GitHub比较页面OG图片测试</h1>
    
    <div class="space-y-6">
      <!-- 测试输入 -->
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium mb-2">用户1:</label>
          <input 
            v-model="testUser1" 
            type="text" 
            placeholder="输入GitHub用户名" 
            class="border rounded px-3 py-2 w-full"
          />
        </div>
        <div>
          <label class="block text-sm font-medium mb-2">用户2:</label>
          <input 
            v-model="testUser2" 
            type="text" 
            placeholder="输入GitHub用户名" 
            class="border rounded px-3 py-2 w-full"
          />
        </div>
      </div>

      <!-- 测试按钮 -->
      <div class="flex gap-4">
        <button 
          @click="testPredictableUrl" 
          class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          生成可预测URL
        </button>
        <button 
          @click="checkImageExists" 
          :disabled="!predictableUrl || isChecking"
          class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50"
        >
          {{ isChecking ? '检查中...' : '检查图片是否存在' }}
        </button>
        <button 
          @click="openComparePage" 
          :disabled="!testUser1 || !testUser2"
          class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 disabled:opacity-50"
        >
          打开比较页面
        </button>
      </div>

      <!-- 可预测URL显示 -->
      <div v-if="predictableUrl" class="bg-gray-100 p-4 rounded">
        <h2 class="text-xl font-semibold mb-2">可预测的比较页面OG图片URL:</h2>
        <p class="font-mono text-sm break-all mb-2">{{ predictableUrl }}</p>
        <a :href="predictableUrl" target="_blank" class="text-blue-500 underline">
          在新窗口中打开图片
        </a>
      </div>

      <!-- 图片存在状态 -->
      <div v-if="imageStatus !== null" class="mt-4">
        <div :class="imageStatus ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'" class="p-3 rounded">
          {{ imageStatus ? '✅ 图片存在' : '❌ 图片不存在' }}
        </div>
      </div>

      <!-- 图片预览 -->
      <div v-if="predictableUrl && imageStatus" class="mt-6">
        <h2 class="text-xl font-semibold mb-2">图片预览:</h2>
        <img :src="predictableUrl" alt="Compare OG Image" class="border rounded max-w-full" />
      </div>

      <!-- API测试 -->
      <div class="mt-8 bg-blue-50 p-4 rounded">
        <h2 class="text-xl font-semibold mb-2">API测试:</h2>
        <div class="space-y-2">
          <button 
            @click="testSaveApi" 
            :disabled="!testUser1 || !testUser2"
            class="bg-orange-500 text-white px-3 py-1 rounded text-sm hover:bg-orange-600 disabled:opacity-50"
          >
            测试保存API
          </button>
          <button 
            @click="testGetApi" 
            :disabled="!testUser1 || !testUser2"
            class="bg-teal-500 text-white px-3 py-1 rounded text-sm hover:bg-teal-600 disabled:opacity-50"
          >
            测试获取API
          </button>
        </div>
        <div v-if="apiResult" class="mt-2 text-sm">
          <strong>API结果:</strong>
          <pre class="bg-white p-2 rounded mt-1 text-xs">{{ JSON.stringify(apiResult, null, 2) }}</pre>
        </div>
      </div>

      <!-- 说明文档 -->
      <div class="mt-8 bg-gray-50 p-4 rounded">
        <h2 class="text-lg font-semibold mb-2">功能说明:</h2>
        <ul class="text-sm space-y-1 list-disc list-inside">
          <li><strong>可预测URL:</strong> 根据用户名生成固定格式的S3图片URL</li>
          <li><strong>格式:</strong> github-compare-user1-vs-user2-latest.png</li>
          <li><strong>检查存在性:</strong> 验证图片是否已经在S3中存在</li>
          <li><strong>比较页面:</strong> 打开实际的比较页面，触发OG图片生成</li>
          <li><strong>API测试:</strong> 测试保存和获取OG图片URL的API端点</li>
        </ul>
      </div>

      <!-- 测试流程 -->
      <div class="mt-6 bg-yellow-50 p-4 rounded">
        <h2 class="text-lg font-semibold mb-2">测试流程:</h2>
        <ol class="text-sm space-y-1 list-decimal list-inside">
          <li>输入两个GitHub用户名（如：octocat, torvalds）</li>
          <li>点击"生成可预测URL"查看预期的图片URL</li>
          <li>点击"检查图片是否存在"验证图片状态</li>
          <li>如果图片不存在，点击"打开比较页面"触发生成</li>
          <li>等待比较分析完成，OG图片会自动生成并上传</li>
          <li>回到测试页面，再次检查图片应该已存在</li>
        </ol>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getPredictableCompareOgImageUrl, checkCompareOgImageExists } from '~/utils'

const testUser1 = ref('octocat')
const testUser2 = ref('torvalds')
const predictableUrl = ref('')
const imageStatus = ref<boolean | null>(null)
const isChecking = ref(false)
const apiResult = ref<any>(null)

const testPredictableUrl = () => {
  if (!testUser1.value.trim() || !testUser2.value.trim()) {
    alert('请输入两个GitHub用户名')
    return
  }

  predictableUrl.value = getPredictableCompareOgImageUrl(testUser1.value.trim(), testUser2.value.trim())
  imageStatus.value = null
  apiResult.value = null
}

const checkImageExists = async () => {
  if (!testUser1.value.trim() || !testUser2.value.trim()) return

  isChecking.value = true
  try {
    imageStatus.value = await checkCompareOgImageExists(testUser1.value.trim(), testUser2.value.trim())
  } catch (error) {
    console.error('检查图片失败:', error)
    imageStatus.value = false
  } finally {
    isChecking.value = false
  }
}

const openComparePage = () => {
  if (!testUser1.value.trim() || !testUser2.value.trim()) {
    alert('请输入两个GitHub用户名')
    return
  }

  const url = `/github/compare?user1=${testUser1.value.trim()}&user2=${testUser2.value.trim()}`
  window.open(url, '_blank')
}

const testSaveApi = async () => {
  try {
    const result = await $fetch('/api/github/save-compare-og-image', {
      method: 'POST',
      body: {
        user1: testUser1.value.trim(),
        user2: testUser2.value.trim(),
        ogImageUrl: 'https://example.com/test-image.png'
      }
    })
    apiResult.value = { type: 'save', result }
  } catch (error: any) {
    apiResult.value = { type: 'save', error: error.message }
  }
}

const testGetApi = async () => {
  try {
    const result = await $fetch(`/api/github/compare-og-image/${testUser1.value.trim()}-vs-${testUser2.value.trim()}`)
    apiResult.value = { type: 'get', result }
  } catch (error: any) {
    apiResult.value = { type: 'get', error: error.message }
  }
}

// 设置页面meta
useSeoMeta({
  title: 'GitHub比较页面OG图片测试 - DINQ',
  description: '测试GitHub比较页面的OG图片生成功能',
})
</script>
