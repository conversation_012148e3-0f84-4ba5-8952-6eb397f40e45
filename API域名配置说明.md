# API域名配置说明

本项目支持在构建时配置自定义API域名，以便在不同环境中部署。

## 环境变量

项目使用以下环境变量来配置API域名：

- `NUXT_PUBLIC_API_BASE`: API的基础URL，默认为 `https://api.dinq.io`

## 构建方法

### 使用默认API域名构建

```bash
# 使用默认API域名 (https://api.dinq.io)
./build.sh
```

### 使用自定义API域名构建

```bash
# 使用自定义API域名
./build.sh http://your-custom-api-domain.com
```

例如，使用测试环境API：

```bash
./build.sh http://qingke.aihe.space
```

## 配置文件说明

项目中的以下文件已经配置为使用环境变量中的API域名：

1. `nuxt.config.ts`: 配置了API代理和运行时配置
   - 设置了 `runtimeConfig.public.apiBase` 以在客户端和服务器端共享API基础URL
   - 配置了 `nitro.routeRules` 以处理API请求代理
   - 使用 `vite.define` 将环境变量暴露给客户端

2. `utils/request.ts`: 配置了API请求客户端
   - 在请求时动态获取运行时配置
   - 根据配置修改请求的 `baseURL`
   - 添加固定的 `userid` 头部用于API认证绕过

3. `composables/useEventStream.ts`: 配置了事件流连接
   - 使用相对路径，让Nuxt处理代理
   - 通过Nuxt的代理系统将请求转发到正确的API域名

## 工作原理

1. 在构建时，环境变量 `NUXT_PUBLIC_API_BASE` 被设置为指定的API域名
2. 这个值被注入到Nuxt的运行时配置中，可以通过 `useRuntimeConfig()` 访问
3. 在客户端请求时，`utils/request.ts` 中的 `onRequest` 钩子会检查运行时配置，并修改请求的 `baseURL`
4. 这样，所有API请求都会发送到指定的API域名

## 部署说明

构建完成后，静态文件将位于 `.output/public` 目录中。您可以将这些文件部署到任何静态文件托管服务，如Cloudflare Pages、Vercel、Netlify等。

## 注意事项

1. 所有API请求都会自动添加 `userid: gAckWxWYazcI5k95n627hRBHB712` 头部以绕过认证
2. 如果部署到子目录，需要在 `nuxt.config.ts` 中设置 `app.baseURL` 属性
3. 确保API服务器允许跨域请求（CORS）
4. 预渲染已被禁用，以避免在构建时访问API导致的问题
