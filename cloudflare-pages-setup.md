# Cloudflare Pages 自动部署设置指南

本文档将指导你如何设置 Cloudflare Pages 以自动构建和部署你的 Nuxt 项目。

## 前提条件

1. 一个 Cloudflare 账户
2. 一个包含你项目的 GitHub 仓库
3. 仓库中已包含 `cloudflare-pages.toml` 配置文件

## 步骤 1: 连接 GitHub 仓库

1. 登录到 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. 在左侧菜单中点击 **Pages**
3. 点击 **创建应用程序** 按钮
4. 选择 **连接到 Git**
5. 选择 **GitHub** 作为你的 Git 提供商
6. 授权 Cloudflare 访问你的 GitHub 账户
7. 选择包含你项目的仓库

## 步骤 2: 配置构建设置

1. 设置项目名称（这将成为你的 `*.pages.dev` 子域名的一部分）
2. 在 **构建设置** 部分：
   - 生产分支: `main` 或 `nuxt`（取决于你的主分支名称）
   - 构建命令: `npm run build`
   - 构建输出目录: `.output/public`
   - 根目录: `/` (如果你的项目在仓库根目录)

3. 环境变量（如果需要）:
   - 点击 **环境变量** 标签
   - 添加以下环境变量:
     - `NODE_VERSION`: `18`

## 步骤 3: 部署设置

1. 点击 **保存并部署**
2. Cloudflare 将开始构建和部署你的项目
3. 构建完成后，你将获得一个 `*.pages.dev` 的 URL

## 步骤 4: 自定义域名（可选）

1. 在项目页面中，点击 **自定义域** 标签
2. 点击 **设置自定义域**
3. 输入你想要使用的域名
4. 按照说明配置 DNS 记录

## 自动部署

现在，每当你推送代码到配置的分支（例如 `main` 或 `nuxt`），Cloudflare Pages 将自动:

1. 检测到代码变更
2. 启动新的构建
3. 部署更新后的网站

## 高级配置

### 预览部署

默认情况下，Cloudflare Pages 会为每个 Pull Request 创建预览部署。你可以在 Cloudflare Dashboard 中的项目设置中禁用此功能。

### 构建缓存

Cloudflare Pages 会自动缓存 `node_modules` 目录以加速后续构建。

### 环境变量

你可以为生产环境和预览环境设置不同的环境变量。

## 故障排除

如果构建失败，你可以在 Cloudflare Dashboard 中查看构建日志以诊断问题。常见问题包括:

1. 依赖安装失败
2. 构建命令错误
3. 内存限制（Cloudflare Pages 有 1GB 内存限制）

## 其他资源

- [Cloudflare Pages 文档](https://developers.cloudflare.com/pages/)
- [Nuxt 部署指南](https://nuxt.com/docs/getting-started/deployment)
