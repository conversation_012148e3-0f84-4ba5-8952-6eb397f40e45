import { ref } from 'vue';
import { signInWithPopup, signOut, onAuthStateChanged, type User, setPersistence, browserLocalPersistence } from 'firebase/auth';
import { googleProvider, githubProvider, createOptimizedGoogleProvider, isSafari } from '@/utils/authProviders';

// 定义可序列化的用户类型
interface SerializableUser {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
}

// 转换 Firebase User 为可序列化对象
const convertToSerializable = (user: User | null): SerializableUser | null => {
  if (!user) return null;
  return {
    uid: user.uid,
    email: user.email,
    displayName: user.displayName,
    photoURL: user.photoURL,
  };
};

// 跟踪全局监听器状态
let unsubscribe: (() => void) | null = null;
// 跟踪初始化状态
let isInitialized = false;

export const useFirebaseAuth = () => {
  const nuxtApp = useNuxtApp();
  const auth = nuxtApp.$auth;

  // 使用 useState 创建组件级状态
  const currentUser = useState<SerializableUser | null>('firebase-user', () => null);
  const loading = ref(false);
  const error = ref<string | null>(null);
  // 添加认证初始化状态
  const authInitialized = useState<boolean>('firebase-auth-initialized', () => false);

  // 只在客户端初始化一次监听
  if (process.client && !unsubscribe && auth && !isInitialized) {
    isInitialized = true;
    
    // 设置持久化登录
    setPersistence(auth, browserLocalPersistence)
      .then(() => {
        console.log('Auth persistence set to LOCAL');
      })
      .catch((err) => {
        console.error('Error setting auth persistence:', err);
      });
    
    // 设置认证状态监听 - 这是获取认证状态的唯一可靠方式
    unsubscribe = onAuthStateChanged(auth, (user) => {
      console.log('Auth state changed:', user ? 'User logged in' : 'User logged out');
      // 无论如何都更新状态，包括从 null 到 user 或从 user 到 null
      currentUser.value = convertToSerializable(user);
      // 标记认证已初始化
      authInitialized.value = true;
    });

    // 确保在页面离开时清理
    if (process.client) {
      window.addEventListener('beforeunload', () => {
        if (unsubscribe) {
          unsubscribe();
          unsubscribe = null;
          isInitialized = false;
        }
      });
    }
  }

  const loginWithGoogle = async () => {
    if (!auth) {
      console.error('Auth is not initialized');
      return;
    }

    loading.value = true;
    try {
      // 使用优化的provider，针对不同浏览器进行配置
      const provider = createOptimizedGoogleProvider();
      
      console.log(`Logging in with Google on ${isSafari() ? 'Safari' : 'other browser'}`);
      
      const result = await signInWithPopup(auth, provider);
      currentUser.value = convertToSerializable(result.user);
      error.value = null;
      return convertToSerializable(result.user);
    } catch (err: any) {
      console.error('Google login error:', err);
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const loginWithGithub = async () => {
    if (!auth) {
      console.error('Auth is not initialized');
      return;
    }

    loading.value = true;
    try {
      const result = await signInWithPopup(auth, githubProvider);
      currentUser.value = convertToSerializable(result.user);
      error.value = null;
      return convertToSerializable(result.user);
    } catch (err: any) {
      console.error('Github login error:', err);
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const logout = async () => {
    if (!auth) {
      console.error('Auth is not initialized');
      return;
    }

    try {
      await signOut(auth);
      currentUser.value = null;
    } catch (err: any) {
      console.error('Logout error:', err);
      error.value = err.message;
      throw err;
    }
  };

  // 获取原始 Firebase User 对象（仅在需要时使用）
  const getRawUser = () => auth?.currentUser || null;

  return {
    currentUser,
    authInitialized,
    loading,
    error,
    loginWithGoogle,
    loginWithGithub,
    logout,
    getRawUser,
  };
};
