import { ref, onUnmounted } from 'vue'

export const useEventStream = () => {
  // 获取运行时配置
  const getApiBaseUrl = () => {
    try {
      const runtimeConfig = useRuntimeConfig()
      return runtimeConfig.public.apiBase || 'https://api.dinq.io'
    } catch (error) {
      console.warn('Could not access runtime config, using default API base URL')
      return 'https://api.dinq.io'
    }
  }
  const thinking = ref<string[]>([])
  const finalResponse = ref<string>('')
  const reportDataInfo = ref<any>(null);
  const pkState = ref<any>(null);
  const pkData = ref<any>(null);
  const error = ref<Error | null>(null)
  const readerRef = ref<ReadableStreamDefaultReader | null>(null)
  const loading = ref(true)
  const limitInfo = ref<any>(null)
  const processMessage = (data: any) => {
    try {
      switch (data.type) {
        case 'thinkTitle':
          thinking.value = [...thinking.value, data.content]
          break

        case 'thinkContent':
          thinking.value = [...thinking.value, `  ${data.content}`]
          break

        case 'finalContent':
          finalResponse.value = data.content
          loading.value  = false;
          break

        case 'reportData':
          reportDataInfo.value = data.content
          // fetchReportData(data.content.jsonUrl)
          break
        case 'pkState':
          pkState.value = data.state;
          break;
        case 'pkData':
          // 处理SSE流中的数据结构
          if (data.content && data.content.data && data.content.data.user1 && data.content.data.user2) {
            pkData.value = data.content.data;
          } else if (data.content && data.content.user1 && data.content.user2) {
            pkData.value = data.content;
          } else {
            pkData.value = data.content;
          }
          // 接收到pkData后，结束加载状态
          loading.value = false;
          break;
        case 'completion':
          loading.value = false
          break
        case 'status':
          // Update status (optional)
          break

        case 'error':
          throw new Error(data.content)
      }
    } catch (e) {
      console.error('Error processing message:', e)
      error.value = e as Error
    }
  }

  const connect = async (query: string, SERVER_URL: string, headers: object) => {
    try {
      // 处理SERVER_URL，确保使用正确的域名
      let fullUrl = SERVER_URL

      // 如果SERVER_URL是相对路径（以/开头），则添加域名
      if (SERVER_URL.startsWith('/')) {
        const apiBaseUrl = getApiBaseUrl()
        fullUrl = `${apiBaseUrl}${SERVER_URL}`
      }

      console.log(`Connecting to: ${fullUrl}`)

      const response = await fetch(fullUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...headers
        },
        body: JSON.stringify({ query }),
      })

      if (!response.ok) {
        if (response.status === 429) {
          // 限流，解析详细信息
          const errJson = await response.json();
          const err = new Error(errJson.message || 'Usage limit exceeded');
          limitInfo.value = { error: errJson, errorType: 'limit' };
          throw err;
        } else {
          throw new Error(`Server responded with status: ${response.status}`);
        }
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('Failed to get response reader')
      }

      readerRef.value = reader
      const decoder = new TextDecoder()
      let buffer = ''

      const readStream = async () => {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          buffer += decoder.decode(value, { stream: true })

          const messages = buffer.split('\n\n')
          buffer = messages.pop() || ''

          for (const message of messages) {
            try {
              // 解析事件流消息
              let jsonData = ''

              message.split('\n').forEach(line => {
                // 我们只关心数据部分，事件类型暂时不使用
                // if (line.startsWith('event:')) {
                //   const eventType = line.substring(7).trim()
                //   console.log('Event type:', eventType)
                // } else
                if (line.startsWith('data:')) {
                  jsonData = line.substring(5).trim()
                }
              })

              if (jsonData) {
                const data = JSON.parse(jsonData)
                processMessage(data)
              }
            } catch (e) {
              console.error('Error parsing SSE message:', e)
              error.value = e as Error
            }
          }
        }
      }

      readStream()
    } catch (err) {
      console.log('Error connecting to server:', err)
      error.value = err as Error
      limitInfo.value = { error: err, errorType: 'limit' };
      loading.value = false;
    }
  }

  const connectWithObj = async (params: object, SERVER_URL: string, headers: object) => {
    try {
      // 处理SERVER_URL，确保使用正确的域名
      let fullUrl = SERVER_URL

      // 如果SERVER_URL是相对路径（以/开头），则添加域名
      if (SERVER_URL.startsWith('/')) {
        const apiBaseUrl = getApiBaseUrl()
        fullUrl = `${apiBaseUrl}${SERVER_URL}`
      }

      const response = await fetch(fullUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...headers
        },
        body: JSON.stringify({ ...params }),
      })

      if (!response.ok) {
        if (response.status === 429) {
          // 限流，解析详细信息
          const errJson = await response.json();
          const err = new Error(errJson.message || 'Usage limit exceeded');
          (err as any).limitInfo = errJson.limit_info;
          (err as any).errorType = errJson.error;
          throw err;
        } else {
          throw new Error(`Server responded with status: ${response.status}`);
        }
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('Failed to get response reader')
      }

      readerRef.value = reader
      const decoder = new TextDecoder()
      let buffer = ''

      const readStream = async () => {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          buffer += decoder.decode(value, { stream: true })

          const messages = buffer.split('\n\n')
          buffer = messages.pop() || ''

          for (const message of messages) {
            try {
              // 解析事件流消息
              let jsonData = ''

              message.split('\n').forEach(line => {
                // 我们只关心数据部分，事件类型暂时不使用
                // if (line.startsWith('event:')) {
                //   const eventType = line.substring(7).trim()
                //   console.log('Event type:', eventType)
                // } else
                if (line.startsWith('data:')) {
                  jsonData = line.substring(5).trim()
                }
              })

              if (jsonData) {
                const data = JSON.parse(jsonData)
                processMessage(data)
              }
            } catch (e) {
              console.error('Error parsing SSE message:', e)
              error.value = e as Error
            }
          }
        }
      }

      readStream()
    } catch (err) {
      error.value = err as Error
      console.log('Error connecting to server:', err)
      limitInfo.value = { error: err, errorType: 'limit' };
      loading.value = false;
    }
  }

  const close = () => {
    if (readerRef.value) {
      readerRef.value.cancel()
      readerRef.value = null
    }
  }

  onUnmounted(() => {
    close()
  })

  return {
    loading,
    thinking,
    finalResponse,
    reportDataInfo,
    pkData,
    pkState,
    error,
    limitInfo,
    connect,
    connectWithObj,
    close,
  }
}
