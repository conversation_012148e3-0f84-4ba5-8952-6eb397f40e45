#!/usr/bin/env node

/**
 * Meta 标签测试工具
 * 用于验证 SSR 页面的 meta 标签是否正确生成
 */

import { execSync } from 'child_process'
import fs from 'fs'

console.log('🔍 测试 Meta 标签生成...\n')

// 测试 URL 列表
const testUrls = [
  '/github?query=octocat',
  '/github/compare?user1=octocat&user2=defunkt',
  '/compare?researcher1=John%20Doe&researcher2=Jane%20Smith'
]

console.log('📝 测试说明:')
console.log('1. 构建项目以生成 SSR 版本')
console.log('2. 检查关键 meta 标签是否存在')
console.log('3. 验证 Twitter Card 和 Open Graph 标签\n')

// 检查构建是否存在
const distPath = '.nuxt/dist'
if (!fs.existsSync(distPath)) {
  console.log('❌ 构建文件不存在，请先运行: npm run build')
  process.exit(1)
}

console.log('✅ 构建文件存在')

// 检查关键文件
const serverPath = `${distPath}/server`
const clientPath = `${distPath}/client`

if (fs.existsSync(serverPath)) {
  console.log('✅ 服务端文件已生成')
} else {
  console.log('❌ 服务端文件未找到')
}

if (fs.existsSync(clientPath)) {
  console.log('✅ 客户端文件已生成')
} else {
  console.log('❌ 客户端文件未找到')
}

console.log('\n🚀 Meta 标签验证建议:')
console.log('\n1. 使用 Twitter Card Validator:')
console.log('   https://cards-dev.twitter.com/validator')
console.log('   输入您的页面 URL 进行验证')

console.log('\n2. 使用 Facebook Sharing Debugger:')
console.log('   https://developers.facebook.com/tools/debug/')
console.log('   检查 Open Graph 标签')

console.log('\n3. 使用 curl 测试服务端渲染:')
testUrls.forEach(url => {
  console.log(`   curl -H "User-Agent: Twitterbot/1.0" https://your-domain.com${url}`)
})

console.log('\n4. 检查关键 meta 标签:')
console.log('   - <meta property="og:title" content="..." />')
console.log('   - <meta property="og:description" content="..." />')
console.log('   - <meta property="og:image" content="..." />')
console.log('   - <meta name="twitter:card" content="summary_large_image" />')
console.log('   - <meta name="twitter:title" content="..." />')

console.log('\n💡 故障排除:')
console.log('1. 确保页面是 SSR 而不是客户端渲染')
console.log('2. 检查 meta 标签是否在 <head> 中正确生成')
console.log('3. 验证 Cloudflare Pages 是否正确部署了 SSR 功能')
console.log('4. 清除 Twitter/Facebook 的缓存（可能需要等待几分钟）')

console.log('\n🎯 下一步:')
console.log('1. 部署到 Cloudflare Pages')
console.log('2. 使用上述工具验证线上版本的 meta 标签')
console.log('3. 如果仍有问题，检查 Cloudflare Pages 的 SSR 配置')

console.log('\n✨ 测试完成!')
